import weeeTest

from wms.test_dir.api.wms.wms import wms


class TestFpoRegion(weeeTest.TestCase):
    """
    订单下发相关接口
    """

    # @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_fpo_region(self):
        """
        从so获取近两个小时的订单
        """
        # /central_inventory/so/order/downByCreateTime?startTime=?&endTime=?
        wms.login()
        data = wms.down_order.fpo_region_by_warehouse(warehouse_number=25, delivery_date='2023-07-19')
        assert data['success'] == True
        data = wms.down_order.fpo_region_by_warehouse(warehouse_number=25, delivery_date='123')
        assert len(data["body"]) == 0
        data = wms.down_order.fpo_region_by_warehouse(warehouse_number="25", delivery_date='2023-07-19')
        assert len(data["body"]) != 0

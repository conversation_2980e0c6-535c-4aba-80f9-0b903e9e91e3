import weeeTest
from wms.qa_config import global_data
from weeeTest import jmespath

from wms.test_dir.api.wms.wms import wms


class CountApi(weeeTest.TestCase):

    def get_bin_task(self, edit_user):
        """
		领取bin库位的任务
		"""
        warehouse_number = global_data.get_count_bin['warehouse_number']
        storage_type = global_data.get_count_bin['storage_type']
        location_type = global_data.get_count_bin['location_type']
        status = global_data.get_count_bin['status']
        module_name = global_data.get_count_bin['module_name']
        # 领取任务
        weeeTest.log.info('领取任务开始............................')
        resp = wms.count.count_get_bin_task(warehouse_number, edit_user, storage_type, location_type, status, module_name)
        assert resp['success'] is True
        weeeTest.log.info('盘点任务领取成功！！！')

    def get_stock_task(self, edit_user):
        """
		领取Stock库位的任务
		"""
        warehouse_number = global_data.get_count_stock['warehouse_number']
        storage_type = global_data.get_count_stock['storage_type']
        location_type = global_data.get_count_stock['location_type']
        status = global_data.get_count_stock['status']
        module_name = global_data.get_count_stock['module_name']
        restock_type = global_data.get_count_stock['restock_type']
        # 领取任务
        weeeTest.log.info('领取任务开始............................')
        resp = wms.count.count_get_stock_task(warehouse_number, edit_user, storage_type, location_type, status,
                                              module_name, restock_type)
        assert resp['success'] is True
        weeeTest.log.info('盘点任务领取成功！！！')

    def release_task(self, edit_user):
        """
		盘点任务释放
		"""
        # 调用释放接口
        resp = wms.count.count_release_task(edit_user)
        assert resp['success'] is True
        weeeTest.log.info('盘点任务释放成功！！！')

    def get_count_data(self, warehouse_number, edit_user, storage_type, location_type, status, module_name):
        """
		bin库位盘点参数获取
		"""
        # 进行盘点
        resp = wms.count.count_task_list(warehouse_number, edit_user, storage_type, location_type, status, module_name)
        assert resp['success'] is True
        count_id = jmespath(resp, "body[0].rec_id")  # 获取info_id
        location_no = jmespath(resp, "body[0].location_no")  # 获取location_no
        status = jmespath(resp, "body[0].status")  # 获取status
        weeeTest.log.info(f'盘点参数：count_id: {count_id}, location_no:{location_no},status: {status}')
        return count_id, location_no, status

    def get_location_detail(self, count_id, status, module_name):
        """
		获取库位盘点明细
		"""
        resp1 = wms.count.scan_location(count_id, status, module_name)
        sku_list = len(resp1["body"])
        return sku_list, resp1

    def get_sku_detail(self, resp1, location_no, warehouse_number):
        """
		获取库位商品明细
		"""
        item_list = []
        is_lpn_list = []
        for item in resp1["body"]:
            item_number = item["item_number"]
            rec_id = item["rec_id"]
            resp2 = wms.count.goods(location_no, warehouse_number, item_number, rec_id)
            is_lpn = jmespath(resp2, "body.lpnList")
            item_list.append(item_number)
            is_lpn_list.append(is_lpn)
        return item_list, is_lpn_list

    def count_bin_upc(self, item_number, warehouse_number, location_no, diff_qty, in_user, status, module_name):
        """
		bin位confirm
		"""
        # UPC盘点
        qty = wms.wms_db.get_quantity_and_allocated_qty_sum(item_number, warehouse_number, location_no)
        sys_qty = qty[0] or 0  # 判断sys_qty是否为空，为空则赋值为0
        input_qty = int(sys_qty) + int(diff_qty)  # 实际盘点数量
        wms.count.confirm_num(location_no, warehouse_number, item_number, input_qty, in_user, status, module_name)

    def count_stock_lpn(self, is_lpn, warehouse_number, location_no, item_number, in_user, status, module_name):
        """
		Stock位confirm
		"""
        # LPN盘点
        lpn_list = []  # 拼接confirm的LPN list
        for lpn in is_lpn:
            new_lpn_list = {
                "lpn_no": lpn["lpn_no"],
                "expired_dtm_str": None,
                "pieces_per_pack": lpn["pieces_per_pack"],
                "inputQty": lpn["lpn_quantity"],
                "warehouse_number": lpn["warehouse_number"],
                "item_number": lpn["item_number"],
                "location_no": lpn["location_no"],
                "lot_code": lpn["lot_code"],
                "date_type": lpn["date_type"]
            }
            lpn_list.append(new_lpn_list)
        # 调用confirm接口
        wms.count.lpn_confirm(warehouse_number, location_no, item_number, in_user, status, module_name, lpn_list)

    def count_finish(self, count_id, in_user, sku_list, module_name, status):
        """
		盘点finish
		"""
        # finish接口
        wms.count.count_finish(count_id, in_user, sku_list, module_name, status)

    def count_checks(self, count_id, status, user_id, warehouse_number):
        """
		查询盘点状态是否完成
		"""
        sql_status = wms.wms_db.get_status_from_wh_count_info(count_id)
        sql_s = sql_status["status"]
        if sql_s == 1:
            weeeTest.log.info('一盘完成,待二盘......')
            # 更新盘点数据,继续二盘
            status = status + 1
            user_id = user_id + 'test1'
            edit_user = 'edit_user' + str(status)
            # 更新盘点user,继续下一盘
            wms.wms_db.update_count_user(count_id, user_id, edit_user)
            # 分配任务
            wms.count.assign_count_task(warehouse_number, count_id, user_id)
            weeeTest.log.info(f'进行{status + 1}盘, 更新{edit_user}:{user_id},count_id: {count_id}')
        if sql_s == 2:
            weeeTest.log.info('二盘完成,待三盘......')
            # 更新盘点数据,继续三盘
            status = status + 1
            user_id = user_id + 'test2'
            edit_user = 'edit_user' + str(status)
            # 更新盘点user,继续下一盘
            wms.wms_db.update_count_user(count_id, user_id, edit_user)
            # 分配任务
            wms.count.assign_count_task(warehouse_number, count_id, user_id)
            weeeTest.log.info(f'进行{status + 1}盘, 更新{edit_user}:{user_id},count_id: {count_id}')
        if sql_s == 4:
            weeeTest.log.info(f'count_id:{count_id},盘点完成！！！')
            # 审核表查询盘点审核记录
            resp = wms.wms_db.get_approval_count(count_id)
            assert resp is not None, "盘点完成没审核记录"

        return sql_s, status

    def count_empty(self, count_id, in_user, status, module_name):
        """
		empty盘点
		"""
        # 没有SKU,走empty流程
        wms.count.empty_scan_location(count_id, in_user, status, module_name)
        # 查询盘点状态
        sql_status = wms.wms_db.get_status_from_wh_count_info(count_id)
        sql_s = sql_status["status"]
        weeeTest.log.info(f'empty后盘点任务:{count_id}状态:{sql_s}')


if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True, open=True)

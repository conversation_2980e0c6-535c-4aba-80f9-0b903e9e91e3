# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON> chen
@Version        :  V1.0.0
------------------------------------
@File           :  test_normal_picking.py
@Description    :
@CreateTime     :  2023/6/6 10:04
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/6/6 10:04
"""
import logging
import time,json,copy

import weeeTest
from weeeTest import jmespath

from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms
from datetime import datetime


class TestPicking(weeeTest.TestCase):

    #@weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_autostore_picking(self):
        """
        【112956】AS Picking 流程
        """

        warehouse_number = global_data.as_picking['warehouse_number']
        storage_type = global_data.as_picking['storage_type']

        # 登录
        user_id, user_name  = wms.wms_login.common_login()

        # 查询可用的cart
        location_no = wms.wms_db.get_wh_storage_location_info(warehouse=warehouse_number, location_type=50, flag=0,
                                                              info='location_no')
        #指定订单 20241116
        order_id = 242548069

        #查询订单状态
        res1 = wms.wms_db.get_order_info(order_id=order_id,info=['shipping_status','delivery_dtm'])
        shipping_status = res1['shipping_status']
        delivery_dtm = res1['delivery_dtm']

        #预占AS完成 shipping_status 25
        if shipping_status ==25:

            #获取Pick List id
            res2 = wms.wms_db.get_order_as_task(order_id=order_id,info=["rec_id","pick_list_rec_id","item_number","status"])
            pick_list_id = res2[0]["pick_list_rec_id"]
            pisk_task_status = res2[0]["status"]

            #获取task_detail表id，item
            resp3 = wms.wms_db.get_order_as_task_detail(pick_list_id=pick_list_id,info=['rec_id','item_number','batch_no','receive_dtm','expire_dtm','quantity'])

            #发送校验，取消待发送订单前一发货日Dry MO订单
            before_delivery_dtm = delivery_dtm-86400
            wms.wms_db.update_order(order_status=2,delivery_dtm=before_delivery_dtm,warehouse_number=warehouse_number)

            #更新AutoStoreConfig开关、起始日期
            #start_dtm = datetime.utcfromtimestamp(delivery_dtm)
            #formatted_date = start_dtm.strftime('%Y-%m-%d')

            start_dtm = wms.util.utc_to_day(delivery_dtm)
            config_value = f"{{\"active\": true, \"agingOrderMaxTime\": 600, \"oneItemOneOrderVolumeLimit\": 1000, \"oneItemVolumeMaxLimit\": 2500, \"oneItemVolumeMinLimit\": 1, \"pickCartConfigList\": [{{\"count\": 4, \"size\": 10, \"weight\": 10}}, {{\"count\": 2, \"size\": 15, \"weight\": 15}}, {{\"size\": 25, \"count\": 0, \"weight\": 25}}, {{\"size\": 30, \"count\": 0, \"weight\": 30}}], \"startDeliveryDate\": \"{start_dtm}\", \"supplier\": \"AutoStore\", \"sendDetailIdDeliveryDate\": \"2000-11-29\", \"supportOneItem\": false, \"oosLockInventory\": true, \"sendSkipWaveIds\": [], \"cancelOrderSetKpiComplete\": false, \"restockCallActive\": true, \"mailOrderSupportOneItem\": true, \"mailOrderOneItemOneOrderVolumeLimit\": 1000, \"asItemCanMarkCondition\": {{\"max_sku_weight_lb\": 10, \"max_skuBox_weight_lb\": 54, \"max_skuBox_volume_in3\": 1000}}}}"
            wms.update_central_config(warehouse_number=warehouse_number,
                                      config_key="support_automation",config_value=config_value)
            #wms.wms_db.update_autostore_config(start_dtm=delivery_dtm,warehouse_number=warehouse_number)

            #发送Pick list
            wms.autostore_picking.autoStoreJob()
            time.sleep(5)
            resp4 = wms.wms_db.get_order_as_list(pick_list_id=pick_list_id,info=["rec_id","status","list_type"])
            pisk_list_status = resp4[0]["status"]
            pick_list_type = resp4[0]["list_type"]
            assert pisk_list_status == 10,"Pick List发送KPI失败"


            #回调-绑定Container-参数整理
            # 给定的 JSON 数据模板
            json_data_template = {
                "version": "2.0.0",
                "action": "PickResult",
                "timestamp": "2024-12-22T02:58:13-08:00",
                "messageId": "388563",
                "instanceId": "SayWeee",
                "params": {
                    "pickListId": pick_list_id,
                    "containerComplete": False,
                    "outboundContainerId": None,
                    "createdBy": "HOST",
                    "hostCreated": True,
                    "listComplete": False,
                    "pickTaskInformation": [
                        {
                            "pickTaskId": None,
                            "pickTaskComplete": True,
                            "itemId": None,
                            "clientId": "TB1",
                            "pickTaskCompleteCode": "PICKED",
                            "compartmentInformation": [
                                {
                                    "binId": "AS17172",
                                    "compartmentId": "A1",
                                    "fifoDate": None,
                                    "expiryDate": None,
                                    "group1": None,
                                    "quantity": None,
                                    "portId": 14,
                                    "portType": "CAROUSEL",
                                    "startDatetime": "2024-12-22T02:57:13-08:00",
                                    "endDatetime": "2024-12-22T02:58:13-08:00",
                                    "userId": "7875714",
                                    "sortBarPositionId": "B1"
                                }
                            ]
                        }
                    ]
                }
            }

            # 给定的列表数据
            data_list = resp3

            # 创建一个空的 JSON 数据列表
            json_data_list = []

            # 遍历列表数据，将每个元素的 rec_id 和 item_number 带入 JSON 数据模板中
            for data in data_list:
                # 深拷贝模板，以免修改原始模板
                current_json_data = copy.deepcopy(json_data_template)

                # 更新 JSON 数据中的 rec_id 和 item_number

                current_json_data['params']['pickTaskInformation'][0]['pickTaskId'] = data['rec_id']
                current_json_data['params']['pickTaskInformation'][0]['itemId'] = data['item_number']
                current_json_data['params']['pickTaskInformation'][0]['compartmentInformation'][0]['fifoDate'] = datetime.utcfromtimestamp(data['receive_dtm']).strftime('%Y-%m-%d')
                current_json_data['params']['pickTaskInformation'][0]['compartmentInformation'][0]['expiryDate'] = datetime.utcfromtimestamp(data['expire_dtm']).strftime('%Y-%m-%d')
                current_json_data['params']['pickTaskInformation'][0]['compartmentInformation'][0]['group1'] = data['batch_no']
                current_json_data['params']['pickTaskInformation'][0]['compartmentInformation'][0]['quantity'] = data['quantity']

                # 将更新后的 JSON 数据添加到列表中
                json_data_list.append(current_json_data)
                new_json_data_list = json.dumps(json_data_list, indent=4)
            #回调绑定Container-回调
            for json_data in json_data_list:
                wms.autostore_picking.autoStoreApi(data=json_data)
                time.sleep(3)

            container_id = wms.wms_db.query_chute(warehouse_number=warehouse_number)['chute_id']

            #回调-PickResult-参数整理
            json_data_template1 = {
                  "version": "2.0.0",
                  "action": "PickResult",
                  "timestamp": "2024-12-22T02:58:34-08:00",
                  "messageId": "388564",
                  "instanceId": "SayWeee",
                  "params": {
                    "pickListId": pick_list_id,
                    "containerComplete": True,
                    "outboundContainerId": container_id,
                    "createdBy": "HOST",
                    "hostCreated": True,
                    "listComplete": False,
                    "pickTaskInformation": [
                      {
                        "pickTaskId": None,
                        "pickTaskComplete": True,
                        "itemId": None,
                        "clientId": "TB1",
                        "pickTaskCompleteCode": "PICKED",
                        "compartmentInformation": [
                          {
                            "binId": "",
                            "compartmentId": "",
                            "quantity": None,
                            "portId": 14,
                            "portType": "CAROUSEL",
                            "startDatetime": "2024-12-22T02:58:34-08:00",
                            "endDatetime": "2024-12-22T02:58:34-08:00",
                            "userId": "7875714",
                            "sortBarPositionId": "B1"
                          }
                        ]
                      }
                    ]
                  }
                }

            # 给定的列表数据
            data_list1 = resp3

            # 创建一个空的 JSON 数据列表
            json_data_list1 = []

            # 遍历列表数据，将每个元素的 rec_id 和 item_number 带入 JSON 数据模板中
            for data in data_list1:
                # 深拷贝模板，以免修改原始模板
                current_json_data1 = copy.deepcopy(json_data_template1)

                # 更新 JSON 数据中的 rec_id 和 item_number

                current_json_data1['params']['pickTaskInformation'][0]['pickTaskId'] = data['rec_id']
                current_json_data1['params']['pickTaskInformation'][0]['itemId'] = data['item_number']
                current_json_data1['params']['pickTaskInformation'][0]['compartmentInformation'][0]['quantity'] = data['quantity']

                # 将更新后的 JSON 数据添加到列表中
                json_data_list1.append(current_json_data1)
                new_json_data_list1 = json.dumps(json_data_list1, indent=4)
                # for json_data in json_data_list:
                #     print(json.dumps(json_data, indent=4))

            # 回调绑定Container-PickResult
            for json_data1 in json_data_list1:
                wms.autostore_picking.autoStoreApi(data=json_data1)
                time.sleep(3)


            #Pick Complete
            wms.autostore_picking.autoStoreApi(data={
                  "version": "2.0.0",
                  "action": "ListActivity",
                  "timestamp": "2024-12-22T03:02:01-08:00",
                  "messageId": "388568",
                  "instanceId": "SayWeee",
                  "params": {
                    "listId": pick_list_id,
                    "listType": "PICK",
                    "status": "COMPLETE",
                    "userId": "7875714",
                    "operationDatetime": "2024-12-22T03:02:01-08:00",
                    "portType": "CAROUSEL",
                    "portId": "14"
                  }
                } )


            # wms拣货
            #AS Normal Picking
            if pick_list_type == 1:

                # 查询可用的M cart
                location_no = wms.wms_db.get_wh_storage_location_info(warehouse=warehouse_number, location_type=50,flag=0,info='location_no')

                # 创建拣货任务
                resp5 = wms.autostore_picking.create_picking_task(cart_no=location_no, warehouse_number=warehouse_number,storage_type=1)
                box_size = resp5['body']['box_size']
                picking_orders = resp5["body"]["picking_order"]

                for order in picking_orders:
                    rec_id = order["rec_id"]
                    order_id = order["order_id"]
                    picking_task_id = order["picking_task_id"]
                    print(f"rec_id: {rec_id}, order_id: {order_id}, picking_task_id: {picking_task_id}")

                    # 查询可用的tote
                    tote_nos = wms.wms_db.select_available_tote(location_type=6, flag=0, warehouse=warehouse_number,
                                                                box_size=box_size)
                    tote_no = tote_nos[0]['location_no']

                    response = wms.autostore_picking.bind_tote_number(
                        picking_task_id=picking_task_id,
                        tote_no=tote_no,
                        order_id=order_id,
                        shipping_type=None,
                        picking_order_rec_id=rec_id,
                        warehouse_number=warehouse_number
                    )
                    time.sleep(3)
                    print(response['body'].get('nextOrder'))
                    # 依次绑定tote，直至分配可拣订单，获取chute_id
                    if response['body'].get('nextOrder') is not None:
                        chute_id = response['body']['nextOrder'].get('chute_id')
                        order_tote_no = response['body']['nextOrder'].get('tote_no')
                        new_order_id = response['body']['nextOrder'].get('order_id')
                        print(f"Chute ID: {chute_id}")
                        print(f"Tote No: {order_tote_no}")
                        print(f"order_id: {new_order_id}")
                        break

                    # 扫描Chute
                time.sleep(3)
                wms.autostore_picking.updateDestinationStatus(warehouseNumber=warehouse_number,
                                                              moduleName="autostore_picking",
                                                              destinationId=chute_id, destinationStatus="Empty")

                # Adjust
                time.sleep(5)
                timestamp = int(time.time())
                wms.autostore_picking.adjustLocation(warehouse_number=warehouse_number, tote_no=order_tote_no,
                                                     chute_id=chute_id, order_id=new_order_id,
                                                     picking_type=11, picking_task_id=picking_task_id, long_press=0,
                                                     scanLocationDtm=timestamp,
                                                     scanUpcDtm=timestamp, scanToteDtm=timestamp, picking_channel=10)

                # Confirm
                wms.autostore_picking.pickingTaskFinish(picking_task_id=picking_task_id, picking_type=11,
                                                        packing_line=1,
                                                        warehouse_number=warehouse_number, picking_channel=10)


            #AS OM Picking
            elif pick_list_type == 2:
                return

            #AS Replenish Picking
            else:
                return


            #数据还原，订单状态，wh_automation_pick_list，wh_automation_pick_task，wh_automation_pick_result，wh_automation_pick_task_detail
            if order_id == 242548069:
                time.sleep(3)
                #只还原测试订单
                wms.wms_db.update_order_ship_status(order_id=order_id,ship_status=25)
                wms.wms_db.update_automation_pick_list(status=1,pick_list_id=pick_list_id)
                wms.wms_db.delete_wh_automation_pick_result(pick_list_id=pick_list_id)
                #更新Pick List id
                wms.wms_db.update_pick_list_id(pick_list_id=pick_list_id)
                #删除Pick数据
                wms.wms_db.delete_wh_picking_order(order_id=order_id)

                #数据还原前置
            else:
                logging.info("KPI拣货完成")



        else:
            logging.info("订单非AS订单,订单状态为：",shipping_status)



if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', debug=True)


# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON> chen
@Version        :  V1.0.0
------------------------------------
@File           :  test_normal_picking.py
@Description    :
@CreateTime     :  2023/6/6 10:04
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/6/6 10:04
"""
import weeeTest
from weeeTest import jmespath

from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms


class TestMofPicking(weeeTest.TestCase):

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')

    def test_mof_picking(self):
        """
        【112954】MOF picking 流程
        """
        warehouse_number = global_data.mof_picking['warehouse_number']
        # storage_type = global_data.mof_picking['storage_type']

        # 登录
        user_id, user_name  = wms.wms_login.common_login()

        # 选择warehouse_number
        # picking.select_warehouse(warehouse=warehouse_number)

        # 查询可用的cart
        location_no = wms.wms_db.get_wh_storage_location_info(warehouse=warehouse_number, location_type=50, flag=0,
                                                              info='location_no')
        # 创建拣货任务
        resp = wms.mof_picking.create_picking_task(cart_no=location_no, warehouse_number=warehouse_number)
        message_id = resp['messageId']
        # 未拉取到任务，结束
        if message_id == "32003":
            return

        # 判断是否有未做完的任务
        if message_id == '99998':
            print("用户有未完成的任务，重新scan cart再进行作业")
            # 退出登录
            wms.logout(warehouse_number=warehouse_number, user_id=user_id, user_name=user_name)

        else:
            box_size = jmespath(resp, "body.box_size")
            picking_task_id = jmespath(resp, "body.picking_task_id")
            order_ids = jmespath(resp, "body.picking_order[*].order_id")
            shipping_types = jmespath(resp, "body.picking_order[*].shipping_type")

            # 查询可用的tote
            tote_nos = wms.wms_db.select_available_tote(location_type=6, flag=0, warehouse=warehouse_number,
                                                        box_size=box_size)

            # 判断订单与数据库中查询的tote数量
            total_order_num = len(order_ids)
            total_shipping_num = len(shipping_types)
            assert total_order_num == total_shipping_num
            assert len(tote_nos) >= total_order_num

            # 订单绑定拣货框
            print("共" + str(total_order_num) + "个订单需要绑定拣货框")
            for i in range(total_order_num):
                resp = wms.mof_picking.bind_tote_number(picking_task_id=picking_task_id,
                                                               tote_no=tote_nos[i]["location_no"],
                                                               order_id=order_ids[i],
                                                               shipping_type=shipping_types[i],
                                                               warehouse_number=warehouse_number)

                #  库存调整
                while True:
                    next_item = jmespath(resp, "body.nextItem")
                    picking_region_no = jmespath(resp, "body.picking_region_no")
                    if next_item is None:
                        print("没有生鲜作业，扫结束码后，重新扫描cart作业干货")
                        break
                    # 不是null，则表示有生鲜冻货作业，继续拣货
                    resp = wms.mof_picking.adjust_location(warehouse_number=warehouse_number,
                                                                  tote_no=next_item["tote_no"],
                                                                  location_no=next_item["location_no"],
                                                                  item_number=next_item["item_number"],
                                                                  order_id=next_item["order_id"],
                                                                  picking_quantity=next_item["item_quantity"],
                                                                  picking_type=jmespath(resp, "body.picking_type"),
                                                                  picking_task_id=jmespath(resp,"body.picking_task_id"))

                # 结束生鲜拣货任务
                wms.mof_picking.scan_picking_region(picking_region_no=picking_region_no, picking_task_id=picking_task_id,warehouse_number=warehouse_number)

                # 再扫cart，继续剩下的作业
                resp = wms.mof_picking.create_picking_task(cart_no=location_no, warehouse_number=warehouse_number)
                print("再次扫cart，继续剩下的作业")

                while True:
                    next_item = jmespath(resp, "body.nextItem")
                    picking_region_no = jmespath(resp, "body.picking_region_no")
                    if next_item is None:
                        print("生鲜作业也完成，扫描最终打包线")
                        break

                    resp = wms.mof_picking.adjust_location(warehouse_number=warehouse_number,
                                                                  tote_no=next_item["tote_no"],
                                                                  location_no=next_item["location_no"],
                                                                  item_number=next_item["item_number"],
                                                                  order_id=next_item["order_id"],
                                                                  picking_quantity=next_item["item_quantity"],
                                                                  picking_type=jmespath(resp, "body.picking_type"),
                                                                  picking_task_id=jmespath(resp,"body.picking_task_id"))
                # 结束任务
                wms.mof_picking.picking_task_finish(picking_task_id=jmespath(resp, "body.picking_task_id"),
                                  picking_type=jmespath(resp, "body.picking_type"),
                                  packing_line=picking_region_no,
                                  warehouse_number=warehouse_number)

                # 退出登录
                wms.logout(warehouse_number=warehouse_number, user_id=user_id, user_name=user_name)

if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', debug=True)


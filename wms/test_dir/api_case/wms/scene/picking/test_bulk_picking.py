# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""

"""
import weeeTest
from weeeTest import jmespath

from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms


class TestBulkPicking(weeeTest.TestCase):

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_bulk_picking(self):
        """
        【112951】Bulk picking 流程
        """
        warehouse_number = global_data.bulk_picking['warehouse_number']
        # 登录
        user_id, user_name  = wms.wms_login.common_login()

        # 查询可用的BMcart
        location_no = wms.wms_db.get_wh_storage_location_info(warehouse=warehouse_number, location_type=73, flag=0,
                                                              info='location_no')

        # 创建拣货任务
        resp = wms.bulk_picking.create_picking_task(cart_no=location_no, warehouse_number=warehouse_number)
        message_id = resp['messageId']

        # 未拉取到任务则返回
        if message_id == "32003":
            return

        # 判断是否有未做完的任务
        if message_id == '99998':
            print("用户有未完成的任务，重新scan cart再进行作业")
            # 可补充再查询未做完的任务，继续完成任务
            # 查询cart，更新上述cart no
            # SELECT * FROM  wms.wh_picking_task WHERE status < 4 AND warehouse_number = {warehouse_number} AND  in_user  = {user_id}
        else:
            #  库存调整
            while True:
                next_item = jmespath(resp, "body.nextItem")
                if next_item is None:
                    break

                resp = wms.bulk_picking.adjust_location(warehouse_number=warehouse_number,
                                                        tote_no=next_item["tote_no"],
                                                        location_no=next_item["location_no"],
                                                        item_number=next_item["item_number"],
                                                        order_id=next_item["order_id"],
                                                        picking_quantity=next_item["item_quantity"],
                                                        picking_type=jmespath(resp, "body.picking_type"),
                                                        picking_task_id=jmespath(resp, "body.picking_task_id"))

            # 结束拣货任务
            wms.bulk_picking.picking_task_finish(picking_task_id=jmespath(resp, "body.picking_task_id"),
                                                 picking_type=jmespath(resp, "body.picking_type"),
                                                 packing_line=jmespath(resp, "body.packing_line"),
                                                 warehouse_number=warehouse_number)
            # 退出登录
            wms.logout(warehouse_number=warehouse_number, user_id=user_id, user_name=user_name)

    if __name__ == '__main__':
        weeeTest.main(base_url='https://api.tb1.sayweee.net', debug=True)

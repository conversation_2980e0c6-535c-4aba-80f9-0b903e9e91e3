# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON> chen
@Version        :  V1.0.0
------------------------------------
@File           :  test_normal_picking.py
@Description    :  
@CreateTime     :  2023/6/6 10:04
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/6/6 10:04
"""
import weeeTest
from weeeTest import jmespath

from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms


class TestPicking(weeeTest.TestCase):

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_picking(self):
        """
        【112950】Normal picking 流程
        """
        # utils.update_header(weee_wms_storage_type=storage_type, weee_warehouse=warehouse_number)
        warehouse_number = global_data.normal_picking['warehouse_number']
        storage_type = global_data.normal_picking['storage_type']

        # 登录
        user_id, user_name  = wms.wms_login.common_login()

        # 选择warehouse_number
        # picking.select_wavehouse(warehouse=warehouse_number)

        # 查询可用的cart
        location_no = wms.wms_db.get_wh_storage_location_info(warehouse=warehouse_number, location_type=50, flag=0,
                                                              info='location_no')

        # 创建拣货任务
        resp = wms.picking.create_picking_task(cart_no=location_no, warehouse_number=warehouse_number,
                                                      storage_type=storage_type)
        message_id = resp['messageId']

        # 未拉取到任务则返回
        if message_id == "32003":
            return

        # 判断是否有未做完的任务
        if message_id == '99998':
            print("用户有未完成的任务，重新scan cart再进行作业")
            box_size = jmespath(resp, "body.box_size")
            print('需要绑定的tote size:', box_size)
            picking_task_id = jmespath(resp, "body.picking_task_id")
            order_ids = jmespath(resp, "body.picking_order[*].order_id")
            shipping_types = jmespath(resp, "body.picking_order[*].shipping_type")

            # 判断是否需要绑定tote
            next_item = jmespath(resp, "body.nextItem")
            if next_item is None:
                print("不需要绑定tote，直接扫cart，完成拣货任务")
                # 不需要绑定tote，直接扫cart，完成拣货任务
                # 查询未做完的任务
                cart_no = wms.wms_db.select_available_cart_no(warehouse=warehouse_number, user_id=user_id)
                print('未完成的任务,cart_no:', cart_no)
                resp = wms.picking.create_picking_task(cart_no=cart_no, warehouse_number=warehouse_number,
                                                              storage_type=storage_type)

                # 库存调整--开始拣货
                while True:
                    next_item = jmespath(resp, "body.nextItem")
                    if next_item is None:
                        break

                    resp = wms.picking.adjust_location(warehouse_number=warehouse_number,
                                                              tote_no=next_item["tote_no"],
                                                              location_no=next_item["location_no"],
                                                              item_number=next_item["item_number"],
                                                              order_id=next_item["order_id"],
                                                              picking_quantity=next_item["item_quantity"],
                                                              picking_type=jmespath(resp, "body.picking_type"),
                                                              picking_task_id=jmespath(resp, "body.picking_task_id"))

                # 结束拣货任务
                wms.picking.picking_task_finish(picking_task_id=jmespath(resp, "body.picking_task_id"),
                                                       picking_type=jmespath(resp, "body.picking_type"),
                                                       packing_line=jmespath(resp, "body.packing_line"),
                                                       warehouse_number=warehouse_number,
                                                       picking_task_status=jmespath(resp, "body.status"))

                # 退出登录
                wms.logout(warehouse_number=warehouse_number, user_id=user_id, user_name=user_name)

            else:
                # 查询未做完的任务
                print("有未完成的任务，且需要绑定tote后进行拣货作业")
                cart_no = wms.wms_db.select_available_cart_no(warehouse=warehouse_number, user_id=user_id)
                print('未完成的任务,cart_no:', cart_no)
                resp = wms.picking.create_picking_task(cart_no=cart_no, warehouse_number=warehouse_number,
                                                              storage_type=storage_type)

                # 查询可用的tote
                tote_nos = wms.wms_db.select_available_tote(location_type=6, flag=0, warehouse=warehouse_number,
                                                            box_size=box_size)
                # 判断订单与数据库中查询的tote数量
                total_order_num = len(order_ids)
                total_shipping_num = len(shipping_types)
                assert total_order_num == total_shipping_num
                if len(tote_nos) < total_order_num:
                    box_size_dict = {"S": 5, "M": 10, "L": 15}
                    for i in range(total_order_num):
                        wms.common_api.create_new_location(warehouse_number, 6, 1, user_id, tote_size=box_size_dict[box_size])

                    # 查询可用的tote
                    tote_nos = wms.wms_db.select_available_tote(location_type=6, flag=0, warehouse=warehouse_number,
                                                                box_size=box_size)
                # 订单绑定拣货框
                print("共" + str(total_order_num) + "个订单需要绑定拣货框")
                for i in range(total_order_num):
                    resp = wms.picking.bind_tote_number(picking_task_id=picking_task_id,
                                                               tote_no=tote_nos[i]["location_no"],
                                                               order_id=order_ids[i],
                                                               shipping_type=shipping_types[i],
                                                               warehouse_number=warehouse_number)

                #  库存调整
                while True:
                    next_item = jmespath(resp, "body.nextItem")
                    if next_item is None:
                        break

                    resp = wms.picking.adjust_location(warehouse_number=warehouse_number,
                                                              tote_no=next_item["tote_no"],
                                                              location_no=next_item["location_no"],
                                                              item_number=next_item["item_number"],
                                                              order_id=next_item["order_id"],
                                                              picking_quantity=next_item["item_quantity"],
                                                              picking_type=jmespath(resp, "body.picking_type"),
                                                              picking_task_id=jmespath(resp, "body.picking_task_id"))

                # 结束拣货任务
                wms.picking.picking_task_finish(picking_task_id=jmespath(resp, "body.picking_task_id"),
                                                       picking_type=jmespath(resp, "body.picking_type"),
                                                       packing_line=jmespath(resp, "body.packing_line"),
                                                       warehouse_number=warehouse_number,
                                                       picking_task_status=jmespath(resp, "body.status"))

                # 退出登录
                wms.logout(warehouse_number=warehouse_number, user_id=user_id, user_name=user_name)
        else:
            print("scan新cart，绑定tote，开始拣货作业")
            box_size = jmespath(resp, "body.box_size")
            picking_task_id = jmespath(resp, "body.picking_task_id")
            order_ids = jmespath(resp, "body.picking_order[*].order_id")
            shipping_types = jmespath(resp, "body.picking_order[*].shipping_type")

            # 查询可用的tote
            tote_nos = wms.wms_db.select_available_tote(location_type=6, flag=0, warehouse=warehouse_number,
                                                        box_size=box_size)

           # 判断订单与数据库中查询的tote数量
            total_order_num = len(order_ids)
            total_shipping_num = len(shipping_types)
            assert total_order_num == total_shipping_num
            if len(tote_nos) < total_order_num:
                box_size_dict = {"S": 5, "M": 10, "L": 15}
                for i in range(total_order_num):
                    wms.common_api.create_new_location(warehouse_number, 6, 1, user_id, tote_size=box_size_dict[box_size])

                # 查询可用的tote
                tote_nos = wms.wms_db.select_available_tote(location_type=6, flag=0, warehouse=warehouse_number,
                                                                box_size=box_size)

            # 订单绑定拣货框
            print("共" + str(total_order_num) + "个订单需要绑定拣货框")
            for i in range(total_order_num):
                resp = wms.picking.bind_tote_number(picking_task_id=picking_task_id,
                                                           tote_no=tote_nos[i]["location_no"],
                                                           order_id=order_ids[i],
                                                           shipping_type=shipping_types[i],
                                                           warehouse_number=warehouse_number)

            #  库存调整
            while True:
                next_item = jmespath(resp, "body.nextItem")
                if next_item is None:
                    break

                resp = wms.picking.adjust_location(warehouse_number=warehouse_number,
                                                          tote_no=next_item["tote_no"],
                                                          location_no=next_item["location_no"],
                                                          item_number=next_item["item_number"],
                                                          order_id=next_item["order_id"],
                                                          picking_quantity=next_item["item_quantity"],
                                                          picking_type=jmespath(resp, "body.picking_type"),
                                                          picking_task_id=jmespath(resp, "body.picking_task_id"))

            # 结束拣货任务
            wms.picking.picking_task_finish(picking_task_id=jmespath(resp, "body.picking_task_id"),
                                                   picking_type=jmespath(resp, "body.picking_type"),
                                                   packing_line=jmespath(resp, "body.packing_line"),
                                                   warehouse_number=warehouse_number,
                                                   picking_task_status=jmespath(resp, "body.status"))

            # 退出登录
            wms.logout(warehouse_number=warehouse_number, user_id=user_id, user_name=user_name)


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', debug=True)

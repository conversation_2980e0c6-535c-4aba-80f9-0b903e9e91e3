# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON> chen
@Version        :  V1.0.0
------------------------------------
@File           :  test_normal_picking.py
@Description    :
@CreateTime     :  2023/6/6 10:04
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/6/6 10:04
"""
import weeeTest
from weeeTest import jmespath

from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms


class TestPicking(weeeTest.TestCase):

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_picking(self):
        """
        【112952】Oneitem picking 流程
        """
        # utils.update_header(weee_wms_storage_type=storage_type, weee_warehouse=warehouse_number)
        warehouse_number = global_data.one_item_picking['warehouse_number']
        # 登录
        user_id, user_name  = wms.wms_login.common_login()

        # 选择warehouse_number
        # wms.one_item_picking.select_wavehouse(warehouse=warehouse_number)

        # 查询可用的OM cart
        location_no = wms.wms_db.get_wh_storage_location_info(warehouse=warehouse_number, location_type=52, flag=0,
                                                              info='location_no')

        # 创建拣货任务
        resp = wms.one_item_picking.create_picking_task(cart_no=location_no, warehouse_number=warehouse_number)
        message_id = resp['messageId']
        # 未拉取到任务，结束
        if message_id == "32003":
            return

        # 判断是否有未做完的任务
        if message_id == '99998':
            print("用户有未完成的任务，请先检查~")
            # TODO可补充再查询未做完的任务，继续完成任务
            # 退出登录
            wms.logout(warehouse_number=warehouse_number, user_id=user_id, user_name=user_name)

        else:
            #  库存调整
            while True:
                next_item = jmespath(resp, "body.nextItem")
                if next_item is None:
                    break

                resp = wms.one_item_picking.adjust_location(warehouse_number=warehouse_number,
                                                            tote_no=next_item["tote_no"],
                                                            location_no=next_item["location_no"],
                                                            item_number=next_item["item_number"],
                                                            order_id=next_item["order_id"],
                                                            picking_quantity=next_item["item_quantity"],
                                                            picking_type=jmespath(resp, "body.picking_type"),
                                                            picking_task_id=jmespath(resp, "body.picking_task_id"))

            # 结束拣货任务
            wms.one_item_picking.picking_task_finish(picking_task_id=jmespath(resp, "body.picking_task_id"),
                                                     picking_type=jmespath(resp, "body.picking_type"),
                                                     packing_line=jmespath(resp, "body.packing_line"),
                                                     warehouse_number=warehouse_number)

            #更新拣货车状态
            wms.wms_db.update_tote_status(warehouse_number=warehouse_number,toteNo="location_no")

            # 退出登录
            wms.logout(warehouse_number=warehouse_number, user_id=user_id, user_name=user_name)


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', debug=True)

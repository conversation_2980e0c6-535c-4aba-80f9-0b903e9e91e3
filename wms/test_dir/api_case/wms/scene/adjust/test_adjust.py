import weeeTest
from weeeTest import jmespath
from datetime import datetime
from wms.test_dir.api.wms.wms import wms
from wms.test_dir.api.wms.adjust.adjust_api import Adjust
from wms.test_dir.api_case.wms.scene.adjust.test_approve import TestInvApprove


class TestAdjust(weeeTest.TestCase):
    # 查询非LPN库存
    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_query_inv_upc(self):
        """
        【111207】Adjust：非LPN库存查询
        """
        # 登录
        wms.wms_login.common_login()
        # 参数设置
        warehouse_number = "25"
        location_no = "A0705-2-1"
        item_number = "10094"
        lpn_no = None
        Adjust().query_location_inv(item_number, location_no, warehouse_number, lpn_no)

    # 调整非LPN库存
    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_adjust_upc_in(self):
        """
        【111216】Adjust：非LPN库存调增
        """
        # 登录
        user_id, user_name = wms.wms_login.common_login()
        edit_user = user_name + '(' + user_id + ")"  # 拼接in_user
        in_user = edit_user
        # 参数设置
        warehouse_number = "25"
        location_no = "A0705-2-1"
        item_number = "10094"
        adjust_number = 20
        is_lpn = False
        pieces_per_pack = "10.00"
        expire_dtm = "2025-04-30"
        batch_no = "240513084377"
        lpn_list = []
        Adjust().create_batch_inv(item_number, warehouse_number, in_user, location_no, adjust_number, is_lpn, pieces_per_pack, expire_dtm, batch_no, lpn_list)
        # 审核库存
        action_type = 2
        lpn_no = None
        TestInvApprove().test_inv_approve_pass(in_user, item_number, warehouse_number, location_no, batch_no, lpn_no, action_type)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_adjust_upc_out(self):
        """
        【112892】Adjust：非LPN库存调减
        """
        # 登录
        user_id, user_name = wms.wms_login.common_login()
        edit_user = user_name + '(' + user_id + ")"  # 拼接in_user
        in_user = edit_user
        # 参数设置
        warehouse_number = "25"
        location_no = "A0705-2-1"
        item_number = "10094"
        adjust_number = -15
        is_lpn = False
        pieces_per_pack = "10.00"
        expire_dtm = "2025-04-30"
        batch_no = "240513084377"
        lpn_list = []
        Adjust().create_batch_inv(item_number, warehouse_number, in_user, location_no, adjust_number, is_lpn, pieces_per_pack, expire_dtm, batch_no, lpn_list)
        # 审核库存
        action_type = 2
        lpn_no = None
        TestInvApprove().test_inv_approve_pass(in_user, item_number, warehouse_number, location_no, batch_no, lpn_no, action_type)

    # 修改非LPN保质期
    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_adjust_expire_dtm_upc(self):
        """
        【111222】Adjust：修改非LPN保质期
        """
        # 登录
        # 登录
        wms.wms_login.common_login()
        # 参数设置
        warehouse_number = "25"
        item_number = "25229"
        location_nos = ["A0510-9-2"]
        lpn_nos = None
        is_all_lpn = None
        # 查询batch_no信息
        resp = Adjust().query_location_inv(item_number=item_number, location_no=location_nos[0], warehouse_number=warehouse_number, lpn_no=lpn_nos)
        batch_no = jmespath(resp, "body.invAdjustList[0].batch_no")  # 获取batch_no
        location_no = jmespath(resp, "body.invAdjustList[0].location_no")  # 获取location_no
        batch_inv_infos = [
            {
                "location_no": location_no,
                "batch_no": batch_no
            }]
        new_expire_date = f"{datetime.now().date()}"  # 设置当前日期为过期时间
        input_dtm_str = f"{datetime.now().date()}"
        Adjust().modify_shelf_life(warehouse_number, item_number, location_nos, lpn_nos, is_all_lpn, batch_inv_infos, new_expire_date, input_dtm_str)

    # Lock/unLock非LPN库存
    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_adjust_unlock_upc(self):
        """
        【111220】Adjust：unLock非LPN库存
        """
        # 登录
        wms.wms_login.common_login()
        # 参数设置
        warehouse_number = "25"
        item_number = "25229"
        location_no = "A0510-9-2"
        is_lock = 0
        is_lpn = False
        lpn_nos = None
        # 查询batch_no信息
        resp1 = Adjust().query_location_inv(item_number=item_number, location_no=location_no, warehouse_number=warehouse_number, lpn_no=lpn_nos)
        batch_no = jmespath(resp1, "body.invAdjustList[0].batch_no")  # 获取batch_no
        Adjust().lock_inv(is_lock, item_number, location_no, warehouse_number, is_lpn, lpn_nos, batch_no)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_adjust_lock_upc(self):
        """
        【111218】Adjust：Lock非LPN库存
        """
        # 登录
        wms.wms_login.common_login()
        # 参数设置
        warehouse_number = "25"
        item_number = "25229"
        location_no = "A0510-9-2"
        is_lock = 1
        is_lpn = False
        lpn_nos = None
        # 查询batch_no信息
        resp1 = Adjust().query_location_inv(item_number=item_number, location_no=location_no, warehouse_number=warehouse_number, lpn_no=lpn_nos)
        batch_no = jmespath(resp1, "body.invAdjustList[0].batch_no")  # 获取batch_no
        Adjust().lock_inv(is_lock, item_number, location_no, warehouse_number, is_lpn, lpn_nos, batch_no)

    # 查询LPN库存
    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_query_inv_lpn(self):
        """
        【111215】Adjust：LPN库存查询
        """
        # 登录
        wms.wms_login.common_login()
        # 参数设置
        warehouse_number = "25"
        location_no = "B2601-1-1"
        item_number = "10094"
        lpn_no = "G2402-10094-M1-1"
        Adjust().query_location_inv(item_number, location_no, warehouse_number, lpn_no)

    # 调整LPN库存
    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_adjust_lpn_in(self):
        """
        【111217】Adjust：LPN库存调增
        """
        # 登录
        user_id, user_name = wms.wms_login.common_login()
        edit_user = user_name + '(' + user_id + ")"  # 拼接in_user
        in_user = edit_user
        # 参数设置
        warehouse_number = "25"
        location_no = "B2601-1-1"
        item_number = "10094"
        adjust_number = 10
        is_lpn = True
        pieces_per_pack = "30.00"
        expire_dtm = "2025-04-30"
        batch_no = "241116000025"
        lpn_no = "G2402-10094-M1-1"
        res = Adjust().query_location_inv(item_number, location_no, warehouse_number, lpn_no)
        # 使用 jmespath 提取 quantity 的值
        original_quantity = jmespath(res, "body.invAdjustList[0].quantity", )
        lpn_list = [
            {
                "lpn_no": lpn_no,
                "original_quantity": original_quantity,
                "adjust_number": adjust_number
            }
        ]
        Adjust().create_batch_inv(item_number, warehouse_number, in_user, location_no, adjust_number, is_lpn, pieces_per_pack, expire_dtm, batch_no, lpn_list)
        # 审核库存
        action_type = 2
        TestInvApprove().test_inv_approve_pass(in_user, item_number, warehouse_number, location_no, batch_no, lpn_no, action_type)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_adjust_lpn_out(self):
        """
        【112891】Adjust：LPN库存调减
        """
        # 登录
        user_id, user_name = wms.wms_login.common_login()
        edit_user = user_name + '(' + user_id + ")"  # 拼接in_user
        in_user = edit_user
        # 参数设置
        warehouse_number = "25"
        location_no = "B2601-1-1"
        item_number = "10094"
        adjust_number = -10
        is_lpn = True
        pieces_per_pack = "30.00"
        expire_dtm = "2025-04-30"
        batch_no = "241116000025"
        lpn_no = "G2402-10094-M1-1"
        res = Adjust().query_location_inv(item_number, location_no, warehouse_number, lpn_no)
        # 使用 jmespath 提取 quantity 的值
        original_quantity = jmespath(res, "body.invAdjustList[0].quantity", )
        lpn_list = [
            {
                "lpn_no": lpn_no,
                "original_quantity": original_quantity,
                "adjust_number": adjust_number
            }
        ]
        Adjust().create_batch_inv(item_number, warehouse_number, in_user, location_no, adjust_number, is_lpn, pieces_per_pack, expire_dtm, batch_no, lpn_list)
        # 审核库存
        action_type = 2
        TestInvApprove().test_inv_approve_pass(in_user, item_number, warehouse_number, location_no, batch_no, lpn_no, action_type)

    # 修改LPN保质期
    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_adjust_expire_dtm_lpn(self):
        """
        【111223】Adjust：修改LPN保质期
        """
        # 登录
        wms.wms_login.common_login()
        # 参数设置
        warehouse_number = "25"
        item_number = "25229"
        location_nos = None
        lpn_nos = ["G2405-25229-M1-1"]
        is_all_lpn = False
        # 查询batch_no信息
        resp1 = Adjust().query_location_inv(item_number=item_number, location_no=location_nos, warehouse_number=warehouse_number, lpn_no=lpn_nos[0])
        batch_no = jmespath(resp1, "body.invAdjustList[0].batch_no")  # 获取batch_no
        location_no = jmespath(resp1, "body.invAdjustList[0].location_no")  # 获取location_no
        batch_inv_infos = [
            {
                "location_no": location_no,
                "batch_no": batch_no
            }]
        new_expire_date = f"{datetime.now().date()}"  # 设置当前日期为过期时间
        input_dtm_str = f"{datetime.now().date()}"
        Adjust().modify_shelf_life(warehouse_number, item_number, location_nos, lpn_nos, is_all_lpn, batch_inv_infos, new_expire_date, input_dtm_str)

    # Lock/unLock LPN库存
    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_adjust_unlock_lpn(self):
        """
        【111221】Adjust：unLock LPN库存
        """
        # 登录
        wms.wms_login.common_login()
        # 参数设置
        warehouse_number = "25"
        item_number = "25229"
        location_no = "B2601-1-1"
        is_lock = 0
        is_lpn = True
        lpn_nos = ["G2405-25229-M1-2"]
        # 查询batch_no信息
        resp1 = Adjust().query_location_inv(item_number=item_number, location_no=location_no, warehouse_number=warehouse_number, lpn_no=lpn_nos[0])
        batch_no = jmespath(resp1, "body.invAdjustList[0].batch_no")  # 获取batch_no
        Adjust().lock_inv(is_lock, item_number, location_no, warehouse_number, is_lpn, lpn_nos, batch_no)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_adjust_lock_lpn(self):
        """
        【111219】Adjust：Lock LPN库存
        """
        # 登录
        wms.wms_login.common_login()
        # 参数设置
        warehouse_number = "25"
        item_number = "25229"
        location_no = "B2601-1-1"
        is_lock = 1
        is_lpn = True
        lpn_nos = ["G2405-25229-M1-2"]
        # 查询batch_no信息
        resp1 = Adjust().query_location_inv(item_number=item_number, location_no=location_no, warehouse_number=warehouse_number, lpn_no=lpn_nos[0])
        batch_no = jmespath(resp1, "body.invAdjustList[0].batch_no")  # 获取batch_no
        Adjust().lock_inv(is_lock, item_number, location_no, warehouse_number, is_lpn, lpn_nos, batch_no)


if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True, open=False)

# 非LPN库存查询
# 非LPN库存调增
# 非LPN库存调减
# 修改非LPN保质期
# Lock 非LPN库存
# UNLock 非LPN库存

# LPN库存查询
# LPN库存调增
# LPN库存调减
# 修改LPN保质期
# Lock LPN库存
# UNLock LPN库存

import weeeTest
from wms.test_dir.api.wms.wms import wms
from wms.test_dir.api.wms.adjust.adjust_api import Adjust


class TestInvApprove(weeeTest.TestCase):
    # 库存审核
    def test_inv_approve_pass(self, in_user, item_number, warehouse_number, location_no, batch_no, lpn_no, action_type, approve_type=1):
        """
        Approve：审核通过
        """
        # 参数设置
        rec_ids = wms.wms_db.get_approval_task(warehouse_number, item_number, location_no)  # SQL查询审核任务ID
        if not rec_ids:
            print('没有待审核的任务...')
            return
        else:
            rec_id = rec_ids[0].get("rec_id")
            if lpn_no is None:
                # batch+非LPN库存
                qty_old = wms.wms_db.get_quantity_and_allocated_qty_sum(item_number, warehouse_number, location_no, batch_no, lpn_no)[0]  # SQL查询调整前总库存
                batch_qty_old = wms.wms_db.get_quantity_and_allocated_qty_sum(item_number, warehouse_number, location_no, batch_no, lpn_no)[1]  # SQL
                # 查询调整前batch库存
                Adjust().approve_task(item_number, approve_type, in_user, warehouse_number, rec_id, action_type)
                # 断言审核状态是不是30
                res = wms.wms_db.get_approval_status(warehouse_number, rec_id)  # SQL查询审核任务状态
                if not res:
                    raise AssertionError("No approval status found for the given task ID.")
                status = res[0].get("status")
                adjust_qty = res[0].get("actual_adjust_qty")
                assert status == 30, "审核状态不等于30"
                # 断言库存是否调整正确
                qty_new = wms.wms_db.get_quantity_and_allocated_qty_sum(item_number, warehouse_number, location_no, batch_no, lpn_no)[0]  # SQL查询调整后总库存
                batch_qty_new = wms.wms_db.get_quantity_and_allocated_qty_sum(item_number, warehouse_number, location_no, batch_no, lpn_no)[1]  #
                # SQL查询调整后batch库存
                assert int(qty_new) == int(qty_old) + adjust_qty, "审核前后总库存不一致"
                assert int(batch_qty_new) == int(batch_qty_old) + adjust_qty, "审核前后batch库存不一致"
            else:
                # batch+LPN库存
                qty_old = wms.wms_db.get_quantity_and_allocated_qty_sum(item_number, warehouse_number, location_no, batch_no, lpn_no)[0]  # SQL查询调整前总库存
                batch_qty_old = wms.wms_db.get_quantity_and_allocated_qty_sum(item_number, warehouse_number, location_no, batch_no, lpn_no)[1]  # SQL
                # 查询调整前batch库存
                lpn_qty_old = wms.wms_db.get_quantity_and_allocated_qty_sum(item_number, warehouse_number, location_no, batch_no, lpn_no)[2]  # SQL
                # 查询调整前lpn库存
                Adjust().approve_task(item_number, approve_type, in_user, warehouse_number, rec_id, action_type)
                # 断言审核状态是不是30
                res = wms.wms_db.get_approval_status(warehouse_number, rec_id)  # SQL查询审核任务状态
                if not res:
                    raise AssertionError("No approval status found for the given task ID.")
                status = res[0].get("status")
                adjust_qty = res[0].get("actual_adjust_qty")
                assert status == 30, "审核状态不是30"
                # 断言库存是否调整正确
                qty_new = wms.wms_db.get_quantity_and_allocated_qty_sum(item_number, warehouse_number, location_no, batch_no, lpn_no)[0]  # SQL查询调整后总库存
                batch_qty_new = wms.wms_db.get_quantity_and_allocated_qty_sum(item_number, warehouse_number, location_no, batch_no, lpn_no)[1]  #
                # SQL查询调整后batch库存
                lpn_qty_new = wms.wms_db.get_quantity_and_allocated_qty_sum(item_number, warehouse_number, location_no, batch_no, lpn_no)[2]  # SQL
                # 查询调整后lpn库存
                assert int(qty_new) == int(qty_old) + adjust_qty, "审核前后总库存不一致"
                assert int(batch_qty_new) == int(batch_qty_old) + adjust_qty, "审核前后batch库存不一致"
                assert int(lpn_qty_new) == int(lpn_qty_old) + adjust_qty, "审核前后LPN库存不一致"

    def test_inv_approve_recount(self, in_user, item_number, warehouse_number, location_no, batch_no, action_type, approve_type=0):
        """
        Approve：审核拒绝
        """
        # 参数设置
        rec_ids = wms.wms_db.get_approval_task(warehouse_number, item_number, location_no)  # SQL查询审核任务ID
        if not rec_ids:
            print('没有待审核的任务...')
            return
        else:
            rec_id = rec_ids[0].get("rec_id")
            qty_old = wms.wms_db.get_quantity_and_allocated_qty_sum(item_number, warehouse_number, location_no, batch_no)[0]  # SQL查询调整前库存
            Adjust().approve_task(item_number, approve_type, in_user, warehouse_number, rec_id, action_type)
            # 断言审核状态是不是30
            res = wms.wms_db.get_approval_status(warehouse_number, rec_id)  # SQL查询审核任务状态
            if not res:
                raise AssertionError("No approval status found for the given task ID.")
            status = res[0].get("status")
            assert status == 40, "审核状态不是40"
            # 断言库存是否调整正确
            qty_new = wms.wms_db.get_quantity_and_allocated_qty_sum(item_number, warehouse_number, location_no, batch_no)[0]  # SQL查询调整后库存
            assert int(qty_new) == int(qty_old), "审核前后总库存不一致"


if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True, open=False)

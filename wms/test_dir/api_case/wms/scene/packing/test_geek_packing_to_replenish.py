# !/usr/bin/python3
# -*- coding: utf-8 -*-

import weeeTest
from weeeTest import jmespath
from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms
import logging
import time, json, copy
from datetime import datetime

class TestGeekPackingToReplenish(weeeTest.TestCase):

    # @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_geek_packing_to_replenish(self):
        """
        【112987】geek packing转补单,补单流程转打包
        """
        warehouse = global_data.geekplus_packing_to_replenish['warehouse']
        tote_no = global_data.geekplus_packing_to_replenish['tote_no']
        order_id = global_data.geekplus_packing_to_replenish['order_id']
        storage_type = global_data.geekplus_packing_to_replenish['storage_type']
        # 登录
        account, username = wms.wms_login.common_login()
        # 如果tote中有库存清空
        location = [tote_no]
        wms.moving_api.clear_inv(warehouse,account,location)

        # 检查当前订单状态
        shipping_status = wms.wms_db.check_shipping_status(order_id=order_id)
        if shipping_status == 70:
            wms.wms_db.rollback_wait_packing(warehouse, order_id, tote_no)
        else:
            # 检查数据库中有无上次执行中断的补单数据,有则强制结束，订单状态变为待打包
            ret = wms.wms_db.select_replenish_order(order_id=order_id)
            if isinstance(ret, dict):
                wms.replenish.checkout_force_complete(ret["tote_no"], ret["slot_no"])

        # 执行打包操作
        oos_item,is_batch, items, in_dtm = wms.normal_packing_operation(warehouse, tote_no, order_id, storage_type, account, username, is_replenish=True)

        wms.normal_packing.query_replenish_quantity(tote_no, order_id)

        # 转补单
        wms.normal_packing.packing_replenish(tote_no, order_id, oos_item)

        # check order status
        wms.wms_db.check_shipping_status(order_id=order_id, status=42)

        # check tote status
        wms.wms_db.check_tote_status(warehouse=warehouse, tote_no=tote_no, status=3)
        # check inventory
        for item in items:
            if item["item_number"] != oos_item.get("item_number", ""):
                wms.wms_assert.check_non_batch_invenotry(warehouse, tote_no, item["item_number"], -item["quantity"], reference_no=order_id, in_dtm=in_dtm)
                if is_batch:
                    wms.wms_assert.check_batch_invenotry(warehouse, tote_no, item["item_number"], -item["quantity"], batch_no=item["batch_no"], reference_no=order_id, in_dtm=in_dtm)
            else:
                wms.wms_assert.check_non_batch_invenotry(warehouse, tote_no, item["item_number"], -item["quantity"], reference_no=order_id, inventory_type=[551, 801], in_dtm=in_dtm)
                if is_batch:
                    wms.wms_assert.check_batch_invenotry(warehouse, tote_no, item["item_number"], -item["quantity"], batch_no=item["batch_no"], reference_no=order_id, inventory_type=[551, 801], in_dtm=in_dtm)

        # replenish order check in
        wms.replenish.check_tote(tote_no)
        slot_no = wms.wms_db.get_wh_storage_location_info(warehouse=warehouse, location_type=35, flag=0,
                                                          info='location_no')
        wms.replenish.check_slot(slot_no, order_id)
        wms.replenish.bind_slot(tote_no, slot_no)
        wms.wms_db.check_shipping_status(order_id=order_id, status=45)

        # 预占成功后调用接口发送到geek
        order_info = wms.wms_db.select_wms_order_info(order_id=order_id)
        delivery_dtm = order_info['delivery_dtm']
        # 获取Pick List id
        res2 = wms.wms_db.get_order_as_task(order_id=order_id,
                                            info=["rec_id", "pick_list_rec_id", "item_number", "status"])
        pick_list_id = res2[0]["pick_list_rec_id"]
        pisk_task_status = res2[0]["status"]

        # 获取task_detail表id，item
        resp3 = wms.wms_db.get_order_as_task_detail(pick_list_id=pick_list_id,
                                                    info=['rec_id', 'item_number', 'batch_no', 'receive_dtm',
                                                          'expire_dtm', 'quantity'])

        # 发送校验，取消待发送订单前一发货日Dry MO订单
        before_delivery_dtm = delivery_dtm - 86400
        wms.wms_db.update_order(order_status=2, delivery_dtm=before_delivery_dtm, warehouse_number=warehouse)

        # 更新AutoStoreConfig开关、起始日期
        start_dtm = datetime.utcfromtimestamp(delivery_dtm)
        formatted_date = start_dtm.strftime('%Y-%m-%d')
        config_value = f"{{\"active\": true, \"agingOrderMaxTime\": 600, \"oneItemOneOrderVolumeLimit\": 1000, \"oneItemVolumeMaxLimit\": 2500, \"oneItemVolumeMinLimit\": 1, \"pickCartConfigList\": [{{\"count\": 4, \"size\": 10, \"weight\": 10}}, {{\"count\": 2, \"size\": 15, \"weight\": 15}}, {{\"size\": 25, \"count\": 0, \"weight\": 25}}, {{\"size\": 30, \"count\": 0, \"weight\": 30}}], \"startDeliveryDate\": \"{formatted_date}\", \"supplier\": \"GeekPlus\", \"sendDetailIdDeliveryDate\": \"2000-11-29\", \"supportOneItem\": false, \"oosLockInventory\": true, \"sendSkipWaveIds\": [], \"cancelOrderSetKpiComplete\": false, \"restockCallActive\": true, \"mailOrderSupportOneItem\": true, \"mailOrderOneItemOneOrderVolumeLimit\": 1000, \"asItemCanMarkCondition\": {{\"max_sku_weight_lb\": 10, \"max_skuBox_weight_lb\": 54, \"max_skuBox_volume_in3\": 1000}}}}"
        wms.update_central_config(warehouse_number=warehouse,
                                  config_key="support_automation", config_value=config_value)

        # 发送Pick list
        wms.autostore_picking.geekPlusJob()
        time.sleep(5)
        resp4 = wms.wms_db.get_order_as_list(pick_list_id=pick_list_id, info=["rec_id", "status"])
        pisk_list_status = resp4["status"]
        assert pisk_list_status == 10, "Pick List发送KPI失败"

        palletno = wms.wms_db.get_wh_storage_location_info(warehouse=warehouse, location_type=6,
                                                           flag=0, info="location_No")
        out_order_code = "TB1_" + str(pick_list_id)

        # 绑定Container
        json_data = {
            "body": {
                "warehouse_code": "41",
                "binding_list": [
                    {
                        "id": 114,
                        "out_order_code": out_order_code,
                        "owner_code": "SayWeee",
                        "operate_time": 1736480158410,
                        "operator": "7875714",
                        "container_code": palletno,
                        "wall_code": "YD3",
                        "workstation_no": "106",
                        "seeding_bin_code": "A1",
                        "status": 1,
                        "reservation01": "",
                        "reservation02": "",
                        "reservation03": "",
                        "warehouse_code": warehouse
                    }
                ]
            },
            "header": {
                "interface_code": "feedback_bind_container",
                "user_id": "Weee",
                "user_key": "96e79218965eb72c92a549dd5a330112",
                "warehouse_code": warehouse
            }
        }
        wms.autostore_picking.geekPlusApi(data=json_data)

        json_data_template1 = {
            "colValPair": {
                "externalCode": out_order_code,
                "column": "id",
                "value": 215732
            },
            "tableName": "out_order_details_back",
            "id": 215732,
            "sku_id": None,
            "packing_spec": "",
            "out_order_code": out_order_code,
            "item": 1,
            "sku_code": None,
            "bar_code": "",
            "sku_level": 0,
            "plan_amount": None,
            "pickup_amount": None,
            "is_sequence_sku": 0,
            "sequence_no": "",
            "out_batch_code": None,
            "production_date": 1751173200000,
            "expiration_date": None,
            "input_date": 1735189200000,
            "product_batch": "",
            "batch_property01": "1751173200000",
            "batch_property02": "1766725200000",
            "batch_property03": "241226000006",
            "batch_property04": "",
            "batch_property05": "",
            "batch_property06": "100",
            "batch_property07": "",
            "batch_property08": "",
            "batch_property09": "",
            "batch_property10": "PA20241226000005",
            "batch_property11": "SayWeee",
            "batch_property12": "",
            "owner_code": "SayWeee",
            "operator": "",
            "operation_date": 0,
            "lack_flag": 0,
            "is_need_product_batch_manage": 0,
            "outorderskuuploadsub_list": [],
            "sequence_list": [],
            "sn_list": [],
            "production_line": 0,
            "site": 0,
            "shelf_bin_list": [
                {
                    "quantity": 2,
                    "shelf_code": "P00010",
                    "shelf_bin_code": "P00010B05A",
                    "operator": "7875714",
                    "container_code": palletno,
                    "logic_area_code": "logic-P"
                }
            ],
            "lack_description": "",
            "lack_code": ""
        }
        data_list1 = resp3
        json_data_list1 = []
        # 遍历列表数据，将每个元素的 rec_id 和 item_number 带入 JSON 数据模板中
        for data in data_list1:
            # 深拷贝模板，以免修改原始模板
            current_json_data1 = copy.deepcopy(json_data_template1)

            # 更新 JSON 数据中的 rec_id 和 item_number
            current_json_data1['sku_id'] = data['item_number']
            current_json_data1['sku_code'] = data['item_number']
            current_json_data1['plan_amount'] = data['quantity']
            current_json_data1['pickup_amount'] = data['quantity']
            current_json_data1['out_batch_code'] = data['batch_no']
            current_json_data1['expiration_date'] = datetime.utcfromtimestamp(data['expire_dtm']).strftime(
                '%Y-%m-%d %H:%M:%S.%f')

            # 将更新后的 JSON 数据添加到列表中
            json_data_list1.append(current_json_data1)
            new_json_data_list1 = json.dumps(json_data_list1, ensure_ascii=False)

        json_data_template2 = {
            "id": 215739,
            "out_order_container_id": 118200,
            "sku_id": None,
            "packing_spec": "",
            "out_order_code": out_order_code,
            "container_code": palletno,
            "item": 1,
            "sku_code": None,
            "bar_code": "",
            "sku_level": 0,
            "amount": None,
            "is_sequence_sku": 0,
            "sequence_no": "",
            "out_batch_code": None,
            "remark": "",
            "sku_name": "Human Happy Spicy Rice Noodle 360g",
            "owner_code": "SayWeee",
            "cell_code": "",
            "production_date": 1751173200000,
            "expiration_date": None,
            "batch_property01": "1751173200000",
            "batch_property02": "1766725200000",
            "batch_property03": "241226000006",
            "batch_property04": "",
            "batch_property05": "",
            "batch_property06": "100",
            "batch_property07": "",
            "batch_property08": "",
            "batch_property09": "",
            "batch_property10": "PA20241226000005",
            "batch_property11": "SayWeee",
            "batch_property12": "",
            "sequence_list": [],
            "sn_list": [],
            "cell_bin_code": "",
            "wall_position": 0,
            "lack_flag": 0,
            "lack_description": "",
            "lack_code": ""
        }
        data_list2 = resp3
        json_data_list2 = []

        # 遍历列表数据，将每个元素的 rec_id 和 item_number 带入 JSON 数据模板中

        for data in data_list2:
            # 深拷贝模板，以免修改原始模板
            current_json_data2 = copy.deepcopy(json_data_template2)
            # 更新 JSON 数据中的 rec_id 和 item_number

            current_json_data2['sku_id'] = data['item_number']
            current_json_data2['sku_code'] = data['item_number']
            current_json_data2['plan_amount'] = data['quantity']
            current_json_data2['out_batch_code'] = data['batch_no']
            current_json_data2['expiration_date'] = datetime.utcfromtimestamp(data['expire_dtm']).strftime(
                '%Y-%m-%d %H:%M:%S.%f')

            # 将更新后的 JSON 数据添加到列表中
            json_data_list2.append(current_json_data2)
            new_json_data_list2 = json.dumps(json_data_list2, ensure_ascii=False)
        # print(new_json_data_list2)

        data = {
            "body": {
                "size": 1,
                "order_amount": 1,
                "order_list": [
                    {
                        "picker": "7875714",
                        "operator": "",
                        "operateTime": 0,
                        "id": 118193,
                        "out_order_code": "TB1_3950",
                        "status": 3,
                        "warehouse_code": "41",
                        "owner_code": "SayWeee",
                        "is_exception": 0,
                        "order_type": 20,
                        "plan_sku_amount": 4,
                        "pickup_sku_amount": 4,
                        "pick_type": 1,
                        "lack_flag": 0,
                        "sync_date": 1736495880231,
                        "finish_date": 1736495802879,
                        "pkg_amount": 0,
                        "container_amount": 1,
                        "reservation1": "",
                        "reservation2": "",
                        "reservation3": "",
                        "reservation4": "",
                        "reservation5": "",
                        "sku_list": new_json_data_list1,
                        "package_list": [],
                        "inf_function": 0,
                        "container_list": [
                            {
                                "picker": "7875714",
                                "isSync": 0,
                                "syncTimes": 0,
                                "isLastContainer": 1,
                                "id": 118200,
                                "wall_code": "YD3",
                                "out_order_code": "TB1_3950",
                                "container_code": "TG0787",
                                "sku_amount": 4,
                                "sku_type_amount": 2,
                                "creation_date": 1736495847038,
                                "start_time": 1736490510111,
                                "complete_time": 1736495802990,
                                "sku_list": new_json_data_list2,
                                "container_amount": 1,
                                "container_type": 2,
                                "reservation1": "",
                                "reservation2": "",
                                "reservation3": "",
                                "reservation4": "",
                                "reservation5": "",
                                "is_order_last_container": 1,
                                "wave_code": "W20250110-00000002",
                                "pick_type": 0,
                                "wave_type": 20,
                                "operator": "",
                                "operation_date": 0,
                                "workstation_no": "106",
                                "seeding_bin_code": "A1",
                                "pick_seeding_bin_no": "A1",
                                "double_pick_type": 1,
                                "inf_function": 0
                            }
                        ],
                        "sku_amount": 4,
                        "data_source_platform": 200,
                        "carrier_code": "SayWeee",
                        "carrier_name": "SayWeee",
                        "waybill_code": "",
                        "consignor": "",
                        "consignor_phone": "",
                        "consignor_tel": "",
                        "consignment_company": "",
                        "consignor_province": "",
                        "consignor_city": "",
                        "consignor_district": "",
                        "consignor_address": "",
                        "consignee_province": "",
                        "consignee_city": "",
                        "consignee_district": "",
                        "consignee_address": "",
                        "consignee": "",
                        "consignee_phone": "",
                        "consignee_tel": "",
                        "start_time": 1736490510116,
                        "reservation6": "",
                        "reservation7": "",
                        "reservation8": "",
                        "reservation9": "",
                        "reservation10": "",
                        "reservation11": "",
                        "reservation12": "",
                        "reservation13": "",
                        "reservation14": "",
                        "reservation15": ""
                    }
                ]
            },
            "header": {
                "interface_code": "feedback_outbound_order",
                "user_id": "Weee",
                "user_key": "96e79218965eb72c92a549dd5a330112",
                "warehouse_code": "41"
            }
        }
        new_data = json.dumps(data, ensure_ascii=False)
        # print(new_data)
        wms.autostore_picking.geekPlusApi(data=new_data)

        # 数据还原，订单状态，wh_automation_pick_list，wh_automation_pick_task，wh_automation_pick_result，wh_automation_pick_task_detail
        # if order_id == 242523270:
        #     #只还原测试订单
        #     wms.wms_db.update_order_ship_status(order_id=242523270,ship_status=25)
        #     wms.wms_db.update_automation_pick_list(pick_list_id=pick_list_id)
        #     wms.wms_db.delete_wh_automation_pick_result(pick_list_id=pick_list_id)
        #     # 更新Pick List id
        #     wms.wms_db.update_pick_list_id(pick_list_id=pick_list_id)


        # replenish item pick
        hot_tote = wms.wms_db.get_wh_storage_location_info(warehouse=warehouse, location_type=26, flag=0,
                                                           info='location_no')
        wait_picking_list = wms.replenish.get_pick_list()
        wms.replenish.scan_hot_tote(hot_tote)
        wms.replenish.pick_confirm(wait_picking_list, hot_tote)

        # replenish item check in
        hot_pick_list = wms.replenish.query_hot_pick_tote()["body"]
        assert hot_tote in list(map(lambda h: h["tote_no"], hot_pick_list)), f"{hot_tote}不在待check in的pick tote列表中"
        wms.replenish.scan_hot_pick_tote(hot_tote)
        checkin_list = wms.replenish.get_checkin_list(hot_tote)["body"]
        wms.replenish.item_checkin_confirm(hot_tote, slot_no, checkin_list)
        wms.replenish.finish_replenish(slot_no, tote_no)

        # check inventory check in tote
        wms.wms_db.check_inventory(location_no=tote_no, item_number=oos_item.get("item_number", ""), quantity=oos_item["item_quantity"])

        # check order status
        wms.wms_db.check_shipping_status(order_id=order_id, status=50)

        # check tote status
        wms.wms_db.check_tote_status(warehouse=warehouse, tote_no=hot_tote, status=0)

        wms.wms_db.check_tote_status(warehouse=warehouse, tote_no=slot_no, status=0)

        wms.wms_db.check_tote_status(warehouse=warehouse, tote_no=tote_no, status=3)

        # 退出登录
        wms.logout(warehouse_number=warehouse, user_id=account, user_name=username)

        # 将order的状态置为结束，避免被其他业务使用
        wms.wms_db.update_order_ship_status(ship_status=70, order_id=order_id)


if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True, open=False)

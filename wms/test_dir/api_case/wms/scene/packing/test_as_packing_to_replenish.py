# !/usr/bin/python3
# -*- coding: utf-8 -*-

import weeeTest
from weeeTest import jmespath
from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms
import logging
import time, json, copy
from datetime import datetime
from wms.test_dir.api_case.wms.utils import DataUtils
from wms.test_dir.api.wms.replenish.replenish_api import ReplenishAPI



class TestAsPackingToReplenish(weeeTest.TestCase):

    util = DataUtils()
    replenish = ReplenishAPI()

    # @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_as_packing_to_replenish(self):
        """
        【112988】as packing转补单,补单流程转打包
        """
        warehouse = global_data.as_packing_to_replenish['warehouse']
        tote_no = global_data.as_packing_to_replenish['tote_no']
        order_id = global_data.as_packing_to_replenish['order_id']
        storage_type = global_data.as_packing_to_replenish['storage_type']
        hot_tote = global_data.as_packing_to_replenish['hot_tote']

        # 登录
        account, username = wms.wms_login.common_login()

        self.replenish.warehouse = warehouse

        # 如果tote中有库存清空
        location = [tote_no]
        wms.moving_api.clear_inv(warehouse,account,location)

        # 检查当前订单状态
        shipping_status = wms.wms_db.check_shipping_status(order_id=order_id)
        if shipping_status == 70:
            wms.wms_db.rollback_wait_packing(warehouse, order_id, tote_no)
        else:
            # 检查数据库中有无上次执行中断的补单数据,有则强制结束，订单状态变为待打包
            ret = wms.wms_db.select_replenish_order(order_id=order_id)
            if isinstance(ret, dict):
                wms.replenish.checkout_force_complete(ret["tote_no"], ret["slot_no"])

        # 执行打包操作
        oos_item,is_batch, items, in_dtm = wms.normal_packing_operation(warehouse, tote_no, order_id, storage_type, account, username, is_replenish=True)

        wms.normal_packing.query_replenish_quantity(tote_no, order_id)

        # 转补单
        wms.normal_packing.packing_replenish(tote_no, order_id, oos_item)

        # check order status
        wms.wms_db.check_shipping_status(order_id=order_id, status=42)

        # check tote status
        time.sleep(3)
        wms.wms_db.check_tote_status(warehouse=warehouse, tote_no=tote_no, status=3)
        # check inventory
        for item in items:
            if item["item_number"] != oos_item.get("item_number", ""):
                wms.wms_assert.check_non_batch_invenotry(warehouse, tote_no, item["item_number"], -item["quantity"], reference_no=order_id, in_dtm=in_dtm)
                if is_batch:
                    wms.wms_assert.check_batch_invenotry(warehouse, tote_no, item["item_number"], -item["quantity"], batch_no=item["batch_no"], reference_no=order_id, in_dtm=in_dtm)
            else:
                wms.wms_assert.check_non_batch_invenotry(warehouse, tote_no, item["item_number"], -item["quantity"], reference_no=order_id, inventory_type=[551, 801], in_dtm=in_dtm)
                if is_batch:
                    wms.wms_assert.check_batch_invenotry(warehouse, tote_no, item["item_number"], -item["quantity"], batch_no=item["batch_no"], reference_no=order_id, inventory_type=[551, 801], in_dtm=in_dtm)


        #查询订单delivery_dtm
        res1 = wms.wms_db.get_order_info(order_id=order_id,info=['shipping_status','delivery_dtm'])
        shipping_status = res1['shipping_status']
        delivery_dtm = res1['delivery_dtm']

        # 更新AutoStoreConfig开关、起始日期
        start_dtm = datetime.utcfromtimestamp(delivery_dtm)
        formatted_date = start_dtm.strftime('%Y-%m-%d')
        config_value = f"{{\"active\": true, \"agingOrderMaxTime\": 600, \"oneItemOneOrderVolumeLimit\": 1000, \"oneItemVolumeMaxLimit\": 2500, \"oneItemVolumeMinLimit\": 1, \"pickCartConfigList\": [{{\"count\": 4, \"size\": 10, \"weight\": 10}}, {{\"count\": 2, \"size\": 15, \"weight\": 15}}, {{\"size\": 25, \"count\": 0, \"weight\": 25}}, {{\"size\": 30, \"count\": 0, \"weight\": 30}}], \"startDeliveryDate\": \"{formatted_date}\", \"supplier\": \"AutoStore\", \"sendDetailIdDeliveryDate\": \"2000-11-29\", \"supportOneItem\": false, \"oosLockInventory\": true, \"sendSkipWaveIds\": [], \"cancelOrderSetKpiComplete\": false, \"restockCallActive\": true, \"mailOrderSupportOneItem\": true, \"mailOrderOneItemOneOrderVolumeLimit\": 1000, \"asItemCanMarkCondition\": {{\"max_sku_weight_lb\": 10, \"max_skuBox_weight_lb\": 40, \"max_skuBox_volume_in3\": 1000}}}}"
        wms.update_central_config(warehouse_number=warehouse,
                                  config_key="support_automation", config_value=config_value)
        # wms.wms_db.update_autostore_config(start_dtm=delivery_dtm,warehouse_number=warehouse_number)

        # replenish order check in
        self.replenish.check_tote(tote_no)
        slot_no = wms.wms_db.get_wh_storage_location_info(warehouse=warehouse, location_type=35, flag=0,
                                                          info='location_no')
        self.replenish.check_slot(slot_no, order_id)
        self.replenish.bind_slot(tote_no, slot_no)
        wms.wms_db.check_shipping_status(order_id=order_id, status=45)

        # 判断补单预占的是否是as库位
        wms.wms_db.check_inventory_log_location(warehouse=warehouse,reference_id=order_id, location="AUTOSTORE")

        # kpi拣货
        # 获取Pick List id
        pick_task = wms.wms_db.get_order_as_task(order_id=order_id,
                                            info=["rec_id", "pick_list_rec_id", "item_number", "status"])
        pick_list_id = pick_task[0]["pick_list_rec_id"]
        pisk_task_status = pick_task[0]["status"]

        # 获取task_detail表id，item
        pick_detail = wms.wms_db.get_order_as_task_detail(pick_list_id=pick_list_id,
                                                    info=['rec_id', 'item_number', 'batch_no', 'receive_dtm',
                                                          'expire_dtm', 'quantity'])

        # 发送校验，取消待发送订单前一发货日Dry MO订单
        before_delivery_dtm = delivery_dtm - 86400
        wms.wms_db.update_order(order_status=2, delivery_dtm=before_delivery_dtm, warehouse_number=warehouse)

        # 发送Pick list
        wms.autostore_picking.autoStoreJob()
        time.sleep(5)
        pick_list = wms.wms_db.get_order_as_list(pick_list_id=pick_list_id, info=["rec_id", "status"])
        pisk_list_status = pick_list[0]["status"]
        assert pisk_list_status == 10, "Pick List发送KPI失败"

        # 回调-绑定Container-参数整理--第一步
        # 给定的 JSON 数据模板
        json_data_template = {
            "version": "2.0.0",
            "action": "PickResult",
            "timestamp": "2024-12-22T02:58:13-08:00",
            "messageId": "3885630",
            "instanceId": "SayWeee",
            "params": {
                "pickListId": pick_list_id,
                "containerComplete": False,
                "outboundContainerId": None,
                "createdBy": "HOST",
                "hostCreated": True,
                "listComplete": False,
                "pickTaskInformation": [
                    {
                        "pickTaskId": None,
                        "pickTaskComplete": True,
                        "itemId": None,
                        "clientId": "TB1",
                        "pickTaskCompleteCode": "PICKED",
                        "compartmentInformation": [
                            {
                                "binId": "AS17172",
                                "compartmentId": "A1",
                                "fifoDate": None,
                                "expiryDate": None,
                                "group1": None,
                                "quantity": None,
                                "portId": 10,
                                "portType": "CAROUSEL",
                                "startDatetime": "2024-12-22T02:57:13-08:00",
                                "endDatetime": "2024-12-22T02:58:13-08:00",
                                "userId": "7390819",
                                "sortBarPositionId": "B1"
                            }
                        ]
                    }
                ]
            }
        }

        # 给定的列表数据
        data_list = pick_detail

        # 创建一个空的 JSON 数据列表
        json_data_list = []

        # 遍历列表数据，将每个元素的 rec_id 和 item_number 带入 JSON 数据模板中
        for data in data_list:
            # 深拷贝模板，以免修改原始模板
            current_json_data = copy.deepcopy(json_data_template)

            # 更新 JSON 数据中的 rec_id 和 item_number

            current_json_data['params']['pickTaskInformation'][0]['pickTaskId'] = data['rec_id']
            current_json_data['params']['pickTaskInformation'][0]['itemId'] = data['item_number']
            current_json_data['params']['pickTaskInformation'][0]['compartmentInformation'][0][
                'fifoDate'] = datetime.utcfromtimestamp(data['receive_dtm']).strftime('%Y-%m-%d')
            current_json_data['params']['pickTaskInformation'][0]['compartmentInformation'][0][
                'expiryDate'] = datetime.utcfromtimestamp(data['expire_dtm']).strftime('%Y-%m-%d')
            current_json_data['params']['pickTaskInformation'][0]['compartmentInformation'][0]['group1'] = data[
                'batch_no']
            current_json_data['params']['pickTaskInformation'][0]['compartmentInformation'][0]['quantity'] = data[
                'quantity']

            # 将更新后的 JSON 数据添加到列表中
            json_data_list.append(current_json_data)
            new_json_data_list = json.dumps(json_data_list, indent=4)
        # 回调绑定Container-回调
        for json_data in json_data_list:
            wms.autostore_picking.autoStoreApi(data=json_data)
            time.sleep(3)

        # 回调-PickResult-参数整理  第二步
        json_data_template1 = {
            "version": "2.0.0",
            "action": "PickResult",
            "timestamp": "2024-12-22T02:58:34-08:00",
            "messageId": "3885640",
            "instanceId": "SayWeee",
            "params": {
                "pickListId": pick_list_id,
                "containerComplete": True,
                "outboundContainerId": "1C308",
                "createdBy": "HOST",
                "hostCreated": True,
                "listComplete": False,
                "pickTaskInformation": [
                    {
                        "pickTaskId": None,
                        "pickTaskComplete": True,
                        "itemId": None,
                        "clientId": "TB1",
                        "pickTaskCompleteCode": "PICKED",
                        "compartmentInformation": [
                            {
                                "binId": "",
                                "compartmentId": "",
                                "quantity": None,
                                "portId": 10,
                                "portType": "CAROUSEL",
                                "startDatetime": "2024-12-22T02:58:34-08:00",
                                "endDatetime": "2024-12-22T02:58:34-08:00",
                                "userId": "7390819",
                                "sortBarPositionId": "B1"
                            }
                        ]
                    }
                ]
            }
        }

        # 给定的列表数据
        data_list1 = pick_detail

        # 创建一个空的 JSON 数据列表
        json_data_list1 = []

        # 遍历列表数据，将每个元素的 rec_id 和 item_number 带入 JSON 数据模板中
        for data in data_list1:
            # 深拷贝模板，以免修改原始模板
            current_json_data1 = copy.deepcopy(json_data_template1)

            # 更新 JSON 数据中的 rec_id 和 item_number

            current_json_data1['params']['pickTaskInformation'][0]['pickTaskId'] = data['rec_id']
            current_json_data1['params']['pickTaskInformation'][0]['itemId'] = data['item_number']
            current_json_data1['params']['pickTaskInformation'][0]['compartmentInformation'][0]['quantity'] = data[
                'quantity']

            # 将更新后的 JSON 数据添加到列表中
            json_data_list1.append(current_json_data1)
            new_json_data_list1 = json.dumps(json_data_list1, indent=4)
            # for json_data in json_data_list:
            #     print(json.dumps(json_data, indent=4))

        # 回调绑定Container-PickResult
        for json_data1 in json_data_list1:
            wms.autostore_picking.autoStoreApi(data=json_data1)
            time.sleep(3)

        # Pick Complete  第三步
        wms.autostore_picking.autoStoreApi(data={
            "version": "2.0.0",
            "action": "ListActivity",
            "timestamp": "2024-12-22T03:02:01-08:00",
            "messageId": "3885680",
            "instanceId": "SayWeee",
            "params": {
                "listId": pick_list_id,
                "listType": "PICK",
                "status": "COMPLETE",
                "userId": "7390819",
                "operationDatetime": "2024-12-22T03:02:01-08:00",
                "portType": "CAROUSEL",
                "portId": "10"
            }
        })

        time.sleep(2)
        # 检查kpi拣货是否回调成功
        pick_list1 = wms.wms_db.get_order_as_list(pick_list_id=pick_list_id, info=["rec_id", "status", "container_id"])
        pisk_list_status1 = pick_list1[0]["status"]
        pisk_list_container1 = pick_list1[0]["container_id"]
        assert pisk_list_status1 == 30, "kpi拣货回调失败"

        # replenish  pick
        # hot_tote = wms.wms_db.get_wh_storage_location_info(warehouse=warehouse, location_type=26, flag=0,
        #                                                    info='location_no')
        hot_tote_status = wms.wms_db.check_tote_status(warehouse=warehouse,tote_no=hot_tote)
        if hot_tote_status == 3:
            wms.wms_db.update_tote_status(warehouse_number=warehouse, toteNo = hot_tote)

        wait_picking_list = self.replenish.get_pick_list()
        self.replenish.scan_hot_tote(hot_tote)
        self.replenish.update_as_container_status(pisk_list_container1)
        self.replenish.as_pick_confirm(wait_picking_list, hot_tote)

        # replenish item check in
        hot_pick_list = self.replenish.query_hot_pick_tote()["body"]
        assert hot_tote in list(map(lambda h: h["tote_no"], hot_pick_list)), f"{hot_tote}不在待check in的pick tote列表中"
        self.replenish.scan_hot_pick_tote(hot_tote)
        checkin_list = self.replenish.get_checkin_list(hot_tote)["body"]
        self.replenish.item_checkin_confirm(hot_tote, slot_no, checkin_list)
        self.replenish.finish_replenish(slot_no, tote_no)

        # check inventory check in tote
        wms.wms_db.check_inventory(location_no=tote_no, item_number=oos_item.get("item_number", ""), quantity=oos_item["item_quantity"])

        # check order status
        wms.wms_db.check_shipping_status(order_id=order_id, status=50)

        # check tote status
        time.sleep(3)
        wms.wms_db.check_tote_status(warehouse=warehouse, tote_no=hot_tote, status=0)

        wms.wms_db.check_tote_status(warehouse=warehouse, tote_no=slot_no, status=0)

        wms.wms_db.check_tote_status(warehouse=warehouse, tote_no=tote_no, status=3)

        # 退出登录
        wms.logout(warehouse_number=warehouse, user_id=account, user_name=username)

        # 将order的状态置为结束，避免被其他业务使用
        wms.wms_db.update_order_ship_status(ship_status=70, order_id=order_id)
        wms.wms_db.update_location_status(warehouse=warehouse, location_no=hot_tote, flag=3)

if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True, open=False)

# !/usr/bin/python3
# -*- coding: utf-8 -*-

import weeeTest
from weeeTest import jmespath

from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms


class TestOneItemPacking(weeeTest.TestCase):

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_one_item_outbound(self):
        """
        【112829】one item打包流程
        """
        # 登录
        account, username = wms.wms_login.common_login()

        # 初始化数据
        warehouse = global_data.one_item_packing['warehouse']
        cart_no = global_data.one_item_packing['cart_no']
        order_id = global_data.one_item_packing['order_id']
        storage_type = global_data.one_item_packing['storage_type']

        # check batch
        is_batch = wms.get_inventory_batch_switch(warehouse, storage_type)
        # 获取操作时间
        in_dtm = wms.util.get_current_time()

        warehouse_time = wms.get_central_config(warehouse, "wms:order:delivery_date")
        print(type(warehouse_time))
        print(warehouse_time)
        if warehouse_time is None:
            warehouse_time = wms.util.utc_current_day()
        else:
            warehouse_time = wms.util.day_to_utc(warehouse_time)
        wms.wms_db.rollback_wait_packing(warehouse, order_id, cart_no, warehouse_time)

        # 设置仓库
        wms.util.update_header(weee_warehouse=str(warehouse))
        # 设置storage_type
        wms.util.update_header(weee_wms_storage_type=str(storage_type))

        # 查询cart 下待打包的订单
        cart_data = wms.wms_db.select_cart_orders(cart_no=cart_no, warehouse=warehouse)
        if isinstance(cart_data, dict):
            cart_data = [cart_data]

        # 开始打包
        wms.normal_packing.user = username + '(' + account + ')'
        wms.normal_packing.warehouse = warehouse

        resp = wms.normal_packing.query_packing_task(cart_no)
        wms.util.update_header(weee_wms_packing_type=str(jmespath(resp, "body.packing_type")))

        ret = wms.normal_packing.query_packing_info(cart_no)
        waiting_packing_order_num = ret["waitingPackingOrderNum"]
        weeeTest.log.info(f'打包台查出{cart_no}中待打包订单数量:{waiting_packing_order_num}')

        for order in cart_data:
            items, order_id, recommend_package = wms.normal_packing.one_item_get_order(cart_no, order["upc"])
            # 当打包Tote/cart中库存数据异常时，恢复库存
            if wms.adjust_api.query_location_inv_list(warehouse, cart_no) == []:
                for item in items:
                    if isinstance(item, dict):
                        wms.adjust_api.create_batch_inv(item["item_number"], warehouse, username + '(' + account + ')',
                                                        cart_no, item["item_quantity"])
                    if is_batch:
                        item["batch_no"] = wms.wms_db.get_batch_inventory_transaction(warehouse, cart_no, item["item_number"])["batch_no"]
            if order["item_quantity"] > 1:
                wms.normal_packing.packing_qc(items, order_id)
            wms.normal_packing.scan_box(recommend_package, order_id)
            wms.normal_packing.label_create(order_id)
            wms.normal_packing.normal_ship(cart_no, order_id)
            wms.normal_packing.one_item_complete(cart_no)

            # check inventory
            for item in items:
                wms.wms_assert.check_non_batch_invenotry(warehouse, cart_no, item["item_number"],
                                                         -item["item_quantity"],
                                                         reference_no=order_id,
                                                         inventory_type=[261], in_dtm=in_dtm)
                if is_batch:
                    wms.wms_assert.check_batch_invenotry(warehouse, cart_no, item["item_number"],
                                                         -item["item_quantity"],
                                                         batch_no=item["batch_no"],
                                                         reference_no=order_id, inventory_type=[261],
                                                         in_dtm=in_dtm)

        # check order status
        for order in cart_data:
            wms.wms_db.check_shipping_status(order_id=order["order_id"], status=70)

        # check cart status
        wms.wms_db.check_tote_status(warehouse=warehouse, tote_no=cart_no, status=0)

        #check report TMS tracking number
        for order in cart_data:
            order_info = wms.wms_db.select_wms_order_info(order_id=order["order_id"])
            wms.wms_assert.check_hook_report(order_info["source_order_id"], "TMS_PACKAGE", in_dtm)

        # 退出登录
        wms.logout(warehouse_number=warehouse, user_id=account, user_name=username)

        # 将订单的delivery_dtm恢复
        wms.wms_db.update_order_delivery_dtm(order_id, **********)

        # 将cart的状态置为占用，避免被其他业务使用
        wms.wms_db.update_location_status(warehouse, cart_no, 3)


if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True, open=False)

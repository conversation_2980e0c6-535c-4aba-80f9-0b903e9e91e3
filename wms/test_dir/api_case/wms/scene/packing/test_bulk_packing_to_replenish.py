# !/usr/bin/python3
# -*- coding: utf-8 -*-

import weeeTest
from weeeTest import jmespath

from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms


class TestBulkPackingToReplenish(weeeTest.TestCase):

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_bulk_packing_to_replenish(self):
        """
        【112836】Bulk打包转补单流程
        """
        warehouse = global_data.bulk_packing_to_replenish['warehouse']
        cart_no = global_data.bulk_packing_to_replenish['cart_no']
        order_id = global_data.bulk_packing_to_replenish['order_id']
        storage_type = global_data.bulk_packing_to_replenish['storage_type']
        # 登录
        account, username = wms.wms_login.common_login()
        # 设置仓库
        wms.util.update_header(weee_warehouse=str(warehouse))
        # 设置storage_type
        wms.util.update_header(weee_wms_storage_type=str(storage_type))
        # check batch
        is_batch = wms.get_inventory_batch_switch(warehouse, storage_type)

        # 检查当前订单状态
        shipping_status = wms.wms_db.check_shipping_status(order_id=order_id[0])
        if shipping_status == 70:
            wms.wms_db.rollback_wait_packing(warehouse, order_id, cart_no)
        else:
            # 检查数据库中有无上次执行中断的补单数据,有则强制结束，订单状态变为待打包
            ret = wms.wms_db.select_replenish_order(order_id=order_id)
            if isinstance(ret, dict):
                wms.replenish.checkout_force_complete(ret["tote_no"], ret["slot_no"])

        # 开始打包
        wms.bulk_packing.user = username + '(' + account + ')'
        wms.bulk_packing.warehouse = warehouse
        # 获取打包任务类型,更新header
        resp = wms.bulk_packing.query_packing_task(cart_no)
        wms.util.update_header(weee_wms_packing_type=str(jmespath(resp, "body.packing_type")))
        items = wms.bulk_packing.query_bulk_packing_info(cart_no)

        # 当打包Tote/cart中库存数据异常时，恢复库存
        if wms.adjust_api.query_location_inv_list(warehouse, cart_no) == []:
            for item in items:
                if isinstance(item, dict):
                    wms.adjust_api.create_batch_inv(item["item_number"], warehouse, username + '(' + account + ')',
                                                    cart_no, item["item_quantity"])
                if is_batch:
                    item["batch_no"] = wms.wms_db.get_batch_inventory_transaction(warehouse, cart_no, item["item_number"])["batch_no"]


        # 校验订单状态(shipping_status=60)
        shipping_status = wms.wms_db.check_shipping_status(order_id=order_id[0])
        assert shipping_status in (60, 61), "未更新wh_order_info表的shipping_status为开始打包状态"

        # QC 时OOS
        oos_item = wms.bulk_packing.bulk_oos_qc(items, cart_no)
        wms.bulk_packing.bulk_complete(cart_no, oos_item)

        # 查询可用的tote转补单
        tote_no = wms.wms_db.get_wh_storage_location_info(warehouse=warehouse, location_type=6, flag=0,
                                                              info='location_no')
        wms.bulk_packing.bulk_scan_replenishtote(cart_no,replenish_tote_no=tote_no)

        # check order status
        shipping_status = wms.wms_db.check_shipping_status(order_id=order_id[0])
        assert shipping_status == 42 , "未更新wh_order_info表的shipping_status为等待补单状态"
        # check tote status
        wms.wms_db.check_tote_status(warehouse=warehouse, tote_no=tote_no, status=3)
        wms.wms_db.check_tote_status(warehouse=warehouse, tote_no=cart_no, status=0)

        # check tote/cart inventory empty
        wms.wms_assert.check_location_empty(warehouse, cart_no)

        # 补单流程
        # replenish order check in
        wms.replenish.check_tote(tote_no)
        slot_no = wms.wms_db.get_wh_storage_location_info(warehouse=warehouse, location_type=35, flag=0,
                                                          info='location_no')
        wms.replenish.check_slot(slot_no, order_id[0])
        wms.replenish.bind_slot(tote_no, slot_no)
        wms.wms_db.check_shipping_status(order_id=order_id[0], status=45)
        # replenish force complete
        wms.replenish.checkout_force_complete(tote_no, slot_no)

        # check order status
        wms.wms_db.check_shipping_status(order_id=order_id[0], status=51)
        # check tote status
        wms.wms_db.check_tote_status(warehouse=warehouse, tote_no=slot_no, status=0)
        wms.wms_db.check_tote_status(warehouse=warehouse, tote_no=tote_no, status=3)
        # 退出登录
        wms.logout(warehouse_number=warehouse, user_id=account, user_name=username)
        # 将order的状态置为结束，避免被其他业务使用
        wms.wms_db.update_order_ship_status(ship_status=70, order_id=order_id[0])
        wms.wms_db.update_location_status(warehouse, cart_no, 3)
        # 还原bulk 补单数据
        wms.wms_db.update_pick_order(order_id=order_id[0])
        wms.wms_db.update_location_status(warehouse, tote_no, 0)

if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True, open=False)
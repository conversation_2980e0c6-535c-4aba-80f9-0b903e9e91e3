# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  Author
@Version        :  V1.0.0
------------------------------------
@File           :  test_global_fbw.py.py
@Description    :  
@CreateTime     :  2024/12/26 16:44
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2024/12/26 16:44
"""
import random
import time
from datetime import datetime, timedelta
import weeeTest
from weeeTest import jmespath

from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms
from wms.test_dir.api_case.wms.utils import DataUtils


class TestglobalFbw(weeeTest.TestCase):
    def setup_class(self):
        self.rep_info = globals()
        tomorrow = datetime.now() + timedelta(days=1)
        self.rep_info['date'] = time.strftime('%Y-%m-%d', tomorrow.timetuple())
        self.rep_info['item_number'] = global_data.IOitem_number['item_Number']
        self.rep_info['user_id'], self.rep_info['user_name'] = wms.wms_login.common_login()
    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_global_order(self):
        """
        【112930】下globalfbw订单 非regression
        """

        # 下单
        self.rep_info['referenceno']=wms.globalfbw.mkplcreateinboundorder(delivery_date=self.rep_info['date'],item_number=self.rep_info['item_number'])
        ref=wms.wms_db.get_inbound_order_info(self.rep_info['referenceno'])
        assert ref["reference_no"] == str(self.rep_info['referenceno']),"创建订单失败"
        #下发lpn
        wms.globalfbw.mkplcreatelpn(refenceno=self.rep_info['referenceno'])
        reflpn=wms.wms_db.get_lpn_info(reference_no=self.rep_info['referenceno'])
        assert reflpn[0]["reference_no"] == str(self.rep_info['referenceno']),"下发lpn失败"
    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_Wrong_collection_writeoff(self):
        """
        【112931】 错收冲减 regression
        """
        res = wms.label_receive.query_receive_list(po_number=self.rep_info['referenceno'], status=1, pageSize=1,
                                                   warehouse_number="25")
        per_pack = jmespath(res, f"poItemList[0].pieces_per_pack")
        expired_dateunix = jmespath(res, f"poItemList[0].expired_dtm")
        # 收货详情
        res = wms.label_receive.label_receive_detail(po_number=self.rep_info['referenceno'],
                                                     item_number=self.rep_info['item_number'],
                                                     expire_dtm=expired_dateunix, receive_dtm=None,
                                                     pieces_per_pack=per_pack)
        lpnInfoList = []
        lpnno = jmespath(res, f"lpnInfoList[0].lpn_no")
        original_quantity = jmespath(res, f"lpnInfoList[0].original_quantity")
        lpnInfoList.append({"lpn_no": lpnno, "original_quantity": original_quantity})
        # 扫描lpn
        wms.label_receive.label_receive_scan_lpn(warehouse_number="25", lpn_no=lpnno, in_user=self.rep_info['user_id'],reference_no=self.rep_info['referenceno'],item_number=self.rep_info['item_number'])

        rec_qty = jmespath(res, "po_lpn_qty")
        expired_date = jmespath(res, "expire_dtm")
        # 收货确认
        resconfrim = wms.label_receive.label_receive_confirm(warehouse_number="25",
                                                             po_number=self.rep_info['referenceno'],
                                                             item_number=self.rep_info['item_number'], rec_qty=rec_qty,
                                                             pieces_per_pack=per_pack, qc_ExpiredDate=expired_date,
                                                             in_user=self.rep_info['user_id'], lpnInfoList=lpnInfoList)
        rec_id = jmespath(resconfrim, "rec_id")
        wms.label_receive.label_receive_print_label(warehouse_number="25", inbound_batch_id=rec_id,
                                                    in_user=self.rep_info['user_id'])

        #进入done列表
        res=wms.label_receive.query_receive_list(po_number=self.rep_info['referenceno'], status=2, warehouse_number="25")
        #进行错收冲减
        wms.label_receive.label_receive_delete_batch(warehouse_number="25", inbound_batch_id=jmespath(res, "batchList[0].rec_Id"), po_number=self.rep_info['referenceno'], in_user=self.rep_info['user_id'])
        order_info = wms.wms_db.get_inbound_order_info(self.rep_info['referenceno'])
        assert order_info["status"] == 0, "错收冲减失败"

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_label_receive(self):
        """
        【110940 】label receive收货 regression
        """
        wms.labelreceive_operation(po_number=self.rep_info['referenceno'],warehouse_number="25",userid=self.rep_info['user_id'])

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_global_putaway(self):
        '''
        【110946】 global fbw上架 regression
        '''
        wms.putaway_operation(putaway_warehouse_no="25", po_number=self.rep_info['referenceno'],
                              item_number=self.rep_info['item_number'],storage_type=1, type=0)

if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net",debug=True, open=False)
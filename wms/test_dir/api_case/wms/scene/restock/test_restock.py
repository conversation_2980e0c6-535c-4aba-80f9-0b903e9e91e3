# !/usr/bin/python3
# -*- coding: utf-8 -*-

import weeeTest

from wms.test_dir.api.wms.wms import wms


class TestRestock(weeeTest.TestCase):
    def test_restock_process(self):
        """
        Restock pick、binload流程
        """
        # 登录
        account, user_name = wms.wms_login.common_login()

        # 获取有补货的任务的仓库
        res = wms.wms_db.get_wh_restock_task_info()

        warehouse = res['warehouse_number']
        storage_type = res['storage_type']
        task_qty = res['task_qty']
        if task_qty < 1:
            return

        # 获取可用pallet
        pallet = wms.wms_db.get_wh_storage_location_info(warehouse=warehouse, location_type=38, flag=0,
                                                         info='location_no')

        # 设置仓库
        wms.util.update_header(weee_warehouse=str(warehouse))
        # 设置storage_type
        wms.util.update_header(weee_wms_storage_type=str(storage_type))

        # 补货接口
        wms.restock.storage_type = str(storage_type)
        wms.restock.warehouse = warehouse
        wms.restock.userid = account
        wms.restock.username = user_name
        wms.restock.user = user_name + '(' + str(account) + ')'

        # 查询当前用户有无未做完的任务
        task = wms.restock.get_tasks(action=0)
        if not task:
            # 查询当前仓库可补货任务数量
            body = wms.restock.pick_summary()
            pick_qty = {"groundPick": body["groundPickCount"],
                        "airPick": body["airPickCount"],
                        "fullPallet": body["fullPalletPickCount"]}

            # 拉取补货任务
            restock_type = {"groundPick": "1", "airPick": "2", "fullPallet": "3"}
            pick_type = restock_type[max(pick_qty, key=lambda key: pick_qty[key])]
            task = wms.restock.get_tasks(pick_type=pick_type)

        # 扫描拣货pallet
        wms.restock.scan_pallet(pallet)

        # scan stock location
        task_id = task[0]["rec_id"]
        stock_location = task[0]["stock_location"]
        recommend_box = task[0]["recommend_box"]
        recommend_qty = task[0]["recommend_qty"]

        if task[0]["lpnNo"] is not None:
            lpn_list = wms.restock.scan_lpn_location(task_id, stock_location)["body"]["lpns"]
            pick_lpn = []
            pick_qty = 0
            for lpn in lpn_list:
                if pick_qty + lpn["quantity"] <= recommend_qty:
                    pick_lpn.append(lpn["lpnNo"])
                else:
                    break
            # pick confirm
            wms.restock.pick_confirm(task_id, stock_location, recommend_box, recommend_qty, pallet, lpn_flag=True,
                                     lpn_nos=pick_lpn)
            # 增加校验pallet与商品绑定
            restult=wms.wms_db.get_restock_pallet_item(task_id)
            assert restult['pallet_no']==pallet
            assert restult['status'] == 1

        else:
            wms.restock.scan_location(task_id, stock_location)
            # pick confirm
            wms.restock.pick_confirm(task_id, stock_location, recommend_box, recommend_qty, pallet)
            # 增加校验pallet与商品绑定
            restult=wms.wms_db.get_restock_pallet_item(task_id)
            assert restult['pallet_no']==pallet
            assert restult['status'] == 1

        # pick complete, release task
        wms.restock.pick_complete(pallet)
        #增加校验restockpick完成状态更新
        restult = wms.wms_db.get_restock_pallet_item(task_id)
        assert restult['status'] == 2
        restulttask=wms.wms_db.get_restock_task_info(task_id)
        assert restulttask['status'] == 2


        # bin load scan pallet
        bin_load_task = wms.restock.bin_load_list(pallet)["list"]
        task_id = bin_load_task[0]["rec_id"]
        bin_location = bin_load_task[0]["bin_sn"]
        upc = bin_load_task[0]["upc"]
        real_take_box = bin_load_task[0]["real_take_box"]
        real_take_qty = bin_load_task[0]["real_take_qty"]
        is_lpn_task = bin_load_task[0]["isLpnTask"]
        item_number = bin_load_task[0]["item_number"]
        action = None

        # scan upc
        slotting_logic_list = wms.restock.bin_load_list(pallet, search_code=upc)["slottingLogicList"]

        # 进入details 页面
        if bin_location is None:
            if slotting_logic_list is not None:
                bin_location = slotting_logic_list[0]["locationNO"]
            else:
                stock_list = wms.restock.no_bin_get_stock(item_number, task_id)
                bin_location = stock_list[0]
                action = 4

        if is_lpn_task:
            can_scan_lpn_list = wms.restock.lpn_bin_load_detail(task_id, pallet, bin_location, action)["canScanLpnList"]
            can_scan_lpn_list = list(map(lambda i: i["lpnNo"], can_scan_lpn_list))
            # binload confirm
            if action is None:
                action = 0
            wms.restock.lpn_bin_load_confirm(task_id, pallet, bin_location, can_scan_lpn_list, action)
        else:
            wms.restock.bin_load_get_detail(task_id, pallet, bin_location, action)

            # binload confirm
            wms.restock.bin_load_confirm(task_id, pallet, bin_location, real_take_box, real_take_qty, action)

        # binload complete
        wms.restock.bin_load_complete(pallet)

        #增加binload后pallet_item状态
        restult = wms.wms_db.get_restock_pallet_item(task_id)
        assert restult['status'] == 3


        # check pallet status
        wms.wms_db.check_tote_status(tote_no=pallet, warehouse=warehouse, status=0)

        # check task status
        assert wms.wms_db.get_restock_task_info(task_id=task_id)["status"] == 3

        # 退出登录
        wms.wms_login.logout(warehouse_number=warehouse, user_id=account, user_name=user_name)


if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True, open=False)

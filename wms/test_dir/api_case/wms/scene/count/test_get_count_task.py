import weeeTest
from wms.test_dir.api_case.wms.simple.cycle_count.simple_count_api import Count<PERSON><PERSON>
from wms.test_dir.api.wms.wms import wms


class TestGetTask(weeeTest.TestCase):

	@weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
	def test_get_bin_task(self):
		"""
		【111589】领取bin库位的任务
		"""
		# 登录
		user_id, user_name = wms.wms_login.common_login()
		edit_user = user_name + '(' + user_id + ")"  # 拼接in_user
		# 调用领取bin库位任务方法
		CountApi().get_bin_task(edit_user)

	@weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
	def test_get_stock_task(self):
		"""
		【111590】领取Stock库位的高层任务
		"""
		# 登录
		user_id, user_name = wms.wms_login.common_login()
		edit_user = user_name + '(' + user_id + ")"  # 拼接in_user
		# 调用领取stock库位任务方法
		CountApi().get_stock_task(edit_user)

	@weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
	def test_release_task(self):
		"""
		【111592】盘点任务释放
		"""
		# 登录
		user_id, user_name = wms.wms_login.common_login()
		edit_user = user_name + '(' + user_id + ")"  # 拼接in_user
		# 调用释放任务方法
		CountApi().release_task(edit_user)


if __name__ == '__main__':
	weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True, open=True)

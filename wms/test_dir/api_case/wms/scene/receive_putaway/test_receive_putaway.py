# !/usr/bin/python3
# -*- coding: utf-8 -*-

import weeeTest
import json

from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms


class TestCentralReceiveAPI(weeeTest.TestCase):

    def setup_class(self):
        # 相关接口需要的信息
        self.rep_info = globals()  # globals()返回包含当前作用域全局变量的字典。

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_receive(self):
        """
        [112679]普通商品收货流程
        """
        # 登录
        account, username = wms.wms_login.common_login()
        self.rep_info["userid"] = account  # rep_info={"userid"：account，"username"：username}
        self.rep_info["username"] = username

        # 获取仓库和po号
        self.rep_info['warehouse_number'] = global_data.receive_info['warehouse_number']
        self.rep_info['po_number'] = global_data.receive_info['reference_no']
        self.rep_info['item_number'] = global_data.receive_info['item_number']

        # 获取商品的storage_type
        storage_type = wms.get_storage_type(self.rep_info['item_number'])

        # 获取仓库support_lpn的config_value
        config_value = wms.wms_db.select_wh_config_by_key(self.rep_info['warehouse_number'], "support_lpn")
        print(f"--------------------------------{config_value}")

        # 检查指定存储类型的值是否为 false
        is_false = config_value.get(storage_type, None)
        print(f"--------------------------------{is_false}")
        # 根据support_lpn 配置判断是否需要调用接口打开仓库lpn配置并打印结果
        if not is_false:
            print(f"{storage_type} 的support_lpn 配置 是 false，调用接口打开仓库lpn配置")
            # 打开仓库LPN配置，
            wms.update_central_config(self.rep_info['warehouse_number'], "support_lpn",
                                      "{ \"dry\": true,\"fresh\": true,\"frozen\": true}")
        else:
            pass
            print(f"{storage_type} 的support_lpn 配置 是 true.")

        # user赋值
        wms.central_receive.user = username + '(' + account + ')'
        wms.put_away.account = account
        wms.put_away.user = username + '(' + account + ')'

        # 模拟用户进入Receive模块(逻辑上需要比较用户进入模块与业务单历史作业模块相同)
        wms.common_api.record_moudle_log(self.rep_info['warehouse_number'], 'receive')

        # 收货
        # 执行收货操作
        wms.receive_operation(self.rep_info['warehouse_number'], self.rep_info['po_number'])

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_putaway(self):
        """
        [112982]普通商品上架流程
        """
        # 获取上架warehouse_number、location_no、sku
        self.rep_info['putaway_warehouse_no'] = global_data.putaway_info['warehouse_number']
        self.rep_info['location_no'] = global_data.putaway_info['location_no']
        self.rep_info['item_number'] = global_data.putaway_info['item_number']
        self.rep_info['po_number'] = global_data.receive_info['reference_no']

        wms.putaway_operation(self.rep_info['putaway_warehouse_no'], self.rep_info['po_number'],
                              self.rep_info['item_number'], type=0)


if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net",
                  case_list=["test_receive_putaway.py::TestCentralReceiveAPI"], debug=True, open=False,
                  ext=['-m', 'WMS', "--junitxml=tests/results.xml"])

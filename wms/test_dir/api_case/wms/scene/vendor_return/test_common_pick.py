#!/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  hao.fan
@Version        :  V1.0.0
------------------------------------
@File           :  test_common_pick.py
@Description    :  通用拣货流程测试用例
@CreateTime     :  2024/8/8 17:01
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2024/8/8 17:01
"""
import weeeTest
from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms

class TestCommonPick(weeeTest.TestCase):
    """通用拣货流程测试用例"""
    
    def setup_class(self):
        self.rep_info = globals()
        self.rep_info['warehouse_number'] = global_data.vendor_order['warehouse_number']
        self.rep_info['storage_type'] = global_data.vendor_order['storage_type']
        self.rep_info['user_id'], self.rep_info['user_name'] = wms.wms_login.common_login()
        self.rep_info['equipment_code'] = "LM-RT-11"
        self.rep_info['pallet_code'] = "PLT0689"
        self.rep_info['biz_type'] = 70

    # @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_scan_equipment(self):
        '''
        测试扫描设备功能
        '''
        scan_equipment_resp = wms.common_pick.scan_Equipment(
            warehouseNumber=self.rep_info['warehouse_number'],
            storageType=self.rep_info['storage_type'],
            userId=self.rep_info['user_id'],
            Equipment_code=self.rep_info['equipment_code']
        )
        self.rep_info['task_rec_id'] = scan_equipment_resp["taskDispatchList"][0]["bizTaskId"]
        self.rep_info['location_no'] = scan_equipment_resp["taskDispatchList"][0]["locationNo"]

    # @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_scan_pallet(self):
        '''
        测试扫描托盘功能
        '''
        scan_pallet_resp = wms.common_pick.scan_pallet(
            bizType=self.rep_info['biz_type'],
            warehouseNumber=self.rep_info['warehouse_number'],
            storageType=self.rep_info['storage_type'],
            userId=self.rep_info['user_id'],
            pallet_code=self.rep_info['pallet_code']
        )
        self.rep_info['biz_order_id'] = scan_pallet_resp["bizOrderId"]

    # @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_get_upc_pick_detail(self):
        '''
        测试获取UPC拣货详情功能
        '''
        pick_detail_resp = wms.common_pick.get_upc_pick_detail(
            warehouseNumber=self.rep_info['warehouse_number'],
            taskRecId=self.rep_info['task_rec_id'],
            locationNo=self.rep_info['location_no'],
            bizType=self.rep_info['biz_type'],
            bizOrderId=self.rep_info['biz_order_id'],
            pallet_code=self.rep_info['pallet_code'],
            userId=self.rep_info['user_id']
        )
        self.rep_info['item_number'] = pick_detail_resp["itemNumber"]
        self.rep_info['picked_quantity'] = pick_detail_resp["recommendTotalQty"]

    # @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_confirm_upc_pick(self):
        '''
        测试确认UPC拣货功能
        '''
        confirm_pick_resp = wms.common_pick.confirm_upc_pick(
            item_number=self.rep_info['item_number'],
            picking_action=0,
            picked_quantity=self.rep_info['picked_quantity'],
            warehouse_number=self.rep_info['warehouse_number'],
            task_rec_id=self.rep_info['task_rec_id'],
            location_no=self.rep_info['location_no'],
            biz_type=self.rep_info['biz_type'],
            biz_order_id=self.rep_info['biz_order_id'],
            user_id=self.rep_info['user_id'],
            picking_pallet_no=self.rep_info['pallet_code']
        )

    # @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_pick_complete(self):
        '''
        测试拣货完成功能
        '''
        complete_resp = wms.common_pick.pick_complete(
            warehouse_number=self.rep_info['warehouse_number'],
            picking_pallet_no=self.rep_info['pallet_code'],
            storage_type=self.rep_info['storage_type'],
            user_id=self.rep_info['user_id'],
            type=self.rep_info['biz_type'],
            biz_order_id=self.rep_info['biz_order_id']
        )
        self.rep_info['slot_no'] = complete_resp["slotNo"]

    # @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_scan_slot(self):
        '''
        测试扫描货位功能
        '''
        scan_slot_resp = wms.common_pick.scan_slot(
            slotNo=self.rep_info['slot_no'],
            warehouseNumber=self.rep_info['warehouse_number'],
            storageType=self.rep_info['storage_type'],
            userId=self.rep_info['user_id'],
            type=self.rep_info['biz_type'],
            bizOrderId=self.rep_info['biz_order_id'],
            pickingPalletNo=self.rep_info['pallet_code']
        )

if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True, open=False,case_list=['test_common_pick.py::TestCommonPick::test_scan_equipment'])

# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  Author
@Version        :  V1.0.0
------------------------------------
@File           :  test_return_order.py
@Description    :
@CreateTime     :  2024/8/15 15:54
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2024/8/15 15:54
"""
import random
import time

import weeeTest
from weeeTest import jmespath

from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms


class Testreturnorder(weeeTest.TestCase):

    def setup_class(self):
        self.rep_info = globals()

        self.rep_info['warehouse_number'] = global_data.vendor_order['warehouse_number']
        self.rep_info['storage_type'] = global_data.vendor_order['storage_type']
        self.rep_info['itemNumber1']=global_data.item_number['item_Number1']
        self.rep_info['current_time'] = time.strftime("%Y-%m-%d", time.localtime())
        self.rep_info['pallet'] = wms.wms_db.get_wh_vendor_storage_location_info(warehouse=self.rep_info['warehouse_number'], location_type=38, flag=0,
                                                         info='location_no')
        self.rep_info['vrppallet'] = wms.wms_db.get_wh_vendor_storage_location_info(warehouse=self.rep_info['warehouse_number'], location_type=80, flag=0,
                                                         info='location_no')
        self.rep_info['slotNo']=wms.wms_db.get_wh_vendor_storage_location_info(warehouse=self.rep_info['warehouse_number'], location_type=39, flag=0,
                                                         info='location_no')
        self.rep_info['orderNOcreate'] = random.randint(1, 1000000)
        self.rep_info['user_id'], self.rep_info['user_name'] = wms.wms_login.common_login()

    # @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_order_cancel(self):
        '''
        测试取消订单的功能
        '''
        orderNOcancel = random.randint(1, 1000000)
        wms.return_order.createOrUpdate(orderNO=orderNOcancel,pickUpDate=self.rep_info['current_time'],deliveryType=1,itemNumber1=self.rep_info['itemNumber1'])
        wms.return_order.cancelorder(orderNO=orderNOcancel)
        return_no = wms.wms_db.get_vendor_order(orderNOcancel)
        assert return_no['status'] == 100,"订单未正常取消"

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def _order_create(self):
        '''
        测试创建订单的功能
        '''
        wms.return_order.createOrUpdate(orderNO=self.rep_info['orderNOcreate'],pickUpDate=self.rep_info['current_time'],deliveryType=1,itemNumber1=self.rep_info['itemNumber1'])
        return_no=wms.wms_db.get_vendor_order(self.rep_info['orderNOcreate'])
        orderitem=wms.wms_db.get_vendor_orderitem(self.rep_info['orderNOcreate'])
        assert return_no['return_no'] == str(self.rep_info['orderNOcreate']),"无创建订单数据"
        assert orderitem['item_number'] == self.rep_info['itemNumber1'],"无创建订单商品数据"

    # @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def _order_preoccupy(self):
        '''
        测试预占库存的功能
        '''
        #     # 开始预占订单
        wms.return_order.startpreoccupy(orderNO=self.rep_info['orderNOcreate'])

        # 获取预占结果
        preoccupyresult = wms.return_order.getpreoccupyresult(orderNO=self.rep_info['orderNOcreate'])
        hastask = jmespath(preoccupyresult, "hasTask")
        if hastask == True:
            # check封装 校验预占生成任务
            time.sleep(1)
            count = wms.wms_db.get_batch_preoccupancy_inventory(reference_no=self.rep_info['orderNOcreate'])
            time.sleep(1)
            self.rep_info['taskdb'] = wms.wms_db.get_commonpick_task(self.rep_info['orderNOcreate'])
            assert len(count) == len(self.rep_info['taskdb']),"预占生成任务数量与预占数量不一致"
        else:
            # 取消订单
            wms.return_order.cancelorder(orderNO=self.rep_info['orderNOcreate'])
            return_no = wms.wms_db.get_vendor_order(self.rep_info['orderNOcreate'])
            assert return_no['status'] == 100,"订单状态异常"

    # @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def _order_pickselfpickup(self):
        '''
        测试拣货的功能Self Pickup
        '''
        # 调整任务优先级
        wms.common_pick.adjust_priority(task_rec_id=self.rep_info['taskdb'][0]['rec_id'], priority=99999999999999, warehouseNumber=self.rep_info['warehouse_number'])

        #开始拣货
        self._scan_equipment_flow()





        # for i in range(len(self.rep_info['taskdb'])):
        #     workType = self.rep_info['taskdb'][i]['work_type']
        #     itemLevel = self.rep_info['taskdb'][i]['item_level']
        #     location = self.rep_info['taskdb'][i]['location_no']
        #     recid = self.rep_info['taskdb'][i]['rec_id']
        #     pickway = self.rep_info['taskdb'][i]['picking_way']
        #     # 获取退货拣选任务列表，参数为1表示获取未扫pallet的任务列表
        #     wms.return_pick.gettasklist(action=1, workType=workType, warehouseNumber=self.rep_info['warehouse_number'],
        #                                 userId=self.rep_info['user_id'])
        #
        #     # 扫好货or坏货pallet
        #     if itemLevel == 1:
        #         wms.return_pick.checkpallet(pallet_no=self.rep_info['pallet'], orderid=self.rep_info['orderNOcreate'],
        #                                     itemLevel=itemLevel, workType=workType, userId=self.rep_info['user_id'])
        #         # 获取退货拣选任务列表，参数为0表示获取已扫pallet的任务列表
        #         wms.return_pick.gettasklist(action=0, workType=workType,
        #                                     warehouseNumber=self.rep_info['warehouse_number'], userId=self.rep_info['user_id'])
        #         taskdb = wms.wms_db.get_vendor_task(self.rep_info['orderNOcreate'])
        #         assert taskdb[i]['picking_pallet_no'] == self.rep_info['pallet'],"生成任务的pallet不正确"
        #     else:
        #         wms.return_pick.checkpallet(pallet_no=self.rep_info['vrppallet'], orderid=self.rep_info['orderNOcreate'],
        #                                     itemLevel=itemLevel, workType=workType, userId=self.rep_info['user_id'])
        #         # 获取退货拣选任务列表，参数为0表示获取已扫pallet的任务列表
        #         wms.return_pick.gettasklist(action=0, workType=workType,
        #                                     warehouseNumber=self.rep_info['warehouse_number'],
        #                                     userId=self.rep_info['user_id'])
        #         taskdb = wms.wms_db.get_vendor_task(self.rep_info['orderNOcreate'])
        #         assert taskdb[i]['picking_pallet_no'] == self.rep_info['vrppallet'],"生成任务的pallet不正确"
        #
        #     if pickway == 2:
        #         # 查看lpn详情，提出扫描的lpn
        #         lpn = wms.return_pick.pickDetailLpn(orderNO=self.rep_info['orderNOcreate'], recid=recid, locationNo=location)
        #         # 获取扫描的lpn个数list
        #         itemQuantity = jmespath(lpn, "itemQuantity")
        #         piecesPerPack = jmespath(lpn, "piecesPerPack")
        #         lpnquantity = itemQuantity / piecesPerPack
        #         lpnlist = [lpn['allLpnList'][0]['lpn_no'] for i in range(int(lpnquantity))]
        #         # pickconfirm提交
        #         wms.return_pick.pickConfirmLpn(itemNumber=self.rep_info['itemNumber1'], pallet=self.rep_info['pallet'],
        #                                        lpnNoList=lpnlist, warehouseNumber=self.rep_info['warehouse_number'],
        #                                        returnNo=self.rep_info['orderNOcreate'], recid=recid, location=location)
        #         # 扫slot
        #         wms.return_pick.scanSlot(orderNO=self.rep_info['orderNOcreate'], slotNo=self.rep_info['slotNo'],
        #                                  pallet_no=self.rep_info['pallet'])
        #         taskdb = wms.wms_db.get_vendor_task(self.rep_info['orderNOcreate'])
        #         assert taskdb[i]['slot_no'] == self.rep_info['slotNo'],"生成任务的slot_no不正确"
        #         assert taskdb[i]['status'] == 40,"生成任务的状态不是放置于slot中"
        #     else:
        #         # 查看详情，提出扫描的upc
        #         upc = wms.return_pick.pickDetail(orderNO=self.rep_info['orderNOcreate'], recid=recid, locationNo=location)
        #         quantity = jmespath(upc, "recommendTotalQty")
        #         wms.return_pick.pickConfirm(itemNumber=self.rep_info['itemNumber1'], pallet=self.rep_info['vrppallet'],
        #                                     quantity=quantity, orderNO=self.rep_info['orderNOcreate'], recid=recid,
        #                                     locationNo=location)
        #         wms.return_pick.scanSlot(orderNO=self.rep_info['orderNOcreate'], slotNo=self.rep_info['slotNo'],
        #                                  pallet_no=self.rep_info['vrppallet'])
        #         taskdb = wms.wms_db.get_vendor_task(self.rep_info['orderNOcreate'])
        #         assert taskdb[i]['slot_no'] == self.rep_info['slotNo'],"生成任务的slot_no不正确"
        #         assert taskdb[i]['status'] == 40,"生成任务的状态不是放置于slot中"
    # @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_vendor_pickup(self):
        '''
        测试vendorpickup功能
        '''
        # vendor pick up
        # pickupinfo = wms.wms_db.pick_up_info(order_no=self.rep_info['orderNOcreate'])

        picklist = wms.vendorpickup.queryCompulsoryList(warehouseNumber=self.rep_info['warehouse_number'], userId=self.rep_info['user_id'])
        for i in picklist:
            if i['returnNo'] == str(self.rep_info['orderNOcreate']):
                orderrecid = i['returnRecId']
        # 获取待提货pallet信息
        palletinfo = wms.vendorpickup.getpickupPallet(warehouseNumber=self.rep_info['warehouse_number'], userId=self.rep_info['user_id'],
                                                      RecId=orderrecid)
        palletlist = [palletinfo[i]['palletNo'] for i in range(2)]
        # pallet装车
        wms.vendorpickup.pickupload(warehouseNumber=self.rep_info['warehouse_number'], userId=self.rep_info['user_id'], RecId=orderrecid,
                                    pallets=palletlist)
        palletstatus = wms.wms_db.pick_up_pallet(order_no=self.rep_info['orderNOcreate'])
        assert [palletstatus[i]['status'] for i in range(2)] == [20, 20],"pallet状态未装车"
        # create bol
        wms.vendorpickup.createbol(warehouseNumber=self.rep_info['warehouse_number'], userId=self.rep_info['user_id'], RecId=orderrecid,
                                   orderNO=self.rep_info['orderNOcreate'], pallets=palletlist)
        palletstatus = wms.wms_db.pick_up_pallet(order_no=self.rep_info['orderNOcreate'])
        assert [palletstatus[i]['status'] for i in range(2)] == [30, 30],"pallet状态未出库"
        pickupinfo = wms.wms_db.pick_up_info(order_no=self.rep_info['orderNOcreate'])
        assert pickupinfo['status'] == 40,"提货出库异常"
        return_no = wms.wms_db.get_vendor_order(self.rep_info['orderNOcreate'])
        assert return_no['status'] == 80,"订单状态未出库"
    # @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_order_pickdispose(self):
        '''
        测试拣货的功能Weee! dispose
        '''
        self.rep_info['palletdis'] = wms.wms_db.get_wh_vendor_storage_location_info(
            warehouse=self.rep_info['warehouse_number'], location_type=38, flag=0,
            info='location_no')
        self.rep_info['vrppalletdis'] = wms.wms_db.get_wh_vendor_storage_location_info(
            warehouse=self.rep_info['warehouse_number'], location_type=80, flag=0,
            info='location_no')
        orderNOpickdispose = random.randint(1, 1000000)
        # 登录WMS系统
        user_id, user_name = wms.wms_login.common_login()

        # 获取return单数据
        wms.return_order.createOrUpdate(orderNO=orderNOpickdispose, pickUpDate=self.rep_info['current_time'],deliveryType=2,itemNumber1=self.rep_info['itemNumber1'])
        # 校验订单数据
        return_no = wms.wms_db.get_vendor_order(orderNOpickdispose)
        orderitem = wms.wms_db.get_vendor_orderitem(orderNOpickdispose)
        assert return_no['return_no'] == str(orderNOpickdispose),"无创建订单数据"
        assert orderitem[0]['item_number'] == self.rep_info['itemNumber1'],"无创建订单商品数据"

        #     # 开始预占订单
        wms.return_order.startpreoccupy(orderNO=orderNOpickdispose)

        # 获取预占结果
        preoccupyresult = wms.return_order.getpreoccupyresult(orderNO=orderNOpickdispose)
        hastask = jmespath(preoccupyresult, "hasTask")
        if hastask == True:
            # check封装 校验预占生成任务
            time.sleep(2)
            count = wms.wms_db.get_batch_preoccupancy_inventory(reference_no=orderNOpickdispose)
            time.sleep(2)
            taskdb = wms.wms_db.get_vendor_task(orderNOpickdispose)
            assert len(count) == len(taskdb),"预占生成任务数量与预占数量不一致"
        else:
            # 取消订单
            wms.return_order.cancelorder(orderNO=orderNOpickdispose)
            return_no = wms.wms_db.get_vendor_order(orderNOpickdispose)
            assert return_no['status'] == 100,"订单状态异常"

        # 调整任务优先级
        time.sleep(1)
        wms.wms_db.update_vendor_taskpriority(orderNOpickdispose)
        time.sleep(1)

        for i in range(len(taskdb)):
            workType = taskdb[i]['work_type']
            itemLevel = taskdb[i]['item_level']
            location = taskdb[i]['location_no']
            recid = taskdb[i]['rec_id']
            pickway = taskdb[i]['picking_way']
            # 获取退货拣选任务列表，参数为1表示获取未扫pallet的任务列表
            wms.return_pick.gettasklist(action=1, workType=workType, warehouseNumber=self.rep_info['warehouse_number'],
                                        userId=user_id)

            # 扫好货or坏货pallet
            if itemLevel == 1:
                wms.return_pick.checkpallet(pallet_no=self.rep_info['palletdis'], orderid=orderNOpickdispose,
                                            itemLevel=itemLevel, workType=workType, userId=user_id)
                # 获取退货拣选任务列表，参数为0表示获取已扫pallet的任务列表
                wms.return_pick.gettasklist(action=0, workType=workType,
                                            warehouseNumber=self.rep_info['warehouse_number'], userId=user_id)
                taskdb = wms.wms_db.get_vendor_task(orderNOpickdispose)
                assert taskdb[i]['picking_pallet_no'] == self.rep_info['palletdis'],"生成任务的pallet不正确"
            else:
                wms.return_pick.checkpallet(pallet_no=self.rep_info['vrppalletdis'], orderid=orderNOpickdispose,
                                            itemLevel=itemLevel, workType=workType, userId=user_id)
                # 获取退货拣选任务列表，参数为0表示获取已扫pallet的任务列表
                wms.return_pick.gettasklist(action=0, workType=workType,
                                            warehouseNumber=self.rep_info['warehouse_number'],
                                            userId=user_id)
                taskdb = wms.wms_db.get_vendor_task(orderNOpickdispose)
                assert taskdb[i]['picking_pallet_no'] == self.rep_info['vrppalletdis'],"生成任务的pallet不正确"

            if pickway == 2:
                # 查看lpn详情，提出扫描的lpn
                lpn = wms.return_pick.pickDetailLpn(orderNO=orderNOpickdispose, recid=recid, locationNo=location)
                # 获取扫描的lpn个数list
                itemQuantity = jmespath(lpn, "itemQuantity")
                piecesPerPack = jmespath(lpn, "piecesPerPack")
                lpnquantity = itemQuantity / piecesPerPack
                lpnlist = [lpn['allLpnList'][0]['lpn_no'] for i in range(int(lpnquantity))]
                # pickconfirm提交
                wms.return_pick.pickConfirmLpn(itemNumber=self.rep_info['itemNumber1'], pallet=self.rep_info['palletdis'],
                                               lpnNoList=lpnlist, warehouseNumber=self.rep_info['warehouse_number'],
                                               returnNo=orderNOpickdispose, recid=recid, location=location)
                # 扫slot
                wms.return_pick.scanSlot(orderNO=orderNOpickdispose, slotNo='SLOT999',
                                         pallet_no=self.rep_info['palletdis'])
                taskdb = wms.wms_db.get_vendor_task(orderNOpickdispose)
                assert taskdb[i]['slot_no'] == 'SLOT999',"生成任务的slot_no不正确"
                assert taskdb[i]['status'] == 40,"生成任务的状态不是放置于slot中"
            else:
                # 查看详情，提出扫描的upc
                upc = wms.return_pick.pickDetail(orderNO=orderNOpickdispose, recid=recid, locationNo=location)
                quantity = jmespath(upc, "recommendTotalQty")
                wms.return_pick.pickConfirm(itemNumber=self.rep_info['itemNumber1'], pallet=self.rep_info['vrppalletdis'],
                                            quantity=quantity, orderNO=orderNOpickdispose, recid=recid,
                                            locationNo=location)
                wms.return_pick.scanSlot(orderNO=orderNOpickdispose, slotNo='SLOT999',
                                         pallet_no=self.rep_info['vrppalletdis'])
                taskdb = wms.wms_db.get_vendor_task(orderNOpickdispose)
                assert taskdb[i]['slot_no'] == 'SLOT999',"生成任务的slot_no不正确"
                assert taskdb[i]['status'] == 40,"生成任务的状态不是放置于slot中"
        time.sleep(1)
        return_no = wms.wms_db.get_vendor_order(orderNOpickdispose)
        time.sleep(1)
        assert return_no['status'] == 80,"订单状态未出库"

    def test_order_ff(self):
        self._order_create()
        self._order_preoccupy()
        self._order_pickselfpickup()

    # @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def _scan_equipment_flow(self):
        '''
        测试扫描设备流程
        '''
        # 设置测试参数
        warehouse_number = self.rep_info['warehouse_number']
        storage_type = self.rep_info['storage_type']
        equipment_code = "LM-RT-11"  # 设备编码
        user_id=self.rep_info['user_id']
        pallet_code = self.rep_info['pallet']      # 托盘编码
        biz_type = 50                # 业务类型

        # 步骤1: 扫描设备
        scan_equipment_resp = wms.common_pick.scan_Equipment(
            warehouseNumber=warehouse_number,
            storageType=storage_type,
            userId=user_id,
            Equipment_code=equipment_code
        )

        # 验证扫描设备结果
        assert scan_equipment_resp is not None, "扫描设备返回结果为空"
        assert "taskDispatchList" in scan_equipment_resp, "未返回任务分派列表"
        assert len(scan_equipment_resp["taskDispatchList"]) > 0, "任务分派列表为空"

        # 获取任务信息
        task_rec_id = scan_equipment_resp["taskDispatchList"][0]["bizTaskId"]
        location_no = scan_equipment_resp["taskDispatchList"][0]["locationNo"]

        # 步骤2: 扫描托盘
        #需要判断当前任务是否已经扫描绑定过pallet，若已绑定则要扫描绑定的pallet号，若未绑定pallet，则扫新的未占用的pallet
        scan_pallet_resp = wms.common_pick.scan_pallet(
            bizType=biz_type,
            warehouseNumber=warehouse_number,
            storageType=storage_type,
            userId=user_id,
            pallet_code=pallet_code
        )

        # 验证扫描托盘结果
        assert scan_pallet_resp is not None, "扫描托盘返回结果为空"
        assert "bizOrderId" in scan_pallet_resp, "未返回业务订单ID"

        # 获取业务订单ID
        #vendorreturn仍需要判断是否领取到多个任务，且要做的当前任务不是创建的订单任务，根据/wms/commonPick/pickList接口返回waitPickList数组
        #判断当前要做的第一个任务
        biz_order_id = scan_pallet_resp["bizOrderId"]

        # 步骤3: 获取UPC拣货详情
        pick_detail_resp = wms.common_pick.get_upc_pick_detail(
            warehouseNumber=warehouse_number,
            taskRecId=task_rec_id,
            locationNo=location_no,
            bizType=biz_type,
            bizOrderId=biz_order_id,
            pallet_code=pallet_code,
            userId=user_id
        )

        # 获取商品信息和推荐拣货数量
        item_number = pick_detail_resp["itemNumber"]
        picked_quantity = pick_detail_resp["recommendTotalQty"]

        # 步骤4: 确认UPC拣货
        wms.common_pick.confirm_upc_pick(
            item_number=item_number,
            picking_action=0,
            picked_quantity=picked_quantity,
            warehouse_number=warehouse_number,
            task_rec_id=task_rec_id,
            location_no=location_no,
            biz_type=biz_type,
            biz_order_id=biz_order_id,
            user_id=user_id,
            picking_pallet_no=pallet_code
        )

        # 步骤5: 拣货完成
        complete_resp = wms.common_pick.pick_complete(
            warehouse_number=warehouse_number,
            picking_pallet_no=pallet_code,
            storage_type=storage_type,
            user_id=user_id,
            type=biz_type,
            biz_order_id=biz_order_id
        )

        # 获取货位信息
        slot_no = complete_resp["slotNo"]

        # 步骤6: 扫描货位
        wms.common_pick.scan_slot(
            slotNo=slot_no,
            warehouseNumber=warehouse_number,
            storageType=storage_type,
            userId=user_id,
            type=biz_type,
            bizOrderId=biz_order_id,
            pickingPalletNo=pallet_code
        )

        # 验证托盘状态
        pallet_info = wms.wms_db.check_tote_status(tote_no=pallet_code, warehouse=warehouse_number)
        assert pallet_info["flag"] == 0, f"托盘状态验证失败，期望：0，实际：{pallet_info['flag']}"

if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True, open=False,case_list=['test_return_order.py::Testreturnorder::test_order_ff'])
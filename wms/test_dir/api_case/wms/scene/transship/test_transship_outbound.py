# !/usr/bin/python3
# -*- coding: utf-8 -*-
import jsonpath
import weeeTest

from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms



class TestTransshipOutbound(weeeTest.TestCase):
    """
    调拨出库&入库流程
    """

    def setup_class(self):
        # 相关接口需要的信息
        self.ts_info = globals()
        # 登录
        account, username = wms.wms_login.common_login()
        # 出库信息
        self.ts_info["user_id"] = account
        self.ts_info["user_name"] = username

        self.ts_info["ship_out_warehouse"] = global_data.ts_info["ship_out_warehouse"]
        self.ts_info["ship_in_warehouse"] = global_data.ts_info["ship_in_warehouse"]
        self.ts_info["item_number"] = global_data.ts_info["item_number"]
        self.ts_info["location_no"] = global_data.ts_info["location_no"]
        self.ts_info["storage_type"] =  global_data.ts_info["storage_type"]

        # 入库信息
        self.ts_receive_info = globals()
        self.ts_receive_info["user_id"] = account
        self.ts_receive_info["user_name"] = username
        wms.util.update_header(weee_user=username + "(" + account + ")")
        self.ts_receive_info["warehouse_number"] = global_data.ts_receive_info["warehouse_number"]

    @weeeTest.mark.list("WMS-Regression", "WMS-Smoke", "WMS", "TS")
    def test_ts_preoccupancy(self):
        """【107089】调拨单预占"""

        # 查询可预占的调拨单
        ts_list = wms.ts_api.query_ts_number(
            self.ts_info["ship_out_warehouse"],
            self.ts_info["ship_in_warehouse"],
            wms.util.get_special_date(days=-30),
            wms.util.get_special_date(days=0),
        )["body"]["data"]
        assert ts_list != [], "无可用的调拨单"
        for order in ts_list:
            # 使用ERP脚本下发的调拨单
            if "7169981" in order["create_user"] or "********" in order["create_user"] or "********" in order["create_user"]:
                self.ts_info["ts_number"] = order["ts_number"]
                break
        assert self.ts_info.get("ts_number", "") != "", "无可用的调拨单"
        self.ts_info["estimate_pickup_date"] = ts_list[0]["estimate_pickup_date"]

        # 更改调拨单预占方式为LIFO,方便预占到指定数据
        wms.wms_db.update_transship_order_item(self.ts_info["ts_number"])

        # 获取调拨单详情
        ts_detail = wms.ts_api.query_ts_details(self.ts_info["ship_out_warehouse"], self.ts_info["ts_number"], self.ts_info["storage_type"])["body"]

        for item in ts_detail["itemList"]:
            # 检查库存数据, 无库存则加库存
            item_inv = wms.wms_db.get_location_inventory(self.ts_info["ship_out_warehouse"], self.ts_info["location_no"], item["item_number"])
            item_qty = 0
            if item_inv != []:
                item_qty = item_inv[0]["quantity"]
            if item_qty < item["order_qty"] or item_inv == []:
                expire_dtm = wms.util.get_special_date(days=3650)
                receive_dtm = wms.util.get_special_date(days=0)
                wms.adjust_api.create_batch_inv(
                    item["item_number"],
                    self.ts_info["ship_out_warehouse"],
                    self.ts_info["user_id"],
                    self.ts_info["location_no"],
                    10,
                    is_lpn=True,
                    pieces_per_pack=1.00,
                    expire_dtm=expire_dtm,
                    receive_date=receive_dtm,
                    lpn_qty=10
                )

            #解锁库位
            inv_res = wms.adjust_api.query_location_inv(
                    item["item_number"],
                    self.ts_info["location_no"],
                    self.ts_info["ship_out_warehouse"],
            )["body"]["invAdjustList"]
            lpn_list = list(map(lambda i: i["lpn_no"], inv_res[0]["lpnInfos"]))
            wms.adjust_api.lock_inv(0, item["item_number"], self.ts_info["location_no"], self.ts_info["ship_out_warehouse"], True, lpn_list, None)

        # 开始预占
        wms.ts_api.ts_auto_preoccupy(self.ts_info["ts_number"])

    @weeeTest.mark.list("WMS-Regression", "WMS-Smoke", "WMS", "TS")
    def test_update_priority(self):
        """【102715】 调整调拨单优先级"""
        wms.wms_db.update_transship_task_priority(self.ts_info["ship_out_warehouse"])
        wms.ts_api.update_ts_priority(self.ts_info["ts_number"])

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', "TS")
    def test_ts_pick(self):
        """
        【112948】调拨拣货
        """

        # 查询可用的Pick pallet(TB1 有脏数据，暂时先写死库位)
        # self.ts_info["pallet_no"] = wms.common_api.get_avaiable_location(self.ts_info["ship_out_warehouse"], 43)[0]["location_no"]
        # self.ts_info["slot_no"] = wms.common_api.get_avaiable_location(self.ts_info["ship_out_warehouse"], 46)[0]["location_no"]
        self.ts_info["pallet_no"] = "AUTO001"
        wms.wms_db.check_tote_status(self.ts_info["pallet_no"], self.ts_info["ship_out_warehouse"], 0)
        self.ts_info["slot_no"] = "AUTOSLOT001"
        wms.wms_db.check_tote_status(self.ts_info["slot_no"], self.ts_info["ship_out_warehouse"], 0)
        # 设置领取Partial Pallet- Air 任务
        self.ts_info["task_type"] = "3"

        # 领取任务
        rep = wms.ts_api.get_ts_task(
            self.ts_info["ship_out_warehouse"],
            self.ts_info["user_id"],
            self.ts_info["pallet_no"],
            self.ts_info["task_type"],
            self.ts_info["storage_type"],
        )
        tasks = rep["body"]["task_list"]

        # 开始拣货
        for t in tasks:
            item_number = t["item_number"]
            plan_pick_qty = t["item_quantity"]
            self.ts_info["ts_number"] = t["ts_number"]
            self.ts_info["task_rec_id"] = t["rec_id"]
            self.ts_info["location_no"] = t["location_no"]
            lpn_list = wms.ts_api.pick_lpn_task(
                t["warehouse_number"], t["location_no"], t["rec_id"]
            )["body"]["allLpnList"]
            pick_lpn_list = []
            pick_qty = 0
            for lpn in lpn_list:
                if pick_qty < plan_pick_qty:
                    pick_lpn_list.append(lpn["lpn_no"])
                    pick_qty += lpn["quantity"]
                else:
                    break
            wms.ts_api.ts_pick_confirm(
                self.ts_info["ship_out_warehouse"],
                self.ts_info["location_no"],
                self.ts_info["pallet_no"],
                self.ts_info["task_rec_id"],
                self.ts_info["user_id"],
                self.ts_info["ts_number"],
                pick_lpn_list,
            )
        wms.ts_api.ts_scan_slot(
            self.ts_info["ship_out_warehouse"],
            self.ts_info["user_id"],
            self.ts_info["pallet_no"],
            self.ts_info["ts_number"],
            self.ts_info["slot_no"],
            self.ts_info["storage_type"],
        )

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'TS-QC', 'TS')
    def test_ts_qc(self):
        """
        【112949】 调拨QC
        """
        # 检查调拨单是否是待QC状态
        ts_order = wms.wms_db.get_transship_order(self.ts_info["ts_number"])
        assert ts_order["status"] == 50, f"""{self.ts_info["ts_number"]}调拨单不是待QC状态"""

        # 开始QC
        wms.ts_api.get_ts_qc_order(
            self.ts_info["ship_out_warehouse"], self.ts_info["storage_type"]
        )
        qcPalletList = wms.ts_api.ts_qc_scan_slot(
            self.ts_info["ts_number"],
            self.ts_info["ship_out_warehouse"],
            self.ts_info["ship_in_warehouse"],
            self.ts_info["slot_no"],
            self.ts_info["estimate_pickup_date"],
        )["body"]["qcPalletList"]
        wms.ts_api.pritn_qc_pallet(
            self.ts_info["ts_number"],
            self.ts_info["ship_out_warehouse"],
            self.ts_info["slot_no"],
            qcPalletList[0]["qc_pallet_no"],
            qcPalletList[0]["rec_id"],
        )
        rep = wms.ts_api.get_qc_tasks(
            self.ts_info["ts_number"],
            self.ts_info["ship_out_warehouse"],
            self.ts_info["slot_no"],
            qcPalletList[0]["qc_pallet_no"],
        )["body"]
        task_list = rep["task_list"]
        # plt_list = rep["plt_list"]
        for task in task_list:
            # # plt_detail_list: [{plt_no: "TSP016", lpn_list: ["G2411-1079-M5-1", "G2411-1079-M5-2", "G2411-1079-M4-1"]
            plt_detail_list = task["plt_detail_list"]
            item_number = task["item_number"]
            for plt in plt_detail_list:
                confirm_lpn_list = []
                confirm_lpn_list.append({"location_no": plt["plt_no"], "lpn_no_list": plt["lpn_list"]})
                wms.ts_api.lpn_qc_confirm(
                    self.ts_info["ship_out_warehouse"],
                    self.ts_info["slot_no"],
                    self.ts_info["ts_number"],
                    qcPalletList[0]["qc_pallet_no"],
                    confirm_lpn_list,
                    item_number,
                    )

        wms.ts_api.qc_complete(
            self.ts_info["ship_out_warehouse"],
            self.ts_info["slot_no"],
            self.ts_info["ts_number"],
            self.ts_info["user_id"],
        )
        move_location = wms.ts_api.get_move_inv_location(
            self.ts_info["ship_out_warehouse"], self.ts_info["ts_number"]
        )["body"]["location_no"][0]
        wms.ts_api.ts_qc_inv_move(
            self.ts_info["ship_out_warehouse"],
            self.ts_info["slot_no"],
            self.ts_info["ts_number"],
            move_location,
            self.ts_info["user_id"],
        )

        # 模拟做完Truck Load给merch上报shipping状态 （Label Receive confirm时Merch会校验调拨单状态必须为shipping）,本地调不通该接口，调试时请注释
        batch_id = f'''qatest-{self.ts_info["ts_number"]}-{wms.util.random_num(999,9999)}'''
        wms.util.wait(sleep_time=3)
        wms.ts_api.ts_status_update_srm(self.ts_info["ts_number"], "D", batch_id, self.ts_info["user_id"])


    @weeeTest.mark.list("WMS-Regression", "WMS-Smoke", "WMS", "TS")
    def test_transship_receive(self):
        """
        【110950】调拨单在入库仓收货
        """
        # 获取可用的调拨入库单
        reference_no_list = wms.label_receive.query_inbound_number(
            self.ts_receive_info["warehouse_number"],
            6,
            wms.util.get_special_date(days=-30),
            wms.util.get_special_date(days=0),
            self.ts_receive_info["user_id"],
            reference_no=self.ts_info["ts_number"]
        )
        assert reference_no_list != [], "无可用的调拨入库单"
        for order in reference_no_list:
            if (
                order["storage_type_name"] == "Frozen"
                and order["sku_qty"] == 1
                and order["total_unit_qty"] == 1
            ):
                self.ts_receive_info["reference_no"] = order["reference_no"]
                weee_wms_storage_type = wms.get_storage_type(
                    storage_type=order["storage_type_name"]
                )
                wms.util.update_header(weee_wms_storage_type=str(weee_wms_storage_type))
                break
        assert self.ts_receive_info.get("reference_no", "") != "", "可用的调拨入库单"

        # 获取调拨单的出库pallet
        self.ts_receive_info["ob_pallet"] = wms.wms_db.get_transship_ob_pallet(
            self.ts_receive_info["reference_no"]
        )["qc_pallet_no"]
        # 模拟用户进入Label Receive模块(逻辑上需要比较用户进入模块与业务单历史作业模块相同)
        # wms.common_api.record_moudle_log(self.ts_receive_info["warehouse_number"], "label_receive")
        # 扫描PALLET 开始收货
        # wms.label_receive.query_po(self.ts_receive_info["ob_pallet"], self.ts_receive_info["warehouse_number"], 2, 6)
        # 获取ToDo List
        receive_list = wms.label_receive.query_receive_list(
            self.ts_receive_info["reference_no"],
            1,
            self.ts_receive_info["warehouse_number"],
        )["poItemList"]
        for r in receive_list:
            detail = wms.label_receive.label_receive_detail(
                self.ts_receive_info["reference_no"],
                r["item_number"],
                r["expired_dtm"],
                r["receive_dtm"],
                r["pieces_per_pack"],
            )
            # 收货前检查RECG0001好货区的库存
            pre_item_inv = wms.wms_db.get_inventory_transaction(
                self.ts_receive_info["warehouse_number"],
                "RECG0001",
                detail["item_number"],
            )
            rec_qty = 0
            for lpn in detail["lpnInfoList"]:
                rec_qty += lpn["original_quantity"]
            confirm_res = wms.label_receive.label_receive_confirm(
                self.ts_receive_info["warehouse_number"],
                self.ts_receive_info["reference_no"],
                detail["item_number"],
                rec_qty,
                detail["pieces_per_pack"],
                detail["expire_dtm"],
                self.ts_receive_info["user_id"],
                detail["lpnInfoList"],
            )
            wms.label_receive.label_receive_print_label(
                self.ts_receive_info["warehouse_number"],
                confirm_res["rec_id"],
                self.ts_receive_info["user_id"],
            )

            # 检查库存
            wms.wms_assert.check_non_batch_invenotry(
                self.ts_receive_info["warehouse_number"],
                "RECG0001",
                detail["item_number"],
                rec_qty,
                reference_no=self.ts_receive_info["reference_no"],
                inventory_type=[305],
                old_qty=pre_item_inv["quantity"],
                in_dtm=wms.util.get_current_time()
            )

            # 检查上架任务生成
            putaway_task = wms.wms_db.get_putaway_task(
                self.ts_receive_info["warehouse_number"],
                self.ts_receive_info["reference_no"],
                6,
                10,
                confirm_res["rec_id"],
            )
            assert putaway_task != [], "收货后未生成putaway task"

        # 检查业务单状态
        order_info = wms.wms_db.get_inbound_order_info(
            self.ts_receive_info["reference_no"]
        )
        assert order_info["status"] == 50, "全部收货后业务单状态未变为50"

    @weeeTest.mark.list("WMS-Regression", "WMS-Smoke", "WMS", "TS")
    def test_transship_receive_delete_batch(self):
        """
        【112939】调拨单在入库仓收货后错收冲减
        """
        # 获取调拨单也Done List
        done_list = wms.label_receive.query_receive_list(
            self.ts_receive_info["reference_no"],
            2,
            self.ts_receive_info["warehouse_number"],
        )["batchList"]
        for d in done_list:
            # 冲减前检查RECG0001好货区的库存
            pre_item_inv = wms.wms_db.get_inventory_transaction(
                self.ts_receive_info["warehouse_number"], "RECG0001", d["item_number"]
            )
            wms.label_receive.label_receive_delete_batch(
                self.ts_receive_info["warehouse_number"],
                d["rec_Id"],
                self.ts_receive_info["reference_no"],
                self.ts_receive_info["user_id"],
            )

            # 检查库存
            wms.wms_assert.check_non_batch_invenotry(
                self.ts_receive_info["warehouse_number"],
                "RECG0001",
                d["item_number"],
                -d["rec_total"],
                reference_no=self.ts_receive_info["reference_no"],
                inventory_type=[306],
                old_qty=pre_item_inv["quantity"],
                in_dtm=wms.util.get_current_time()
            )
        # 检查业务单状态
        order_info = wms.wms_db.get_inbound_order_info(
            self.ts_receive_info["reference_no"]
        )
        assert order_info["status"] == 0, "已收货Batch全部错收冲减后,业务单状态未变为0"

    @weeeTest.mark.list("WMS-Regression", "WMS-Smoke", "WMS", "TS")
    def test_fullpallet_preoccupy(self):
        '''
        [113027]调拨整版预占  WMS-13647
        '''
        self.ts_info["warehouse_number8"] = 8
        # 调拨下单预占
        reference_no = wms.util.random_num(10000000, 99999999)
        wms.ts_api.ts_order(item_number=10421, reference_no=reference_no)
        wms.ts_api.ts_auto_preoccupy(reference_no)
        #断言full pallet任务
        res=wms.wms_db.get_transship_task(warehouse_number=self.ts_info["warehouse_number8"],ts_number=reference_no)
        assert res[0]["type"]==1,"full pallet任务生成失败"
        #取消订单
        wms.ts_api.ts_order_cancel(reference_no)
    @weeeTest.mark.list("WMS-Regression", "WMS-Smoke", "WMS", "TS")
    def test_pickway(self):
        '''
        [113028]调拨拣货领取一种拣货类型任务  WMS-14250
        '''
        # 调拨下单预占,构造预占到一个upc，一个lpn上，两个任务，进行扫pallet领取任务
        #校验getlist领取的任务只有一个类型picking_way，case成功
        #取消该订单
        # 数据修改为48仓
        self.ts_info["warehouse_number8"] = 8
        self.ts_info["storage_type8"]=1
        # 调拨下单预占
        reference_no = wms.util.random_num(10000000, 99999999)
        wms.ts_api.ts_order(item_number=51339, reference_no=reference_no)
        wms.ts_api.ts_auto_preoccupy(reference_no)
        #断言领取一个pickway类型任务
        self.ts_info["pallet_no8"] = "AUTO002"
        wms.wms_db.check_tote_status(self.ts_info["pallet_no8"], self.ts_info["warehouse_number8"], 0)
        self.ts_info["slot_no8"] = "AUTOSLOT002"
        wms.wms_db.check_tote_status(self.ts_info["slot_no8"], self.ts_info["warehouse_number8"], 0)
        # 设置领取Partial Pallet- Air 任务
        type=wms.wms_db.get_transship_task(warehouse_number=self.ts_info["warehouse_number8"],ts_number=reference_no)
        worktype=jsonpath.jsonpath(type,'$[*].type')
        assert worktype[0] == worktype[1],"领取两个任务类型不一致"
        self.ts_info["task_type8"] = worktype[0]

        # 领取任务
        rep = wms.ts_api.get_ts_task(
            self.ts_info["warehouse_number8"],
            self.ts_info["user_id"],
            self.ts_info["pallet_no8"],
            self.ts_info["task_type8"],
            self.ts_info["storage_type8"],
        )
        tasks = rep["body"]["task_list"]
        assert len(tasks) == 1,"未只领取一个类型任务"
        wms.ts_api.ts_pick_complete(self.ts_info["warehouse_number8"], self.ts_info["user_id"], reference_no, self.ts_info["pallet_no8"], self.ts_info["storage_type8"])
        #取消订单
        wms.ts_api.ts_order_cancel(reference_no)

    @weeeTest.mark.list("WMS-Regression", "WMS-Smoke", "WMS", "TS")
    def test_pick_fullpallet(self):
        '''
        [113248]调拨拣货full pallet   WMS-13458
        '''
        self.ts_info["warehouse_number8"]=8
        self.ts_info["storage_type8"] = 1  # 添加storage_type参数

        wms.wms_db.update_location_status(8,"AUTOSLOT0011",0)
        wms.wms_db.update_location_status(8,"AUTO0011",0)

        #调拨下单预占
        reference_no=wms.util.random_num(10000000,99999999)
        wms.ts_api.ts_order(item_number=10395,reference_no=reference_no)
        wms.ts_api.ts_auto_preoccupy(reference_no)
        #调整优先级
        wms.wms_db.update_transship_task_priority(warehouse_number=self.ts_info["warehouse_number8"])
        wms.ts_api.update_ts_priority(reference_no)
        #full pallet拣货 ，指定使用固定一个pallet
        self.ts_info["pallet_no8"] = "AUTO0011"
        wms.wms_db.check_tote_status(self.ts_info["pallet_no8"], self.ts_info["warehouse_number8"], 0)
        self.ts_info["slot_no8"] = "AUTOSLOT0011"
        wms.wms_db.check_tote_status(self.ts_info["slot_no8"], self.ts_info["warehouse_number8"], 0)
        # 设置领取Partial Pallet- Air 任务
        self.ts_info["task_type8"] = "3"

        # 领取任务
        rep = wms.ts_api.get_ts_task(
            self.ts_info["warehouse_number8"],
            self.ts_info["user_id"],
            self.ts_info["pallet_no8"],
            self.ts_info["task_type8"],
            self.ts_info["storage_type8"],
        )
        tasks = rep["body"]["task_list"]
        # 断言获取到任务
        assert len(tasks) > 0, "未获取到Full Pallet任务"

        # 开始拣货
        for t in tasks:
            item_number = t["item_number"]
            plan_pick_qty = t["item_quantity"]
            self.ts_info["ts_number"] = t["ts_number"]
            self.ts_info["task_rec_id"] = t["rec_id"]
            self.ts_info["location_no"] = t["location_no"]
           # 获取可拣货的LPN列表
            lpn_list = wms.ts_api.pick_lpn_task(
                t["warehouse_number"], 
                t["location_no"], 
                t["rec_id"]
            )["body"]["allLpnList"]
            pick_lpn_list = []
            pick_qty = 0
            for lpn in lpn_list:
                pick_lpn_list.append(lpn["lpn_no"])
                break
            break
        wms.ts_api.ts_pick_confirm_fullpallet(
            self.ts_info["warehouse_number8"],
            self.ts_info["location_no"],
            self.ts_info["pallet_no8"],
            self.ts_info["task_rec_id"],
            self.ts_info["user_id"],
            self.ts_info["ts_number"],
            pick_lpn_list,
        )
        wms.ts_api.ts_pick_complete(
            self.ts_info["warehouse_number8"],
            self.ts_info["user_id"],
            self.ts_info["ts_number"],
            self.ts_info["pallet_no8"],
            self.ts_info["storage_type8"],
        )
        wms.ts_api.ts_scan_slot(
            self.ts_info["warehouse_number8"],
            self.ts_info["user_id"],
            self.ts_info["pallet_no8"],
            self.ts_info["ts_number"],
            self.ts_info["slot_no8"],
            self.ts_info["storage_type8"],
        )
        # 7. 断言pallet full后预占生成新的任务
        task_status = wms.wms_db.get_transship_task(
            warehouse_number=self.ts_info["warehouse_number8"],
            ts_number=reference_no
        )
        assert len(task_status) == 1, "Full Pallet任务未生成新任务"
        
        # 8. 清理测试数据
        wms.wms_db.update_transship_order_status(10,self.ts_info["ts_number"])
        wms.ts_api.ts_order_cancel(self.ts_info["ts_number"])

if __name__ == "__main__":
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True, open=False)
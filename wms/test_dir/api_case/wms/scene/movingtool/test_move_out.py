import weeeTest
from weeeTest import jmespath
from wms.test_dir.api.wms.wms import wms
from wms.test_dir.api.wms.movingtool.moving_api import Moving


class TestMoveOut(weeeTest.TestCase):
    # UPC转移
    @weeeTest.mark.list("WMS-Regression", "WMS-Smoke", "WMS")
    def test_move_out_upc1(self):
        """
        【111582】MoveOut：【UPC】Stock转移到E0001
        """
        # 登录
        user_id, user_name = wms.wms_login.common_login()
        edit_user = user_name + '(' + user_id + ")"  # 拼接in_user
        in_user = edit_user
        # 参数设置
        warehouse_number = "29"
        location_from = "E1005-2-1"
        location_to = "E0001"
        upc_code = "2000000096627"
        qty = 2
        item_number = "96627"
        move_in = False
        batch_no = "250221011166"
        lpn_no = None
        location_no = location_to
        # 调用item查询接口
        resp = Moving().check_item(warehouse_number, location_from, upc_code, move_in)
        expire_dtm = jmespath(resp, "body.expire_dtm")  # 获取expire_dtm
        receive_dtm = jmespath(resp, "body.receive_dtm")  # 获取receive_dtm
        # 转移前查询E0001库存
        qty_old = wms.wms_db.get_quantity_and_allocated_qty_sum(item_number, warehouse_number, location_no, batch_no, lpn_no)[0]  # SQL查询转移前库存
        # 调用转移接口
        Moving().move_upc(location_from, location_to, qty, item_number, warehouse_number, expire_dtm, move_in, receive_dtm, in_user)
        # 转移后查询E0001库存
        qty_new = wms.wms_db.get_quantity_and_allocated_qty_sum(item_number, warehouse_number, location_no, batch_no, lpn_no)[0]  # SQL查询转移后库存
        # 断言库存是否转移成功
        assert int(qty_new) == int(qty) + int(qty_old), "转移前后库存不一致"
        weeeTest.log.info(f'{location_from}转移到{location_to}成功!!!')

    @weeeTest.mark.list("WMS-Regression", "WMS-Smoke", "WMS")
    def test_move_out_upc2(self):
        """
        【111583】MoveOut：【UPC】Stock转移到D-Spoilage
        """
        # 登录
        user_id, user_name = wms.wms_login.common_login()
        edit_user = user_name + '(' + user_id + ")"  # 拼接in_user
        in_user = edit_user
        # 参数设置
        warehouse_number = "29"
        location_from = "E1005-2-1"
        location_to = "D-Spoilage"
        upc_code = "2000000096627"
        qty = 1
        item_number = "96627"
        move_in = False
        batch_no = "250221011166"
        lpn_no = None
        location_no = location_to
        # 调用item查询接口
        resp = Moving().check_item(warehouse_number, location_from, upc_code, move_in)
        expire_dtm = jmespath(resp, "body.expire_dtm")  # 获取expire_dtm
        receive_dtm = jmespath(resp, "body.receive_dtm")  # 获取receive_dtm
        # 转移前查询E0001库存
        qty_old = wms.wms_db.get_quantity_and_allocated_qty_sum(item_number, warehouse_number, location_no, batch_no, lpn_no)[0]  # SQL查询调整前库存
        # 调用转移接口
        Moving().move_upc(location_from, location_to, qty, item_number, warehouse_number, expire_dtm, move_in, receive_dtm, in_user)
        # 转移后查询E0001库存
        qty_new = wms.wms_db.get_quantity_and_allocated_qty_sum(item_number, warehouse_number, location_no, batch_no, lpn_no)[0]  # SQL查询调整前库存
        # 断言库存是否转移成功
        assert int(qty_new) == int(qty) + int(qty_old), "转移前后库存不一致"
        weeeTest.log.info(f'{location_from}转移到{location_to}成功!!!')

    @weeeTest.mark.list("WMS-Regression", "WMS-Smoke", "WMS")
    def test_move_out_upc3(self):
        """
        【111584】MoveOut：【UPC】D-Spoilage转移到E0002
        """
        # 登录
        user_id, user_name = wms.wms_login.common_login()
        edit_user = user_name + '(' + user_id + ")"  # 拼接in_user
        in_user = edit_user
        # 参数设置
        warehouse_number = "29"
        location_from = "D-Spoilage"
        location_to = "E0002"
        upc_code = "2000000096627"
        qty = 1
        item_number = "96627"
        move_in = False
        batch_no = "250221011166"
        lpn_no = None
        location_no = location_to
        # 调用item查询接口
        resp = Moving().check_item(warehouse_number, location_from, upc_code, move_in)
        expire_dtm = jmespath(resp, "body.expire_dtm")  # 获取expire_dtm
        receive_dtm = jmespath(resp, "body.receive_dtm")  # 获取receive_dtm
        # 转移前查询E0001库存
        qty_old = wms.wms_db.get_quantity_and_allocated_qty_sum(item_number, warehouse_number, location_no, batch_no, lpn_no)[0]  # SQL查询调整前库存
        # 调用转移接口
        Moving().move_upc(location_from, location_to, qty, item_number, warehouse_number, expire_dtm, move_in, receive_dtm, in_user)
        # 转移后查询E0001库存
        qty_new = wms.wms_db.get_quantity_and_allocated_qty_sum(item_number, warehouse_number, location_no, batch_no, lpn_no)[0]  # SQL查询调整前库存
        # 断言库存是否转移成功
        assert int(qty_new) == int(qty) + int(qty_old), "转移前后库存不一致"
        weeeTest.log.info(f'{location_from}转移到{location_to}成功!!!')

    @weeeTest.mark.list("WMS-Regression", "WMS-Smoke", "WMS")
    def test_move_out_upc4(self):
        """
        【111585】MoveOut：【UPC】bin转移到E0008,E0008再转移到新的Bin
        """
        # 登录
        user_id, user_name = wms.wms_login.common_login()
        edit_user = user_name + '(' + user_id + ")"  # 拼接in_user
        in_user = edit_user
        # 参数设置
        warehouse_number = "29"
        location_from = "E1005-2-1"
        location_to = "E0008"
        upc_code = "2000000096627"
        qty = 1
        item_number = "96627"
        move_in = False
        batch_no = "250221011166"
        lpn_no = None
        # 清空E0008和E0304-6-1库存
        locationNos = ["E0008", "E0304-6-1"]
        Moving().clear_inv(warehouse_number, in_user, locationNos)
        weeeTest.log.info('E0008和E0304-6-1库存已清除......')
        # 调用item查询接口
        resp = Moving().check_item(warehouse_number, location_from, upc_code, move_in)
        expire_dtm = jmespath(resp, "body.expire_dtm")  # 获取expire_dtm
        receive_dtm = jmespath(resp, "body.receive_dtm")  # 获取receive_dtm
        # 转移后查询E1005-2-1库存和保质期信息
        resp1 = Moving().query_item_qty(item_number, location_from, warehouse_number)
        expiration_date_new = resp1[2]  # 获取expiration_date
        case_spec_new = resp1[3]  # 获取case_spec
        # 若from location avaiable qty 太少，则加库存
        if resp1[0] <= 1:
            wms.adjust_api.create_batch_inv(item_number, warehouse_number, edit_user, location_from, 100, pieces_per_pack=case_spec_new,
                                            expire_dtm=wms.util.utc_to_day(expiration_date_new), receive_date=wms.util.utc_to_day(receive_dtm))

        # 转移前查询E0008库存
        qty_old = wms.wms_db.get_quantity_and_allocated_qty_sum(item_number, warehouse_number, location_to, batch_no, lpn_no)[0]  # SQL查询调整前库存
        assert qty_old == 0, "E0008库存不为0"
        # 调用转移接口
        Moving().move_upc(location_from, location_to, qty, item_number, warehouse_number, expire_dtm, move_in, receive_dtm, in_user)
        # 转移后查询E0008库存
        qty_new = wms.wms_db.get_quantity_and_allocated_qty_sum(item_number, warehouse_number, location_to, batch_no, lpn_no)[0]  # SQL查询调整前库存
        # 断言库存是否转移成功
        assert int(qty_new) == int(qty) + 0, "转移前后库存不一致"
        weeeTest.log.info(f'{location_from}转移到{location_to}成功!!!')

        # E0008再转移到新的Bin,参数设置
        warehouse_number = "29"
        location_from = "E0008"
        location_to = "E0304-6-1"
        qty = 1
        item_number = "96627"
        move_in = False
        location_no = location_to
        # 调用item查询接口
        expire_dtm = jmespath(resp, "body.expire_dtm")  # 获取expire_dtm
        receive_dtm = jmespath(resp, "body.receive_dtm")  # 获取receive_dtm
        # 转移前查询E0304-6-1库存
        qty_old = wms.wms_db.get_quantity_and_allocated_qty_sum(item_number, warehouse_number, location_no, batch_no, lpn_no)[0]  # SQL查询调整前库存
        assert qty_old == 0, "E0304-6-1库存不为0"
        # 调用转移接口
        Moving().move_upc(location_from, location_to, qty, item_number, warehouse_number, expire_dtm, move_in, receive_dtm, in_user)
        # 转移后查询E0304-6-1库存
        resp2 = Moving().query_item_qty(item_number, location_to, warehouse_number)
        qty_new = wms.wms_db.get_quantity_and_allocated_qty_sum(item_number, warehouse_number, location_no, batch_no, lpn_no)[0]  # SQL查询调整前库存
        expiration_date_old = resp2[2]  # 获取expiration_date
        case_spec_old = resp2[3]  # 获取case_spec
        # 断言库存是否转移成功
        assert int(qty_new) == int(qty) + 0, "转移前后库存不一致"
        # 校验箱规和保质期
        assert expiration_date_old == expiration_date_new, "转移前后保质期不一致"
        assert case_spec_old == case_spec_new, "转移前后箱规不一致"
        weeeTest.log.info(f'{location_from}转移到{location_to}成功!!!')

    # LPN转移
    @weeeTest.mark.list("WMS-Regression", "WMS-Smoke", "WMS")
    def test_move_out_lpn1(self):
        """
        【112894】MoveOut：【LPN】Stock转移到E0006
        """
        # 登录
        user_id, user_name = wms.wms_login.common_login()
        edit_user = user_name + '(' + user_id + ")"  # 拼接in_user
        in_user = edit_user
        # 参数设置
        warehouse_number = "29"
        location_from = "F0303-1-3"
        location_to = "E0006"
        item_number = "96627"
        move_in = False
        batch_no = "250207158435"
        location_no = location_to
        # 调用item查询接口获取LPN信息
        resp1 = Moving().query_item_qty(item_number=item_number, location_to=location_from, warehouse_number=warehouse_number)
        # 若from location avaiable qty 太少，则加库存
        if resp1[0] <= 1:
            wms.adjust_api.create_batch_inv(item_number, warehouse_number, edit_user, location_from, 100, is_lpn=True, pieces_per_pack=10,
                                            expire_dtm=wms.util.get_special_date(days=365), receive_date=wms.util.get_special_date(), lpn_qty=10)
            resp1 = Moving().query_item_qty(item_number=item_number, location_to=location_from, warehouse_number=warehouse_number)
        lpn_list = resp1[1]  # 获取lpn_list
        lpn_no = lpn_list[0]["lpnNo"]
        upc_code = lpn_no
        # 遍历lpn_list数据，找到匹配的lpnNo并提取对应的quantity值
        qty = None
        for entry in lpn_list:
            if entry["lpnNo"] == lpn_no:
                qty = entry["quantity"]
                break
        resp = Moving().check_item(warehouse_number, location_from, upc_code, move_in)
        expire_dtm = jmespath(resp, "body.expire_dtm")  # 获取expire_dtm
        receive_dtm = jmespath(resp, "body.receive_dtm")  # 获取receive_dtm
        lpn_info_lists = jmespath(resp, "body.lpn_info_list")  # 获取lpn_info_list
        # 遍历数据，找到符合lpn_no条件的字典
        lpn_info_list = next((item for item in lpn_info_lists if any(lpn["lpn_no"] == lpn_no for lpn in item["lpn_no_list"])))
        # 转移前查询E0006库存
        qty_old = wms.wms_db.get_quantity_and_allocated_qty_sum(item_number, warehouse_number, location_no, batch_no, lpn_no)[0]  # SQL查询调整前库存
        # 调用转移接口
        Moving().move_lpn(location_from, location_to, qty, item_number, warehouse_number, expire_dtm, receive_dtm, lpn_info_list, in_user)
        # 转移后查询E0006库存
        qty_new = wms.wms_db.get_quantity_and_allocated_qty_sum(item_number, warehouse_number, location_no, batch_no, lpn_no)[0]  # SQL查询调整前库存
        # 断言库存是否转移成功
        assert int(qty_new) == int(qty) + int(qty_old), "转移前后库存不一致"
        weeeTest.log.info(f'{location_from}转移到{location_to}成功!!!')

    @weeeTest.mark.list("WMS-Regression", "WMS-Smoke", "WMS")
    def test_move_out_lpn2(self):
        """
        【112895】MoveOut：【LPN】Stock转移到D-Spoilage
        """
        # 登录
        user_id, user_name = wms.wms_login.common_login()
        edit_user = user_name + '(' + user_id + ")"  # 拼接in_user
        in_user = edit_user
        # 参数设置
        warehouse_number = "29"
        location_from = "F0303-1-3"
        location_to = "D-Spoilage"
        item_number = "96627"
        move_in = False
        batch_no = "250207158435"
        location_no = location_to
        # 调用item查询接口获取LPN信息
        resp1 = Moving().query_item_qty(item_number=item_number, location_to=location_from, warehouse_number=warehouse_number)
        # 若from location avaiable qty 太少，则加库存
        if resp1[0] <= 1:
            wms.adjust_api.create_batch_inv(item_number, warehouse_number, edit_user, location_from, 100, is_lpn=True, pieces_per_pack=10,
                                            expire_dtm=wms.util.get_special_date(days=365), receive_date=wms.util.get_special_date(), lpn_qty=10)
            resp1 = Moving().query_item_qty(item_number=item_number, location_to=location_from, warehouse_number=warehouse_number)
        lpn_list = resp1[1]  # 获取lpn_list
        lpn_no = lpn_list[0]["lpnNo"]
        upc_code = lpn_no
        # 遍历lpn_list数据，找到匹配的lpnNo并提取对应的quantity值
        qty = None
        for entry in lpn_list:
            if entry["lpnNo"] == lpn_no:
                qty = entry["quantity"]
                break
        resp = Moving().check_item(warehouse_number, location_from, upc_code, move_in)
        expire_dtm = jmespath(resp, "body.expire_dtm")  # 获取expire_dtm
        receive_dtm = jmespath(resp, "body.receive_dtm")  # 获取receive_dtm
        lpn_info_lists = jmespath(resp, "body.lpn_info_list")  # 获取lpn_info_list
        # 遍历数据，找到符合lpn_no条件的字典
        lpn_info_list = next((item for item in lpn_info_lists if any(lpn["lpn_no"] == lpn_no for lpn in item["lpn_no_list"])))
        # 转移前查询E0006库存
        qty_old = wms.wms_db.get_quantity_and_allocated_qty_sum(item_number, warehouse_number, location_no, batch_no, lpn_no)[0]  # SQL查询调整前库存
        # 调用转移接口
        Moving().move_lpn(location_from, location_to, qty, item_number, warehouse_number, expire_dtm, receive_dtm, lpn_info_list, in_user)
        # 转移后查询E0006库存
        qty_new = wms.wms_db.get_quantity_and_allocated_qty_sum(item_number, warehouse_number, location_no, batch_no, lpn_no)[0]  # SQL查询调整前库存
        # 断言库存是否转移成功
        assert int(qty_new) == int(qty) + int(qty_old), "转移前后库存不一致"
        weeeTest.log.info(f'{location_from}转移到{location_to}成功!!!')


if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True, open=False)

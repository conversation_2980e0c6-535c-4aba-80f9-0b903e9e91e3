# !/usr/bin/python3
# -*- coding: utf-8 -*-

import weeeTest
from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms


class TestCollectExtra(weeeTest.TestCase):
    """
    CollectExtra流程
    """

    @weeeTest.mark.list("WMS-Regression", "WMS-Smoke", "WMS", "Debug")
    def test_transship_receive(self):
        """
        【112238】CollectExtra 流程
        """
        account, username = wms.wms_login.common_login()
        warehouse_number = global_data.collect_extra_data["warehouse_number"]
        location_no = global_data.collect_extra_data["location_no"]
        upc = global_data.collect_extra_data["upc"]

        # 进入collect extra搜索商品
        item_list = wms.collect_extra_api.collect_extra_search_item(warehouse_number, upc)["body"]["items"]
        assert item_list !=[], f"使用UPC:{upc}未搜索到商品"

        item_info = item_list[0]
        item_number = item_info["item_number"]
        pre_item_inv = wms.wms_db.get_inventory_transaction(
                warehouse_number, location_no, item_number
            )
        quantity = 1
        expire_dtm_str = wms.util.get_special_date(days=365)
        receive_dtm_str = wms.util.get_special_date()
        wms.collect_extra_api.collect_extra_complete(warehouse_number, location_no, item_number, quantity, item_info["pieces_per_pack"], expire_dtm_str, receive_dtm_str)
        # 检查库存
        wms.wms_assert.check_non_batch_invenotry(
                warehouse_number,
                location_no,
                item_number,
                quantity,
                reference_no="9999999",
                old_qty=pre_item_inv["quantity"],
                in_dtm=wms.util.get_current_time()
            )

if __name__ == "__main__":
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True, open=True)
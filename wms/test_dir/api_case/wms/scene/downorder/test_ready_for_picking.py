# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  Author
@Version        :  V1.0.0
------------------------------------
@File           :  test_ready_for_picking.py
@Description    :  
@CreateTime     :  2025/4/1 11:35
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/4/1 11:35
"""
import time
import weeeTest

from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms
from tms.test_dir.api.tms.tms import tms

class TestDownOrderMo(weeeTest.TestCase):
    """
    订单下发仓库测试
    """

    #@weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_mo_down_order(self):
        """
        【107307】MO订单下发测试
        """

        warehouse_number = global_data.normal_picking['warehouse_number']
        storage_type = global_data.normal_picking['storage_type']

        # 登录
        user_id, user_name = wms.wms_login.common_login()

        # 指定订单
        order_id = 42627785

        result = wms.wms_db.get_so_order_mapping(order_id=order_id, info=['woi.delivery_id'])
        if result != None:
            delivery_id = result['delivery_id']
            # 重置数据并下发
            tms.login()
            # 重置WMS Data
            wms.wms_db.clean_wms_data(delivery_id=delivery_id)

            # 重置FPO数据
            tms.tms_db.clean_fpo_data(delivery_id=delivery_id)

            # 重置TMS数据
            sub_region_id = tms.tms_db.get_sub_region_id_by_delivery(delivery_id=delivery_id)
            if sub_region_id != []:
                delivery_date = tms.tms_db.get_delivery_date(delivery_id=delivery_id)
                tms.delete_delivery_data(delivery_date=delivery_date, sub_region_ids=sub_region_id)

            # 重置ERP数据
            tms.tms_db.delete_delivery(delivery_id=delivery_id)

            # 下发WMS
            # 订单处理
            tms.tms_db.order_data_process(so_order_id=order_id)

            # 订单同步fpo
            wms.down_order_job.put_order_from_so(so_order_id=order_id)
            # 获取config
            fpo_config_id = tms.tms_db.get_order_fpo_config_info(order_id=order_id, info=['fpo_delivery_config_id'])[0][0]

            last_push_time = tms.tms_db.get_last_push_time(config_id=fpo_config_id)

            # 调用订单下发job
            wms.down_order_job.JobDownOrder(last_push_time=last_push_time)
            time.sleep(5)

            #delivery_id_new = tms.tms_db.get_delivery_id_by_order_id(order_id=order_id)

            MAX_RETRIES = 3
            RETRY_DELAY = 5  # 秒

            def get_delivery_id(order_id):
                for attempt in range(MAX_RETRIES):
                    try:
                        delivery_id_new = tms.tms_db.get_delivery_id_by_order_id(order_id=order_id)
                        if delivery_id_new:
                            return delivery_id_new
                        else:
                            print(f"未查询到delivery_id, 订单ID: {order_id}")
                    except IndexError:
                        print(f"查询结果为空, 订单ID: {order_id}")

                    if attempt < MAX_RETRIES - 1:  # 如果不是最后一次尝试
                        print(f"等待{RETRY_DELAY}秒后重试...")
                        time.sleep(RETRY_DELAY)

                print(f"在{MAX_RETRIES}次尝试后仍未查询到delivery_id")
                return None

            delivery_id_new = get_delivery_id(order_id)
            if delivery_id_new:
                print(f"查询到的delivery_id: {delivery_id_new}")
            else:
                print("未能获取delivery_id")

            # check数据
            erp_data = tms.tms_db.check_downorder_erp(delivery_id=delivery_id_new)
            erp_data_dicts = []
            for entry in erp_data:
                erp_data_dicts.append({
                    'invoice_no': str(entry[0]),
                    'item_number': str(entry[1]),
                    'itemQuantity': str(entry[2])
                })
            print("erp data dict", erp_data_dicts)
            wms_data = wms.wms_db.check_downorder_wms(delivery_id=delivery_id_new)
            print("WMS DATA", wms_data)
            # 对比
            columns_to_compare = ['invoice_no', 'item_number', 'itemQuantity']

            success = True
            # 遍历结果集
            for row1, row2 in zip(erp_data_dicts, wms_data):
                for column in columns_to_compare:
                    if row1[column] != row2[column]:
                        print(f"不匹配的行：{row1} != {row2}")
                        success = False
                        break
            if success:
                print("MO订单下发成功")
            else:
                print("MO订单下发异常，请检查")

            #ready for picking
            #获取订单region
            message = wms.wms_db.get_so_order_region(order_id=order_id,info=['woe.region'])
            region_name = message['region']
            delivery_dtm =wms.util.day_to_utc(delivery_date)

            #删除redis
            wms.down_order.delete_redis(warehouse_number=warehouse_number,region=region_name,delivery_dtm=delivery_dtm)

            #触发ready for picking
            wms.down_order.ready_for_picking(delivery_dtm=delivery_date,region_name=region_name,warehouse_num=warehouse_number)

            #查询redis
            wms.down_order.get_redis(warehouse_number=warehouse_number,region=region_name,delivery_dtm=delivery_dtm)

            #删除redis
            # 删除redis
            wms.down_order.delete_redis(warehouse_number=warehouse_number, region=region_name,
                                        delivery_dtm=delivery_dtm)


        else:
            # 直接下发
            tms.tms_db.order_data_process(so_order_id=order_id)

            # 订单同步fpo
            wms.down_order_job.put_order_from_so(so_order_id=order_id)
            # 获取config
            fpo_config_id = tms.tms_db.get_order_fpo_config_info(order_id=order_id, info=['fpo_delivery_config_id'])[0][0]

            last_push_time = tms.tms_db.get_last_push_time(config_id=fpo_config_id)

            # 调用订单下发job
            wms.down_order_job.JobDownOrder(last_push_time=last_push_time)
            time.sleep(5)

            #delivery_id = tms.tms_db.get_delivery_id_by_order_id(order_id=order_id)

            MAX_RETRIES = 3
            RETRY_DELAY = 5  # 秒

            def get_delivery_id(order_id):
                for attempt in range(MAX_RETRIES):
                    try:
                        delivery_id_new = tms.tms_db.get_delivery_id_by_order_id(order_id=order_id)
                        if delivery_id_new:
                            return delivery_id_new
                        else:
                            print(f"未查询到delivery_id, 订单ID: {order_id}")
                    except IndexError:
                        print(f"查询结果为空, 订单ID: {order_id}")

                    if attempt < MAX_RETRIES - 1:  # 如果不是最后一次尝试
                        print(f"等待{RETRY_DELAY}秒后重试...")
                        time.sleep(RETRY_DELAY)

                print(f"在{MAX_RETRIES}次尝试后仍未查询到delivery_id")
                return None

            delivery_id_new = get_delivery_id(order_id)
            if delivery_id_new:
                print(f"查询到的delivery_id: {delivery_id_new}")
            else:
                print("未能获取delivery_id")

            # check数据
            erp_data = tms.tms_db.check_downorder_erp(delivery_id=delivery_id_new)
            print("ERP DATA",erp_data)
            erp_data_dicts = []
            for entry in erp_data:
                erp_data_dicts.append({
                    'invoice_no': str(entry[0]),
                    'item_number': str(entry[1]),
                    'itemQuantity': str(entry[2])
                })
            print("erp data dict",erp_data_dicts)
            wms_data = wms.wms_db.check_downorder_wms(delivery_id=delivery_id_new)
            print("WMS DATA",wms_data)
            # 对比
            columns_to_compare = ['invoice_no', 'item_number', 'itemQuantity']

            success = True
            # 遍历结果集
            for row1, row2 in zip(erp_data_dicts, wms_data):
                for column in columns_to_compare:
                    if row1[column] != row2[column]:
                        print(f"不匹配的行：{row1} != {row2}")
                        success = False
                        break
            if success:
                print("MO订单下发成功")
            else:
                print("MO订单下发异常，请检查")
if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', debug=True)

# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  Author
@Version        :  V1.0.0
------------------------------------
@File           :  test_downorder_bulk.py
@Description    :
@CreateTime     :  2025/2/17 18:38
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/2/17 18:38
"""
import time
import weeeTest


from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms
from tms.test_dir.api.tms.tms import tms

class TestDownOrderFDI(weeeTest.TestCase):

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_dryice_down_order(self):
        """
        【107304】Frozen Dry Ice订单下发测试
        """

        warehouse_number = global_data.normal_picking['warehouse_number']
        storage_type = global_data.normal_picking['storage_type']

        # 登录
        user_id, user_name = wms.wms_login.common_login()

        # 指定订单
        order_id = 42645024

        result = wms.wms_db.get_so_order_mapping(order_id=order_id, info=['woi.delivery_id'])
        if result is not None:
            delivery_id = result['delivery_id']

            # 重置数据并下发
            tms.login()
            wms.reset_data(delivery_id)

            # 查询订单Frozen SKU
            result1 = tms.tms_db.query_order_sku(order_id=order_id, storage_type="F")
            item_number = result1[0][0]

            # 查询down单模型，确认支持Frozen Dry Ice
            wms.common_api.update_sys_config(config_id=1403, config_key="wmsso:downorder:model",
                                             warehouse_number=warehouse_number,
                                             config_value='{ "model": "all", "freshFrozenMerge": false, "dryIceSplit": true, "dryBulkSplit": true, "freshBulkSplit": true, "fbwRestaurantSplit": true }')

            # 标记Frozen SKU为Dry Ice sku
            wms.down_order.bulk_sku_tag(warehouse_number=warehouse_number, item_number=item_number, user_id=user_id,
                                        attributeCode="28", attributeView="frozen_dry_ice_order_type", updateValue=True)

            wms.down_order_process(order_id)
            delivery_id_new = wms.get_delivery_id(order_id)
            # check Frozen Dry Ice tag
            order_tag = wms.wms_db.get_order_type(so_order_id=order_id, item_number=item_number,
                                                  info=['woi.order_id', 'woi.order_type'])
            if order_tag['order_type'] == 7:
                print("Frozen Dry Ice拆分成功")
            wms.check_data(delivery_id_new)
        else:
            # 直接下发
            # 查询订单Frozen SKU
            result1 = tms.tms_db.query_order_sku(order_id=order_id, storage_type="F")
            item_number = result1[0][0]

            # 查询down单模型，确认支持Frozen Dry Ice
            wms.common_api.update_sys_config(config_id=1403, config_key="wmsso:downorder:model",
                                             warehouse_number=warehouse_number,
                                             config_value='{ "model": "all", "freshFrozenMerge": false, "dryIceSplit": true, "dryBulkSplit": true, "freshBulkSplit": true, "fbwRestaurantSplit": true }')

            # 标记Frozen SKU为Dry Ice sku
            wms.down_order.bulk_sku_tag(warehouse_number=warehouse_number, item_number=item_number, user_id=user_id,
                                        attributeCode="28", attributeView="frozen_dry_ice_order_type", updateValue=True)

            wms.down_order_process(order_id)
            delivery_id_new = wms.get_delivery_id(order_id)
            #check Frozen Dry Ice tag
            order_tag = wms.wms_db.get_order_type(so_order_id=order_id,item_number=item_number,
                                                 info=['woi.order_id','woi.order_type'])
            if order_tag['order_type'] == 7:
                print("Frozen Dry Ice拆分成功")

            wms.check_data(delivery_id_new)


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', debug=True)

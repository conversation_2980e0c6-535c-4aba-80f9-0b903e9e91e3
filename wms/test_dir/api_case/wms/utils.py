# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON> chen
@Version        :  V1.0.0
------------------------------------
@File           :  utils.py
@Description    :  
@CreateTime     :  2023/6/6 11:13
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/6/6 11:13
"""

import time
import datetime, pytz
import random
from wms.test_dir.api.wms.wms import header
from weeeTest import log


class DataUtils(object):
    @staticmethod
    def update_header(**kwargs):
        """
        设置请求头部信息
        """
        for key, value in kwargs.items():
            header[key] = value

    @staticmethod
    def utc_current_day():
        """
        获取当前日期UTC零点时间戳
        """
        current_time = datetime.datetime.now(datetime.timezone.utc).replace(hour=0, minute=0, second=0)
        return int((current_time - datetime.datetime(1970, 1, 1, tzinfo=datetime.timezone.utc)).total_seconds())

    @staticmethod
    def utc_to_day(delivery_dtm):
        """
        零点时间戳转日期
        """
        return datetime.datetime.fromtimestamp(delivery_dtm).strftime("%Y-%m-%d")

    @staticmethod
    def day_to_utc(delivery_date):
        """
        日期转零点时间戳
        """
        delivery_date = datetime.datetime.strptime(delivery_date, '%Y-%m-%d')
        return pytz.utc.localize(datetime.datetime(delivery_date.year, delivery_date.month, delivery_date.day)).timestamp()

    @staticmethod
    def day_to_utc_format(delivery_date):
        """
        日期转零点时间戳
        """
        delivery_date = datetime.datetime.strptime(delivery_date, '%Y/%m/%d')
        return pytz.utc.localize(datetime.datetime(delivery_date.year, delivery_date.month, delivery_date.day)).timestamp()

    @staticmethod
    def get_special_date(days=0):
        """
        获取days天之后的日期
        :param days: 天数
        :return:
        """
        days_later = datetime.date.today() + datetime.timedelta(days=days)
        return str(days_later)

    @staticmethod
    def get_current_time(seconds_buffer=-60):
        """
        获取当前时间 -/+ buffer
        """
        return int(datetime.datetime.timestamp(datetime.datetime.now() + datetime.timedelta(seconds=seconds_buffer)))

    @staticmethod
    def wait(sleep_time=2, reason=None):
        """
        等待时间
        :param sleep_time: 等待时间,单位:秒
        :param reason: 等待原因
        :return:
        """
        if reason:
            log.info(f"等待{sleep_time}秒, 原因为:{reason}")
        else:
            log.info(f"等待{sleep_time}秒.")
        time.sleep(sleep_time)

    @staticmethod
    def random_num(min_num, max_num):
        """
        根据传入的数值生成一个随机数
        :param min_num: 最小值
        :param max_num: 最大值
        :return:
        """
        return random.randint(min_num, max_num)


if __name__ == '__main__':
    # sql = '''select * from wms.wh_storage_location where warehouse_number=%s and location_no="%s"''' %("33", "TS1114")
    # sql = '''select * from wms.wh_hook_task where in_dtm > 1689930477 and reference_id =23838138 and event_key ="SO_PACKAGE" limit 1'''
    # sql = '''select * from wms.wh_order_info where warehouse_number=%s and order_id=%s''' %("33", "17866123")
    # print(WMSDBConnect().select_data_deal(sql))
    print(DataUtils.get_special_date(days=1))

# !/usr/bin/python3
# -*- coding: utf-8 -*-

from jsonpath import jsonpath
from wms.test_dir.api.wms.wms import wms



class WmsAssertUtils(object):
    """
    WMS断言处理
    """
    def check_location_status(self, warehouse_number, location_type, location_no, flag):
        """location status check"""
        location_info = wms.common_api.get_avaiable_location(warehouse_number, location_type, location_no=location_no)
        location_actual_flag = jsonpath(location_info, "$[*].flag")[0]
        assert location_actual_flag == flag, f"预期仓库{warehouse_number}下库位{location_no}状态为{flag}, 实际为{location_actual_flag}"



if __name__ == '__main__':
    w = WmsAssertUtils()
    w.check_location_status("48", 38, "PLT0008", 0)
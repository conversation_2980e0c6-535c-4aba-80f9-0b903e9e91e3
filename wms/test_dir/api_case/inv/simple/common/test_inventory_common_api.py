import weeeTest
from wms.test_dir.api.inv.central_inventory_api import CentralInventory


class TestInvCommonApi(weeeTest.TestCase):
    """
    库存平台相关通用接口
    """

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'INV')
    def test_update_inv(self):
        """
        商品库存售卖状态更新
        """
        CentralInventory().update_product_inv_status()
        assert self.response['result'] is True

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'INV')
    def test_update_overtime_inv(self):
        """
        取消订单支付超时商品库存修复
        """
        CentralInventory().update_overtime_payment_inv()
        assert self.response['result'] is True

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'INV')
    def test_daily_inv_snapshot(self):
        """
        库存备份，仓库商品某一时刻库存快照
        """
        CentralInventory().job_save_daily_inv_snapshot()
        assert self.response['result'] is True

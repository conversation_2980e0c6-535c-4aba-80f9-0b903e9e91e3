import json
import time
from jsonpath import jsonpath
from time import sleep
from weeeTest import jmespath
from wms.test_dir.api.inv.ec_inventory_api import EcInventory
from wms.test_dir.api.inv.central_inventory_api import CentralInventory
from wms.test_dir.api.wms.wms import wms


class InvDataUtils(object):
    def assert_create_order_v5(self, date, product_id, product_type, sales_org_id, zipcode, inventory_mode="available"):
        # 查询下单日期的可售库存
        inv_res = wms.central_inv_api.query_product_region_inv(sales_org_id, date, zipcode, product_id, deliveryQty=True)
        product_inv = jsonpath(inv_res, "$.object[0].qty")
        assert product_inv[0] > 1, f"销售组织:{sales_org_id},zipcode:{zipcode}下, product_id:{product_id}在date:{date}可售库存为{product_inv}"
        # 调用下单接口
        res = EcInventory().create_order(date, product_id, product_type, sales_org_id, zipcode, inventory_mode)
        failed_products = jmespath(res, "object.failedProducts")
        order_id = jmespath(res, "object.successProducts[0].message_id")
        assert failed_products is None, f'下单失败"failedProducts":{failed_products}'
        # 动态轮询检查订单写入DB
        max_retries = 20
        retry_interval = 2  # 每次轮询间隔4秒
        for _ in range(max_retries):
            order_detail = wms.inv_db.query_order_detail(order_id)
            if order_detail:
                status = order_detail[0].get("status")
                if status == "P":
                    print(f"订单明细{order_id}写入DB成功，状态为P")
                    return order_id
            sleep(retry_interval)
        raise TimeoutError("订单明细写入超时")

    def assert_cancel_order_v5(self, date, order_id, product_id, product_type, sales_org_id, zipcode, inventory_mode="available"):
        # 取消订单
        # 调用取消订单接口
        res1 = EcInventory().cancel_order_v5(date, order_id, product_id, product_type, sales_org_id, zipcode, inventory_mode)

        # 提取失败产品列表
        failed_products = jmespath(res1, "object.failedProducts")
        if failed_products is not None:
            raise ValueError(f"取消订单失败: {failed_products}")

        # 提取成功产品的 message_id
        success_products = jmespath(res1, "object.successProducts")
        if not success_products or "message_id" not in success_products[0]:
            raise KeyError("取消订单成功但未返回 message_id")
        order_id = success_products[0]["message_id"]

        # 动态轮询检查订单状态，取消订单MQ消息有延迟
        max_retries = 10
        retry_interval = 6  # 每次轮询间隔4秒
        for _ in range(max_retries):
            order_detail = wms.inv_db.query_order_detail(order_id)
            if not order_detail:
                raise ValueError(f"查询订单{order_id}明细结果为空")

            status = order_detail[0].get("status")
            if status == "X":
                print("订单状态已更新为X")
                return

            print(f"订单状态尚未更新为X，当前状态: {status}")
            sleep(retry_interval)
        raise TimeoutError("订单状态更新超时，未变为X")

    def assert_inventory_query_v5(self, date, zipcode, product_id, sales_warehouse_number, inv_qty, sales_model="inventory"):
        # inventory query v5接口
        v5_res = wms.ec_inv_api.ec_inventory_query_v5(date, zipcode, product_id)
        assert jsonpath(v5_res, "$.object[0].sales_model") == [sales_model], f"售卖模式不是{sales_model}"
        assert jsonpath(v5_res, "$.object[0].warehouse_number") == [sales_warehouse_number], f'期望售卖仓库为{sales_warehouse_number}, 实际为{v5_res}'
        assert jsonpath(v5_res, "$.object[0].qty") == [inv_qty], f'期望售卖数量为{inv_qty}, 实际为{v5_res}'
        if inv_qty > 0:
            assert jsonpath(v5_res, "$.object[0].is_sold_out") == [False], f'售卖数量为{inv_qty}, 但是售罄字段不是False'

    def assert_inventory_query_v4(self, date, sales_org_id, product_id, sales_warehouse_number, inv_qty, sales_model="inventory"):
        # inventory query v4接口
        v4_res = wms.ec_inv_api.ec_inventory_query_v4(date, sales_org_id, product_id)
        assert jsonpath(v4_res, "$.object[0].sales_model") == [sales_model], f"售卖模式不是{sales_model}"
        assert jsonpath(v4_res, "$.object[0].warehouse_number") == [sales_warehouse_number], f'期望售卖仓库为{sales_warehouse_number}, 实际为{v4_res}'
        assert jsonpath(v4_res, "$.object[0].qty") == [inv_qty], f'期望售卖数量为{inv_qty}, 实际为{v4_res}'
        if inv_qty > 0:
            assert jsonpath(v4_res, "$.object[0].is_sold_out") == [False], f'售卖数量为{inv_qty}, 但是售罄字段不是False'

    def assert_product_all_region_inv(self, date, sales_org_id, product_id, region_id, product_inv):
        # query_product_region接口
        query_product_region_res = wms.ec_inv_api.query_product_region(product_ids=[product_id])
        region_inv_res = jsonpath(query_product_region_res, f'$.object[?(@.sales_org_id=={sales_org_id} && @.region_id=="{region_id}")].inventory_infos[?('
                                                            f'@.date=="{date}")].qty')
        assert region_inv_res == [product_inv], f'期望售卖数量为{product_inv}, 实际为{region_inv_res}'

    def assert_inventory_daily_sales(self, sales_org_id, date, zipcode, product_id):
        # 查询每日可售库存接口
        inv_res = wms.central_inv_api.query_product_region_inv(sales_org_id, date, zipcode, product_id)
        product_inv = jsonpath(inv_res, "$.object[0].qty")
        assert product_inv, f"销售组织:{sales_org_id},zipcode:{zipcode}下, product_id:{product_id}无可售库存"
        return product_inv[0]

    def assert_redis_inv(self, product_id, warehouse_number, product_inv):
        # Redis库存查询
        inv_res = wms.central_inv_api.redis_detail_query(product_id)
        productInvs = jsonpath(inv_res, '$.object.productInvs')[0]
        for inv in productInvs:
            if f"inv:inventory:warehouseSellable:sku_{product_id}_wh_{warehouse_number}" in inv:
                value = inv.split("->")[1]
                print(int(value))  # 打印Redis库存
                assert int(value) == product_inv, f"Redis库存查询结果与预期不符, 期望为{product_inv}, 实际为{value}"
                return

    def assert_local_inv(self, product_id, date, warehouse_number, product_inv):
        # local库存查询校验
        tpa_res = wms.central_inv_api.query_product_local_inv(productIds=[product_id], dates=[date])
        qty = jsonpath(tpa_res,
                       f'$...inventory_list[?(@.warehouse_number == "{warehouse_number}")].available_qty')
        assert qty == [product_inv], f"本地库存查询结果与预期不符, 期望为{product_inv}, 实际为{qty}"

    def assert_sales_inv(self, sales_org_id, date, product_id, product_inv):
        # local库存查询校验
        tpa_res1 = wms.central_inv_api.query_product_sales_inv(sales_org_id, date, product_ids=[product_id])
        qty1 = jsonpath(tpa_res1, "$.object[0].qty")
        assert qty1 == [product_inv], f"本地库存查询结果与预期不符, 期望为{product_inv}, 实际为{qty1}"


    def del_inv_redis_key_del(self, product_id, warehouse_number):
        """
        拼接Redis key进行删除
        """
        # 登录
        wms.wms_login.common_login()
        data = wms.central_inv_api.redis_detail_query(item_number=product_id)
        key_list = []

        for sales_model in data['object']['salesModels']:
            try:
                # 检查 sales_model 是否为有效字符串
                if not isinstance(sales_model, str) or '->' not in sales_model:
                    raise ValueError("Invalid sales_model format. Expected a string containing '->'.")

                # 分割字符串并提取第二部分
                parts = sales_model.split('->')
                if len(parts) < 2 or not parts[1].strip():
                    raise ValueError("Invalid sales_model format. The part after '->' is missing or empty.")

                target_data = parts[1].strip()

                # 使用 jmespath 进行搜索
                # 确保 target_data 是合法的 JSON 格式
                try:
                    sales_info_list = json.loads(target_data)
                except (ValueError, TypeError) as e:
                    raise ValueError(f"Failed to parse data: {e}")

                # 遍历 sales_info_list 并检查每个元素
                for sales_info in sales_info_list:
                    if sales_info is not None and sales_info.get('product_id') == product_id and sales_info.get('warehouse_number') == str(warehouse_number):
                        key_list.append(sales_model.split('->')[0])

            except Exception as e:
                # 统一异常处理，记录错误信息
                print(f"Error processing sales_model: {e}")

        for product_inv in data['object']['productInvs']:
            # 解析键值对
            key, value = product_inv.split('->')
            if key.startswith(f'inv:inventory:warehouseSellable:sku_{product_id}_wh_{warehouse_number}'):
                key_list.append(key)

        print(key_list)
        # 删除 Redis key
        wms.central_inv_api.inv_redis_key_del(key_list)

    def reserve_start(self, product_id, reserve_qty, sales_org_id):
        # 赠品活动库存预占
        start_date = wms.util.get_special_date(days=0)
        end_date = wms.util.get_special_date(days=1)
        reserve_start_res = CentralInventory().reserve_start(start_date, end_date, product_id, reserve_qty, sales_org_id)
        real_reserve_qty = jsonpath(reserve_start_res, "$.object[0].real_reserve_qty")
        res1 = CentralInventory().query_local_warehouse_inv(productIds=[product_id], salesOrgId=sales_org_id)
        assert jsonpath(res1, "$.object.data[0].reserved_qty") == real_reserve_qty, f"预占数量不正确, 期望{real_reserve_qty}, 实际{res1}"

    def reserve_end(self, product_id, sales_org_id):
        # 赠品活动库存预占
        start_date = wms.util.get_special_date(days=0)
        end_date = wms.util.get_special_date(days=1)
        reserve_qty_res = CentralInventory().query_local_warehouse_inv(productIds=[product_id], salesOrgId=sales_org_id)
        reserve_qty = jsonpath(reserve_qty_res, "$.object.data[0].reserved_qty")[0]
        CentralInventory().reserve_end(start_date, end_date, product_id, reserve_qty, sales_org_id)  # 退还所有的reserve_qty
        res1 = CentralInventory().query_local_warehouse_inv(productIds=[product_id], salesOrgId=sales_org_id)
        assert jsonpath(res1, "$.object.data[0].reserved_qty")[0] == 0, f"预占数量不正确, 期望{0}, 实际{res1}"

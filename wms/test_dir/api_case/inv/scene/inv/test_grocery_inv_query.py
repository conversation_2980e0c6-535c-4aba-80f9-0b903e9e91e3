import weeeTest
from weeeTest import jmespath
from jsonpath import jsonpath
from wms.test_dir.api.wms.wms import wms
from wms.test_dir.api.inv.ec_item_api import EcItem
from wms.test_dir.api_case.inv.utils import InvDataUtils


class TestGroceryInvQuery(weeeTest.TestCase):
    """
    Grocery商品库存相关接口库存查询
    """

    def setup_class(self):
        # 登录
        self.user_id = wms.wms_login.common_login()[0]
        self.sales_org_id = 1
        self.zipcode = "94538"
        self.product_id = 9450
        self.region_id = "5"
        self.warehouse_number = "25"
        self.date = wms.util.get_special_date(days=3)
        # 查询可售库存校验
        self.product_inv = InvDataUtils().assert_inventory_daily_sales(self.sales_org_id, self.date, self.zipcode, self.product_id)

    # EC-INV相关查询接口
    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'INV')
    def test_query_v5_inv(self):
        """
        /ec/inventory/query/v5接口库存查询
        """
        # 登录
        wms.login()

        # V5接口校验
        InvDataUtils().assert_inventory_query_v5(self.date, self.zipcode, self.product_id, self.warehouse_number, self.product_inv)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'INV')
    def test_query_v4_inv(self):
        """
        /ec/inventory/query/v4接口库存查询
        """
        # 登录
        wms.login()

        # V4接口校验
        InvDataUtils().assert_inventory_query_v4(self.date, self.sales_org_id, self.product_id, self.warehouse_number,
                                                 self.product_inv)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'INV')
    def test_all_region_inv(self):
        """
        /query/all/region/inv接口库存查询
        """
        # 登录
        wms.login()

        # region_inv接口校验
        InvDataUtils().assert_product_all_region_inv(self.date, self.sales_org_id, self.product_id, self.region_id, self.product_inv)

    # Central-INV相关查询接口
    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'INV')
    def test_redis_inv(self):
        """
        /redis/detail/query接口库存查询
        """
        # 登录
        wms.login()

        # V5接口校验
        InvDataUtils().assert_redis_inv(self.product_id, self.warehouse_number, self.product_inv)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'INV')
    def test_local_inv(self):
        """
        /query/product/local/inv接口库存查询
        """
        # 登录
        wms.login()

        # V5接口校验
        InvDataUtils().assert_local_inv(self.product_id, self.date, self.warehouse_number, self.product_inv)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'INV')
    def test_sales_inv(self):
        """
        /local/warehouse/sales/inv接口库存查询
        """
        # 登录
        wms.login()

        # V5接口校验
        InvDataUtils().assert_sales_inv(self.sales_org_id, self.date, self.product_id, self.product_inv)


if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True, open=False)

# 资源池售卖库存查询
# 库存售卖库存查询

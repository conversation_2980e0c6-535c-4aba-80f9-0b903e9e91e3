import weeeTest
from wms.test_dir.api.wms.wms import wms
from wms.test_dir.api_case.inv.utils import InvDataUtils


class TestReserve(weeeTest.TestCase):
    # 赠品活动预占库存测试
    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'INV')
    def test_reserve_start(self, product_id=100178, reserve_qty=10, sales_org_id=1):
        """
        【102273】创建赠品活动_预占reserve库存
        """
        wms.login()  # 登录
        InvDataUtils().reserve_start(product_id, reserve_qty, sales_org_id)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'INV')
    def test_create_order_inventory(self, product_id=100178, product_type='normal', sales_org_id=1, zipcode="94538"):
        """
        【102271/102272】reserve库存_下单&取消
        """
        # 参数设置
        wms.login()  # 登录
        # 下单校验
        order_id = InvDataUtils().assert_create_order_v5(wms.util.get_special_date(days=1), product_id, product_type, sales_org_id, zipcode,
                                                         inventory_mode="reserve")
        # 取消订单校验
        InvDataUtils().assert_cancel_order_v5(wms.util.get_special_date(days=1), order_id, product_id, product_type, sales_org_id, zipcode,
                                              inventory_mode="reserve")

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'INV')
    def test_reserve_end(self, product_id=100178, sales_org_id=1):
        """
        【102274】结束赠品活动_退还reserve库存
        """
        wms.login()  # 登录
        InvDataUtils().reserve_end(product_id, sales_org_id)


if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True, open=False)
# 活动库存预占
# 活动库存下单/取消
# 活动预占结束

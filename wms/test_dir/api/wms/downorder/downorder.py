import time
import json

import weeeTest
from weeeTest import log

from wms.test_dir.api.wms.wms import header


class WmsDownOrder(weeeTest.TestCase):
    """
    downorder 相关接口
    """

    def pull_order_from_so(self):
        """
          从 so 拉取订单到 fpo
        """
        url='/wms-order/fpo/order/pullOrderFromSO'
        self.get(url=url, headers=header)
        time.sleep(5)
        return self.response

    def fpo_region_by_warehouse(self, warehouse_number, delivery_date):
        """
          获取region 和配送日信息
        """
        url='/wms-order/fpo/region/byWarehouse/%s/%s' % (warehouse_number, delivery_date)
        self.get(url=url, headers=header)
        return self.response

    def invoice_generate(self, delivery_date, region_ids, warehouse_number, user_id):
        """
        生成发货批次
        """
        url='/wms-order/fpo/invoice/generate'
        body = {
            "delivery_date": delivery_date,
            "region_ids": region_ids,
            "warehouse_number": warehouse_number,
            "user_id": user_id
        }
        self.post(url=url, json=body, headers=header)
        log.info(self.response)
        return self.response

    def list_delivery(self, region_id_list, delivery_start_date, delivery_end_date):
        """
        central 发货批次列表
        """
        url='/wms-order/fpo/delivery/listDelivery'
        body = {
            "regionIdList": region_id_list,
            "deliveryStartDate": delivery_start_date,
            "deliveryEndDate": delivery_end_date,
            "startColumn": 0,
            "pageSize": 10
        }
        self.post(url=url, json=body, headers=header)
        return self.response

    def fpo_order_page(self, delivery_date, order_id):
        """
         central fpo order management page
        """
        url=f'/wms-order/fpo/order/page?delivery_date={delivery_date}&order_id={order_id}&startColumn=0&pageSize=10'
        self.get(url=url, headers=header)
        return self.response

    def fpo_invoice_page(self, start_date, end_date):
        """
        central fpo invoice management page
        """
        url=f'/wms-order/fpo/invoice/page?start_date={start_date}&end_date={end_date}&startColumn=0&pageSize=10'
        self.get(url=url, headers=header)
        return self.response

    def bulk_sku_tag(self,warehouse_number,item_number,user_id,attributeCode,attributeView,updateValue):
        """
        bulk商品标记
        """
        body = {
                "warehouse_number": warehouse_number,
                "item_numbers": [item_number],
                "details": [{
                    "attributeCode": attributeCode,
                    "attributeView": attributeView,
                    "updateValue": updateValue
                }],
                "edit_user": user_id,
                "secondConfirm": False
            }
        url = '/wms/items/management/updateItemsAttributes'
        self.post(url=url, json=body, headers=header)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def set_redis(self,warehouse_number,region,delivery_dtm):
        body = {
            "key": "fpo:readyForPicking:{}:{}:{}".format(warehouse_number, region, delivery_dtm),
            "value": "1"
        }
        url = '/wms/redis/set'
        self.post(url=url, json=body, headers=header)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def get_redis(self,warehouse_number,region,delivery_dtm):
        url = f'/wms/redis/getValue/fpo:readyForPicking:{warehouse_number}:{region}:{delivery_dtm}'
        self.get(url=url, headers=header)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def delete_redis(self,warehouse_number,region,delivery_dtm):
        url = f'/wms/redis/fpo:readyForPicking:{warehouse_number}:{region}:{delivery_dtm}'
        self.get(url=url, headers=header)
        return self.response

    def ready_for_picking(self,delivery_dtm,region_name,warehouse_num):
        body = {
                    "delivery_dtm": {delivery_dtm},
                    "region_name": region_name,
                    "warehouse_num": warehouse_num
                }
        url = '/wms/outbound/orders/readyForPicking'
        self.post(url=url, json=body, headers=header)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response


if __name__ == '__main__':
    WmsDownOrder()

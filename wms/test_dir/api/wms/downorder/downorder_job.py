# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  Author
@Version        :  V1.0.0
------------------------------------
@File           :  downOrderJOB.py
@Description    :
@CreateTime     :  2023/11/26 20:32
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/11/26 20:32
"""
import json
import weeeTest
from wms.test_dir.api.wms.wms import header


class DownOrderJOB(weeeTest.TestCase):
    """
    FPO JOB下发订单到WMS相关接口
    """

    def put_order_from_so(self, so_order_id):
        """
        so同步订单到fpo_order
        """
        # so_order_id = input("请输入需要WMS出库的order_id:")
        # print("输入的order_id是：", so_order_id)
        url = "/central/so/order/downByOrderId"
        body = [{
            "order_id": so_order_id,
            "type": "down_order_queue_ack"}]
        self.put(url=url, json=body, headers= header)
        assert self.response['result'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def JobDownOrder(self,last_push_time):
        """
        根据last_push_time JOB下发订单，生成后获取发货批次信息
        """
        url='/wms-order/job/fpo/autoGenerateAndSend?time=' + str(last_push_time)
        self.get(url=url, headers=header)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def cutoff_config(self,delivery_date,region_id,warehouse_no):
        """
        截单
        :param delivery_date:
        :param region_id:
        :param warehouse_no:
        :return:
        """
        url='/wms-order/fpo/order/cutoff'
        body = {
            "delivery_dtm": delivery_date,
            "region_id": region_id,
            "warehouse_num": warehouse_no
        }
        self.post(url=url, json=body, headers=header)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def checkInvoice(self,delivery_id):
        url=f'/wms-order/job/checkInvoice?deliveryIds={delivery_id}'
        self.post(url=url,headers=header)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response


    def order_preoccupancy(self):
        return self.response

    def order_batch(self):
        return self.response

if __name__ == '__main__':
    downorder = DownOrderJOB()
    downorder.put_order_from_so(so_order_id=42624315)

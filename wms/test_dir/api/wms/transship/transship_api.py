# !/usr/bin/python3
# -*- coding: utf-8 -*-
import json
from wms.test_dir.api.wms.wms import *


class TransshipAPI(weeeTest.TestCase):
    """
    调拨功能相关接口
    """
    def ts_order(self,item_number,reference_no):
        '''调拨下单接口
        '''
        url = "/wms/transShipment/createTsOrder"
        body = {
            "cancelOrderNotifyErp": False,
            "create_dtm": 1731686400,
            "create_user_id": 10953840,
            "create_user_name": "hao.fan",
            "eta_date": "2025-03-15",
            "expected_date": "2025-03-15",
            "items": [
                    {
                        "boxes": 2,
                        "item_number": item_number,
                        "outbound_policy": 1,
                        "pieces_per_pack": "1",
                        "quantity": 2
                    }
                ],
                "reference_no": reference_no,
                "send_dtm": 1726196402,
                "send_user_id": 10953840,
                "send_user_name": "hao.fan",
                "ship_in_config": {
                    "ship_in_system": "WMS",
                    "ship_out_system": "WMS"
                },
                "ship_in_warehouse": "29",
                "ship_out_config": {
                    "ship_in_system": "WMS",
                    "ship_out_system": "WMS"
                },
                "ship_out_warehouse": "8",
                "title": "LA - La Mirada -> IL - Chicago Internal Transfer Order",
                "user_id": "10953840"
            }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def ts_order_cancel(self,reference_no):
        """调拨单取消接口
        """
        url = f"/wms/transShipment/cancelTsOrder"
        body = {
                "reference_no":reference_no,
                "user_id":"10953840"
                }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口状态错误取消失败,详情：{json.dumps(self.response)}"
        return self.response
    def query_ts_number(self, warehouse_number, ship_in_warehouse, start_date, end_date):
        """获取待预占调拨单
        """
        url = "/wms/transship-central/page4TsOrder"
        body = {
            "ship_out_warehouse": warehouse_number,
            "ship_in_warehouse": ship_in_warehouse,
            "start_estimate_pickup_date": start_date,
            "end_estimate_pickup_date": end_date,
            "ts_number": "",
            "storage_type": 3,
            "status": 0,
            "slot_nos": [],
            "startColumn": 0,
            "pageSize": 200,
            "page": 1
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def query_ts_details(self, warehouse_number, ts_number, storage_type):
        """获取调拨单详情
        """
        url = "/wms/transship-central/tsOrderDetail"
        body = {
            "ship_out_warehouse": warehouse_number,
            "reference_no": ts_number,
            "storage_type": storage_type
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def ts_auto_preoccupy(self, ts_number):
        """自动预占
        """
        url = f"/wms/transship-central/startPreoccupy/{ts_number}"
        self.get(url=url, headers=header)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"

    def ts_manual_preoccupy(self, warehouse_number, ts_number, item_list):
        """手动指定库位预占
        "item_list": [{
                "item_number": "1079",
                "locations": [{
                    "location_no": "F0102-2-3",
                    "receive_dtm": 1731657600,
                    "expire_dtm": 1764489600,
                    "pieces_per_pack": "5.00",
                    "preoccupation_qty": 15
                }]
            }]

        """
        url = "/wms/transship-central/confirmManualPreoccupy"
        body = {
            "warehouse_number": warehouse_number,
            "ts_number": ts_number,
            "item_list": item_list
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def update_ts_priority(self, ts_number):
        """调整调拨单拣货优先级"""
        url = "/wms/transship-central/tsOrder/priority"
        body ={
            "priority": 9999940000,
            "ts_number": ts_number
        }
        self.put(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def get_current_task(self, warehouse_number, user_id, storage_type):
        """获取当前可领取的任务"""
        url = "/wms/dc_pick/current_task"
        body = {
            "warehouse_number": warehouse_number,
            "user_id": user_id,
            "storage_type": storage_type
            }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def check_ts_pallet(self, warehouse_number, user_id, pallet_no, task_type, storage_type):
        """校验扫描的pallet"""
        url = "/wms/dc_pick/check_pallet"
        body = {
            "warehouse_number": warehouse_number,
            "user_id": user_id,
            "pallet_no": pallet_no,
            "task_type": task_type,
            "storage_type": storage_type
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def get_ts_task(self, warehouse_number, user_id, pallet_no, task_type, storage_type):
        """领取任务"""
        url = "/wms/dc_pick/get_list"
        body = {
            "warehouse_number": warehouse_number,
            "user_id": user_id,
            "pallet_no": pallet_no,
            "task_type": task_type,
            "storage_type": storage_type
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def pick_lpn_task(self, warehouse_number, location_no, task_rec_id):
        """pick lpn类型的任务"""
        url = "/wms/dc_pick/pick_detail_lpn"
        body = {
            "location_no": location_no,
            "task_rec_id": task_rec_id,
            "warehouse_number": warehouse_number,
            "pickedLpnList": [],
            "new_lpn_no": "",
            "undo": None,
            "pickedMasterLpnList": []
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def ts_pick_complete(self, warehouse_number, user_id, ts_number, pallet_no, storage_type):
        """pick lpn类型的任务"""
        url = "/wms/dc_pick/pick_complete"
        body = {
            "warehouse_number": warehouse_number,
            "user_id": user_id,
            "ts_number": ts_number,
            "pallet_no": pallet_no,
            "storage_type": storage_type
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def ts_pick_confirm(self, warehouse_number, location_no, pallet_no, task_rec_id, user_id, ts_number, pickedLpnList):
        """pick lpn类型的任务提交"""
        url = "/wms/dc_pick/pick_confirm_lpn"
        body = {
            "warehouse_number": warehouse_number,
            "location_no": location_no,
            "pallet_no": pallet_no,
            "task_rec_id": task_rec_id,
            "lpnPickingErr": 0,
            "pickedLpnList": pickedLpnList,
            "pickedMasterLpnList": []
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def ts_pick_confirm_fullpallet(self, warehouse_number, location_no, pallet_no, task_rec_id, user_id, ts_number, pickedLpnList):
        """pick lpn类型的任务提交"""
        url = "/wms/dc_pick/pick_confirm_lpn"
        body = {
            "warehouse_number": warehouse_number,
            "location_no": location_no,
            "pallet_no": pallet_no,
            "task_rec_id": task_rec_id,
            "lpnPickingErr": 2,
            "pickedLpnList": pickedLpnList,
            "pickedMasterLpnList": []
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def ts_scan_slot(self, warehouse_number, user_id, pallet_no, ts_number, slot_no, storage_type):
        """pick完成放入slot"""
        url = "/wms/dc_pick/scan_slot"
        body = {
            "warehouse_number": warehouse_number,
            "user_id": user_id,
            "pallet_no": pallet_no,
            "ts_number": ts_number,
            "slot_no": slot_no,
            "storage_type": storage_type,
            "full_slot": False
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def get_ts_qc_order(self, warehouse_number, storage_type):
        """获取待做QC的数据"""
        url = f"/wms/dc_qc/lpn/list/tsOrder/{warehouse_number}/{storage_type}"
        self.get(url=url, headers=header)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def ts_qc_scan_slot(self, ts_number, ship_out_warehouse, ship_in_warehouse, slot_no, expected_date, is_pallet_full=False, full_qc_pallet=""):
        """ts qc scan slot"""
        url = "/wms/dc_qc/lpn/scanSlot"
        body = {
            "ts_number": ts_number,
            "ship_out_warehouse": ship_out_warehouse,
            "ship_in_warehouse": ship_in_warehouse,
            "slot_no": slot_no,
            "expected_date": expected_date,
            "is_pallet_full": is_pallet_full
        }
        if is_pallet_full:
            body["full_qc_pallet"] = full_qc_pallet
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def pritn_qc_pallet(self, ts_number, ship_out_warehouse, slot_no, qc_pallet_no, qc_pallet_rec_id):
        """打印qc pallet"""
        url = "/wms/dc_qc/lpn/print/qcPallet"
        body = {
        "ts_number": ts_number,
        "ship_out_warehouse": ship_out_warehouse,
        "slot_no": slot_no,
        "qc_pallet_no": qc_pallet_no,
        "is_reprint": False,
        "qc_pallet_rec_id": qc_pallet_rec_id,
        "print_source": 0,
        "printer_no": 1870
    }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response
    
    def get_qc_tasks(self, ts_number, ship_out_warehouse, slot_no, qc_pallet_no, plt_no=None):
        """获取slot上qc数据
        plt_no不传时获取slot上的待做任务List
        传值时获取plt_no对应的任务
        """
        url = "/wms/dc_qc/lpn/list/slot/tasks"
        body = {
            "ts_number": ts_number,
            "ship_out_warehouse": ship_out_warehouse,
            "slot_no": slot_no,
            "qc_pallet_no": qc_pallet_no,
            "action": "1",
            "start_column": 0,
            "page_size": 100
        }
        if plt_no:
            body["plt_no"] = plt_no
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def get_lpn_plt_details(self, ship_out_warehouse, slot_no, ts_number, qc_pallet_no, item_number, lpn_no):
        """扫描LPN，先跳转到对应PLT详情页，输数量QC"""
        url = "/wms/dc_qc/lpn/query/qc/plt/detail"
        body = {
            "ship_out_warehouse": ship_out_warehouse,
            "ts_number": ts_number,
            "item_number": item_number,
            "slot_no": slot_no,
            "lpn_no": lpn_no,
            "qc_pallet_no": qc_pallet_no
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def get_lpn_details(self, ship_out_warehouse, slot_no, ts_number, qc_pallet_no, item_number, lpn_no):
        """扫描LPN，先跳转到对应PLT详情页，输数量QC， 输入数量不等于plt上LPN的总数量，跳转到LPN详情页"""
        url = "/wms/dc_qc/lpn/query/qc/detail"
        body = {
            "ship_out_warehouse": ship_out_warehouse,
            "ts_number": ts_number,
            "item_number": item_number,
            "slot_no": slot_no,
            "lpn_no": lpn_no,
            "qc_pallet_no": qc_pallet_no
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def pallet_qc_confirm(self, ship_out_warehouse, slot_no, ts_number, qc_pallet_no, item_list, plt_no):
        """使用pallet_no进行qc"""
        url = "/wms/dc_qc/lpn/qc/plt/confirm"
        # "item_list": [{
        #         "item_number": "1079",
        #         "lpn_no_list": ["G2411-1079-M1-1", "G2411-1079-M1-2"]
        #     }]
        body = {
            "qc_pallet_no": qc_pallet_no,
            "ship_out_warehouse": ship_out_warehouse,
            "slot_no": slot_no,
            "ts_number": ts_number,
            "plt_no": plt_no,
            "item_list": item_list,
            "new_master_lpn": False
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def lpn_qc_confirm(self, ship_out_warehouse, slot_no, ts_number, qc_pallet_no, lpn_list, item_number):
        """使用pallet_no进行qc"""
        url = "/wms/dc_qc/lpn/qc/confirm"
        # ""lpn_list": [{
        #         "location_no": "TSP016",
        #         "lpn_no_list": ["G2411-1079-M5-1"]
        #     }]
        body = {
            "item_number": item_number,
            "lpn_list":  lpn_list,
            "qc_pallet_no": qc_pallet_no,
            "ship_out_warehouse": ship_out_warehouse,
            "slot_no": slot_no,
            "ts_number": ts_number,
            "new_master_lpn": False
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response


    def qc_complete(self, ship_out_warehouse, slot_no, ts_number, user_id):
        """qc complete"""
        url = "/wms/dc_qc/lpn/qc/complete"
        body = {
            "ts_number": ts_number,
            "ship_out_warehouse": ship_out_warehouse,
            "slot_no": slot_no,
            "user_id": user_id
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def get_move_inv_location(self, warehouse_number, ts_number):
        """获取inventory move的Location"""
        url = f"/wms/dc_qc/list/outStage/{warehouse_number}/{ts_number}"
        self.get(url=url, headers=header)
        assert self.response['success'] is True
        return self.response

    def ts_qc_inv_move(self, ship_out_warehouse, slot_no, ts_number, out_region_no, user_id):
        """qc complete"""
        url = "/wms/dc_qc/needMove"
        body = {
            "ship_out_warehouse": ship_out_warehouse,
            "ts_number": ts_number,
            "slot_no": slot_no,
            "out_region_no": out_region_no,
            "user_id": user_id
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def ts_check_truck(self, warehouse_number, truck_id, user_id):
        """checkTruck"""
        url = "/wms/truck/load/checkTruck"
        body = {
            "warehouse_number":warehouse_number,
            "truck_id":truck_id,
            "user_id":user_id
            }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def get_truck_pallet_list(self, warehouse_number, truck_id, user_id):
        """getPalletList"""
        url = "/wms/truck/load/getPalletList"
        body ={
            "warehouse_number": warehouse_number,
            "dock_id": "Dock020",
            "truck_id": truck_id,
            "user_id": user_id,
            "action": 1
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def truck_load_scan_pallet(self, warehouse_number, truck_id, user_id, ob_pallet):
        """scan pallet"""
        url = "/wms/truck/load/scanPallet"
        body ={
            "warehouse_number": warehouse_number,
            "truck_id": truck_id,
            "user_id": user_id,
            "dock_id": "Dock020",
            "pallet_no": ob_pallet
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def truck_load_pallet(self, warehouse_number, truck_id, user_id, ob_pallet, location_no):
        """load pallet"""
        url = "/wms/truck/load/loadPallet"
        body ={
            "warehouse_number": warehouse_number,
            "truck_id": truck_id,
            "user_id": user_id,
            "dock_id": "Dock020",
            "pallet_no": ob_pallet,
            "location_no": location_no
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def truck_load_check_complete(self, warehouse_number, truck_id, user_id):
        """truck_load_check_completet"""
        url = "/wms/truck/load/checkComplete"
        body ={
            "warehouse_number": warehouse_number,
            "truck_id":  truck_id,
            "dock_id": "Dock020",
            "user_id": user_id
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def truck_load_complete(self, warehouse_number, truck_id, user_id, dock_log_id):
        """/truck/load/complete"""
        url = "/wms/truck/load/complete"
        body ={
            "warehouse_number": warehouse_number,
            "truck_id":  truck_id,
            "dock_id": "Dock020",
            "user_id": user_id,
            "skip": False,
            "dock_log_id": dock_log_id
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def get_truck_record(self, warehouse_number, truck_id):
        """queryTruckRecordList"""
        url = "/wms/truckRecord/queryTruckRecordList"
        body = {
            "warehouse_number":warehouse_number,
            "truck_id":truck_id
            }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def truck_load_scan_dock(self, warehouse_number, truck_id, dock_id, user_id):
        """scanDock"""
        url = "/wms/truck/load/scanDock"
        body ={
            "warehouse_number": warehouse_number,
            "truck_id":  truck_id,
            "dock_id": dock_id,
            "user_id": user_id
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def truck_load_save_record(self, warehouse_number, truck_id, user_id):
        """saveTruckRecord"""
        url = "/wms/truckRecord/saveTruckRecord"
        body ={
            "warehouse_number": warehouse_number,
            "in_user": user_id,
            "dock_id": "Dock020",
            "truck_type": 1,
            "truck_size": 1,
            "record_type": 2,
            "comments": "",
            "dock_log_id": "",
            "truck_id": truck_id,
            "operate_type": 4
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def ts_status_update_srm(self, ts_number, status, batch_id, user_id):
        """
        上报merch调拨单在仓库的状态
        拣货confim 完成后上报srm "PS"，调拨单状态：Picking
        调拨单QC完成move completed后上报srm "PC"，调拨单状态：Ready for Shipping
        调拨单操作truck load,完成后上报srm "D"，查看调拨单状态：Shipping
        调拨单入库仓收货完成或者关闭时上报srm "M"，调拨单状态：Delivered
        """
        url = "/wms_in/update_transship_status"
        body ={
            "in_user": user_id,
            "ship_out_warehouse": None,
            "in_dtm": "1739416348",
            "ship_in_warehouse": None,
            "batch_id": batch_id,
            "user_id": None,
            "items": None,
            "reference_no": ts_number,
            "status": status
        }
        self.post(url=url, headers=header, json=body, special_url="http://php.internal.tb1.sayweee.net")
        assert self.response['result'] is True, f"{url}接口请求失败,详情：{self.response}"
        return self.response
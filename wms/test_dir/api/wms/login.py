import weeeTest
from weeeTest import log

from weeeTest.utils import jmespath

from wms.qa_config import global_data
from wms.test_dir.api.wms import header


class Login(weeeTest.TestCase):

    # 登录
    def common_login(self, account=global_data.wms_user_id, password=global_data.wms_user_password):
        if account is None or password is None:
            raise Exception('登录的account,password不能为空')
        # 若账号已登录，不用再次登录，节省用例执行时间
        if header.get("authorization") is not None and len(header.get("authorization")) > 0:
            log.info(f'账号已登录,无需再次登录,user_id: {account}, user_name:{header.get("weee_user")}')
            return account, header.get("weee_user")

        # 账号未登录，则登录
        body = {
            "account": account,
            "password": password,
            "realm_key": "ecommerce",
            "login_platform": "WMS"
        }
        self.post(url='/hub/auth/user/login', headers=header,
                  json=body)
        auth = jmespath(self.response, "object.token")
        username = jmespath(self.response, "object.user_name")
        log.info(f'登录成功,user_id: {account}, user_name:{username}')
        if auth is not None and len(auth) > 0:
            log.info(f'登录成功auth:{auth}')
            header["authorization"] = 'Bearer ' + auth
            header["weee_user"] = username
        else:
            log.info(f'登录失败,msg:{jmespath(self.response, "message")}')
        return account, username

    def logout(self, warehouse_number, user_id, user_name):
        body = {
            "last_action_dtm": *************,
            "user_id": user_id,
            "user_name": user_name,
            "action": 1
        }
        self.post(url=f'/wms/common/logout/{warehouse_number}', headers=header, json=body)
        log.info(f"{user_name}:{user_id},退出登录")
        header["authorization"] = ''

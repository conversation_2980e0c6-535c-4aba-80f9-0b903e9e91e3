# !/usr/bin/python3
# -*- coding: utf-8 -*-

import json
from wms.test_dir.api.wms.wms import *
from wms.test_dir.api.wms.wms import header


class BulkPacking(weeeTest.TestCase):
    user = ""
    warehouse = "33"

    def query_packing_task(self, cart_no, station="1-1"):
        url = "/wms/packing/queryPackingTask"
        body = {
            "warehouse_number": self.warehouse,
            "tote_no": cart_no,
            "edit_user": self.user,
            "packing_station_number": station
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def query_bulk_packing_info(self, cart_no, station="1-1"):
        url = "/wms/packing/bulk/queryPackingInfo"
        body = {
            "warehouse_number": self.warehouse,
            "tote_no": cart_no,
            "edit_user": self.user,
            "packing_station_number": station
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        items = self.response['body']['items']
        return items

    def bulk_packing_qc(self, cart_no, items):
        url = "/wms/packing/bulk/qc"
        body = {
            "item_number": items["item_number"],
            "quantity": items["item_quantity"],
            "warehouse_number": self.warehouse,
            "tote_no": cart_no,
            "edit_user": self.user,
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"

    def bulk_complete(self, cart_no, items):
        url = "/wms/packing/bulk/complete"
        for item in items:
            body = {
                "warehouse_number": self.warehouse,
                "tote_no": cart_no,
                "edit_user": self.user,
                "item_number": item.get("item_number", "")  # Assuming "item_quantity" is a key in the dictionary
            }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]["outBoundOrderIds"]

    def bulk_label_creates(self, cart_no, items):
        url = "/wms/packing/bulk/label/create"
        body = {
            "warehouse_number": self.warehouse,
            "tote_no": cart_no,
            "item_number": items["item_quantity"],
            "edit_user": self.user
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"

    def bulk_ship(self, cart_no):
        url = "/wms/packing/ship"
        body = {
            "warehouse_number": self.warehouse,
            "tote_no": cart_no,
            "edit_user": self.user
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"

    def bulk_oos_qc(self, items, cart_no):
        url = "/wms/packing/bulk/qc"
        body = {
            "tote_no": cart_no,
            "edit_user": self.user,
            "quantity": 0,
            "item_number": "",
            "warehouse_number": self.warehouse
        }
        for i in range(len(items)):
            body["item_number"] = items[i]["item_number"]
            self.post(url=url, headers=header, json=body)
            assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return items

    def bulk_scan_replenishtote(self, cart_no, replenish_tote_no):
        url = "/wms/packing/bulk/scanReplenishTote"
        body = {
            "tote_no": cart_no,
            "edit_user": self.user,
            "replenish_tote_no": replenish_tote_no,
            "warehouse_number": self.warehouse
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
# !/usr/bin/python3
# -*- coding: utf-8 -*-

import json
from wms.test_dir.api.wms.wms import *
from weeeTest import log


class NormalPacking(weeeTest.TestCase):
    '''
    Weee_wms_packing_type: 10(normal)
    11(normal 补单),20(MOF/MO称重)
    21(MOF补单/MO称重补单)
    30(one item),11(oneitem 补单)
    40(Bulk),41(Bulk补单)
    60(MO非称重)
    61(MO非称重补单)
    '''

    user = ""
    warehouse = "33"

    def query_packing_task(self, tote_no, station="1-1"):
        url = "/wms/packing/queryPackingTask"
        body = {
            "warehouse_number": self.warehouse,
            "tote_no": tote_no,
            "edit_user": self.user,
            "packing_station_number": station
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def query_packing_info(self, tote_no, station="1-1"):
        url = "/wms/packing/queryPackingInfo"
        body = {
            "warehouse_number": self.warehouse,
            "tote_no": tote_no,
            "edit_user": self.user,
            "packing_station_number": station
        }
        self.post(url=url, headers=header, json=body)
        if self.response['messageId'] != "94002":
            assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        # oneitem 直接返回
        if header["weee_wms_packing_type"] == "30":
            return self.response["body"]

        items = self.response['body']['items']
        order_id = self.response['body']['order_id']
        assert self.response['success'] == True
        recommend_package = self.response['body']['recommendBoxes']
        return items, order_id, recommend_package

    def packing_qc(self, items, order_id):
        url = "/wms/packing/qc"
        body = {
            "order_id": order_id,
            "upc": "",
            "edit_user": self.user,
            "quantity": 0,
            "item_number": "",
            "action": 12
        }
        for item in items:
            body["upc"] = item["upc"]
            body["item_number"] = item["item_number"]
            body["quantity"] = item["item_quantity"]
            self.post(url=url, headers=header, json=body)
            assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"

    def one_item_get_order(self, cart, upc):
        url = "/wms/packing/queryPackingInfo"
        body = {
            "warehouse_number": self.warehouse,
            "tote_no": cart,
            "upc": upc,
            "edit_user": self.user,
            "packing_station_number": "1-1"
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        log.info(self.response)

        items = self.response['body']['items']
        order_id = self.response['body']['order_id']
        recommend_package = self.response['body']['recommendBoxes']
        return items, order_id, recommend_package

    def scan_box(self, recommend_package, order_id):
        url = "/wms/packing/scanBox"
        body = {
            "warehouse_number": self.warehouse,
            "order_id": order_id,
            "box_barcode": "",
            "edit_user": self.user,
            "box_num": None
        }
        # oneitem 固定使用包材MB01
        if header["weee_wms_packing_type"] == "30":
            body["box_barcode"] = "MB01"
            self.post(url=url, headers=header, json=body)
            assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        # 非 One item使用推荐包材
        else:
            for box in recommend_package:
                body["box_barcode"] = box
                self.post(url=url, headers=header, json=body)
                assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"

    def label_create(self, order_id):
        url = f"/wms/packing/{order_id}/label/create"
        body = {
            "order_id": order_id,
            "warehouse_number": self.warehouse,
            "edit_user": self.user
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"

    def mo_label_create(self, order_id):
        url = f"/wms/packing/{order_id}/mail_order/label/create"
        body = {
            "order_id": order_id,
            "warehouse_number": self.warehouse,
            "edit_user": self.user,
            "supervisor_approval": False
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"

    def normal_ship(self, tote_no, order_id):
        url = f"/wms/packing/{order_id}/ship"
        body = {
            "order_id": order_id,
            "warehouse_number": self.warehouse,
            "tote_no": tote_no,
            "edit_user": self.user
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"

    def oos_qc(self, items, order_id):
        url = "/wms/packing/qc"
        body = {
            "order_id": order_id,
            "upc": "",
            "edit_user": self.user,
            "quantity": 0,
            "item_number": "",
            "action": 12
        }
        for i in range(len(items) - 1):
            body["upc"] = items[i]["upc"]
            body["item_number"] = items[i]["item_number"]
            body["quantity"] = items[i]["item_quantity"]
            self.post(url=url, headers=header, json=body)
            assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        # 返回未QC的Item
        return items[-1:][0]

    def oneitem_oos_qc(self, items, order_id):
        url = "/wms/packing/qc"
        body = {
            "order_id": order_id,
            "upc": "",
            "edit_user": self.user,
            "quantity": 0,
            "item_number": "",
            "action": 13
        }
        for i in range(len(items)):
            body["upc"] = items[i]["upc"]
            body["item_number"] = items[i]["item_number"]
            self.post(url=url, headers=header, json=body)
            assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return items

    def packing_replenish(self, tote_no, order_id, oos_item):
        url = "/wms/packing/packingReplenish"
        replenish_list = list()
        replenish_item_details = dict()
        replenish_item_details["item_number"] = oos_item["item_number"]
        replenish_item_details["miss_quantity"] = oos_item["item_quantity"]
        replenish_item_details["quality_quantity"] = 1
        replenish_item_details["lost_quantity"] = oos_item["item_quantity"] - 1
        replenish_list.append(replenish_item_details)

        body = {
            "warehouse_number": self.warehouse,
            "tote_from": tote_no,
            "tote_to": tote_no,
            "edit_user": self.user,
            "order_id": order_id,
            "replenishItemDetails": replenish_list
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"

    def oneitem_packing_replenish(self, tote_no, cart_no, order_id, oos_item):
        url = "/wms/packing/packingReplenish"
        replenish_list = list()
        replenish_item_details = dict()

        if len(oos_item) > 0:
            first_item = oos_item[0]  # 获取列表中的第一个元素
            replenish_item_details["item_number"] = first_item["item_number"]
            replenish_item_details["miss_quantity"] = first_item["item_quantity"]
            replenish_item_details["quality_quantity"] = 1
            replenish_item_details["lost_quantity"] = first_item["item_quantity"] - 1
            replenish_list.append(replenish_item_details)

            body = {
                "warehouse_number": self.warehouse,
                "tote_from": cart_no,
                "tote_to": tote_no,
                "edit_user": self.user,
                "order_id": order_id,
                "replenishItemDetails": replenish_list
            }
            self.post(url=url, headers=header, json=body)
            assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"

    def query_replenish_quantity(self, cart_no,order_id):
        url = "/wms/packing/queryReplenishItemQuantity"
        body = {
            "tote_from": cart_no,
            "edit_user": self.user,
            "order_id": order_id,
            "warehouse_number": self.warehouse
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"

    def batch_qc(self, tote_no, order_id):
        url = "/wms/packing/batch_qc"
        body = {
            "order_id": order_id,
            "edit_user": self.user,
            "isAutoQC": True,
            "tote_no": tote_no,
            "warehouse_number": self.warehouse
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"

    def packing_force_stock(self, tote_no, order_id):
        url = f"/wms/packing/forcestock/{order_id}"
        body = {
            "warehouse_number": self.warehouse,
            "order_id": order_id,
            "edit_user": self.user,
            "tote_from": tote_no,
            "tote_to": ""
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"

    def one_item_complete(self, cart_no):
        url = "/wms/packing/oneitem/complete"
        body = {
            "warehouse_number": self.warehouse,
            "tote_no": cart_no,
            "edit_user": self.user,
            "packing_station_number": "1-1"
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"

    def get_superPassword(self):
        url = f"/wms/warehouseUser/superPassword/list/{self.warehouse}"
        self.post(url=url, headers=header)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"][0]

    def move_to_cot(self, tote_no, order_id):
        url = "/wms/packing/cancel/moveToCOT"
        body = {
            "warehouse_number": self.warehouse,
            "tote_from": tote_no,
            "edit_user": self.user,
            "order_id": order_id,
            "work_station": "1-1"
            }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"


    def create_cot_label(self, order_id):
        url = f"/wms/cancel/order/label/create/{order_id}"
        self.get(url=url, headers=header)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
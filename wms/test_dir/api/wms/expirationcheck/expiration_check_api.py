import json
import weeeTest
from wms.test_dir.api.wms.wms import header


class Expired(weeeTest.TestCase):
    # 保质期检查接口
    def expired_get_task(self, warehouse_number, storage_type, edit_user):
        url = "/wms/expirationcheck/getList4Count"
        body = {
            "warehouse_number": warehouse_number,
            "storage_type": storage_type,
            "status": "0",
            "edit_user": edit_user,
            "flag": 1,
            "restock_type": 1,
            "module_name": "expiration_check"
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def expired_release_task(self, edit_user):
        url = "/wms/expirationcheck/releaseCountTask"
        body = {"editUser": edit_user}
        self.put(url=url, headers=header, json=body)
        assert self.response['messageId'] == "10000", f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def expired_query_task(self, warehouse_number, location_no, item_number):
        url = "/wms/expirationcheck/queryCountInfoList"
        body = {
            "warehouse_number": warehouse_number,
            "start_time": "",
            "end_time": "",
            "location_type": "",
            "type": "",
            "status": -1,
            "location_no": location_no,
            "item_number": item_number,
            "user_id": "",
            "aisles": [

            ],
            "startColumn": 0,
            "pageSize": 15,
            "order": None
        }

        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def expired_assign_task(self, rec_id, user_id):
        url = "/wms/expirationcheck/assignCountTask"
        body = {
            "rec_ids": [rec_id],
            "edit_user": user_id
        }

        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def expired_scan_location(self, warehouse_number, location_no, status, check_user):
        url = "/wms/expirationcheck/scanLocation"
        body = {
            "warehouse_number": warehouse_number,
            "location_no": location_no,
            "status": status,
            "module_name": "expiration_check",
            "check_user": check_user
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def expired_goods(self, warehouse_number, upc, location_no):
        url = "/wms/expirationcheck/goods"
        body = {
            "warehouse_number": warehouse_number,
            "item_number": upc,
            "location_no": location_no
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def expired_confirm(self, warehouse_number, location_no, item_number, edit_user, status, expired_dtm_str, expired_dtm_input):
        url = "/wms/expirationcheck/confirm"
        body = {
            "warehouse_number": warehouse_number,
            "location_no": location_no,
            "item_number": item_number,
            "in_user": edit_user,
            "status": status,
            "expired_dtm_str": expired_dtm_str,
            "date_type": "0",
            "expired_dtm_input": expired_dtm_input
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def expired_lpn_confirm(self, warehouse_number, location_no, item_number, edit_user, status, expired_dtm_str, expired_dtm_input, lpn_lists):
        # lpn_list参数拼接
        lpn_list = []
        for data in lpn_lists:
            new_lpn_list = {
                "warehouse_number": data["warehouse_number"],
                "item_number": data["item_number"],
                "location_no": data["location_no"],
                "lpn_no": data["lpn_no"],
                "expired_dtm_str": expired_dtm_str,
                "inputQty": data["lpn_quantity"],
                "pieces_per_pack": data["pieces_per_pack"],
                "lot_code": data["lot_code"],
                "date_type": 0,
                "expired_dtm_input": expired_dtm_input
            }
            lpn_list.append(new_lpn_list)
        # 调用confirm接口
        url = "/wms/expirationcheck/lpnConfirm"
        body = {
            "module_name": "expiration_check",
            "warehouse_number": warehouse_number,
            "location_no": location_no,
            "item_number": item_number,
            "in_user": edit_user,
            "count1Auth": True,
            "count2Auth": True,
            "count3Auth": True,
            "status": status,
            "lpnList": lpn_list
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

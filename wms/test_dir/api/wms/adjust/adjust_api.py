from time import sleep
import json
import weeeTest
from wms.test_dir.api.wms.wms import header
from wms.test_dir.api_case.wms.utils import DataUtils


class Adjust(weeeTest.TestCase):
    # 库存查询
    def query_location_inv(self, item_number, location_no, warehouse_number, lpn_no=None):
        """单个商品库存查询"""
        url = "/wms/adjust/queryLocationInvDetail"
        body = {
            "warehouse_number": warehouse_number,
            "item_number": item_number,
            "location_no": location_no,
            "lpn_no": lpn_no
        }

        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def query_location_inv_list(self, warehouse_number, item_number="", location_no="", location_types=[]):
        """多个商品库存查询"""
        url = "/wms/adjust/queryLocationInvList"
        body = {
            "warehouse_number": warehouse_number,
            "item_number": item_number,
            "location_no": location_no,
            "location_types": location_types
        }

        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]["data"]

    # 库存调整
    def create_batch_inv(self, item_number, warehouse_number, in_user, location_no, adjust_number, is_lpn=False, pieces_per_pack=1, expire_dtm=0, batch_no=None,
                         lpn_list=[], receive_date=None, input_dtm_type=0, lpn_qty=None):
        url = "/wms/adjust/confirmLocationInv"
        body = {
            "item_number": item_number,
            "warehouse_number": warehouse_number,
            "in_user": in_user,
            "reason": "other",
            "comment": "自动化测试test------",
            "adjustList": [
                {
                    "location_no": location_no,
                    "adjust_number": adjust_number,
                    "is_lpn": is_lpn,
                    "pieces_per_pack": pieces_per_pack,
                    "expire_dtm": expire_dtm,
                    "adjustLpnList": lpn_list,
                    "receive_date": DataUtils.get_special_date() if not receive_date else receive_date,
                    "input_dtm_str": DataUtils.get_special_date() if not receive_date else receive_date,
                    "input_dtm_type": input_dtm_type,
                    "lpn_qty": lpn_qty
                }
            ]
        }
        if batch_no:
            for adjust in body["adjustList"]:
                adjust["batch_no"] = batch_no
        sleep(1)
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True or self.response['messageId'] == "99004", f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    # 修改保质期
    def modify_shelf_life(self, warehouse_number, item_number, location_nos, lpn_nos, is_all_lpn, batch_inv_infos, new_expire_date, input_dtm_str):
        url = "/wms/adjust/update/item/expireDate"
        body = {
            "warehouse_number": warehouse_number,
            "item_number": item_number,
            "location_nos": location_nos,
            "lpn_nos": lpn_nos,
            "is_all_lpn": is_all_lpn,
            "batch_inv_infos": batch_inv_infos,
            "new_expire_date": new_expire_date,
            "input_dtm_type": 0,
            "input_dtm_str": input_dtm_str
        }
        sleep(1)
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    # Lock/Unlock库存
    def lock_inv(self, is_lock, item_number, location_no, warehouse_number, is_lpn, lpn_nos, batch_no):
        url = "/wms/location/lockLocation"
        body = {
            "is_lock": is_lock,
            "item_number": item_number,
            "location_no": location_no,
            "warehouse_number": warehouse_number,
            "is_lpn": is_lpn,
            "lpn_nos": lpn_nos,
            "batch_no": batch_no
        }
        sleep(1)
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    # approve库存
    def approve_task(self, item_number, approve_type, in_user, warehouse_number, rec_id, action_type):
        url = "/wms/approval/batchApproveTask"
        body = [{
            "item_number": item_number,
            "type": approve_type,  # 1:通过 0:拒绝
            "in_user": in_user,
            "warehouse_number": warehouse_number,
            "dailyReportItem": [
                {
                    "rec_id": rec_id,  # rec_id审核任务ID
                    "info_id": None,
                    "detail_id": None,
                    "action_type": action_type,  # 业务类型 1:盘点 2:central调整 3:通用工具 4:work order
                    "issue_type": None,
                    "adjustment_analysis": "自动化测试审核数据......"
                }
            ]
        }]

        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    # 库存调整
    def transfer_inv(self, item_number, warehouse_number, in_user, from_location, to_location):
        url = "/wms/general/backEndBatchConfirmMove"
        body = {
            "warehouseNumber": warehouse_number,
            "in_user": in_user,
            "comment": "自动化测试test------",
            "moveLocationInfoList": [
                {
                    "locationFrom": from_location,
                    "item_numbers": [item_number],
                    "locationTo": to_location
                }
            ]
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response


if __name__ == '__main__':
    w = Adjust()
    print(w.query_location_inv_list("25", "TM1112"))
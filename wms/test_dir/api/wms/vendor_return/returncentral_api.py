# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  hao.fan
@Version        :  V1.0.0
------------------------------------
@File           :  returncentral_api.py
@Description    :  
@CreateTime     :  2024/8/8 17:01
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2024/8/8 17:01
"""
import json
import weeeTest
from wms.test_dir.api.wms.wms import header

class ReturnCentralAPI(weeeTest.TestCase):

    def createOrUpdate(self,orderNO,pickUpDate,deliveryType,itemNumber1):
        #创建、更新订单
        url = "/wms/vendorReturnOrder/createOrUpdate"
        body = {
        "orderNo": orderNO,
        "type": 2,
        "warehouseNumber": "25",
        "sellerId": 9012,
        "sellerName": "Globa+ Vendor0328 New",
        "vendorId": "",
        "vendorName": "",
        "pickUpDate": pickUpDate,
        "deliveryType": deliveryType,
        "createUserId": "10953840",
        "createUserName": "hao.fan",
        "itemList": [
            {
                "detailUniqueId": 1,
                "itemNumber": itemNumber1,
                "quantity": 2,
                "itemLevel": 1,
                "requireNumber": "",
                "partial": True,
                "outboundPolicy": 2,
                "storageType": 2
            }
        ]
    }
        self.post(url=url, headers=header, json=body)
        '''response body
        {
            "messageId": "10000",
            "success": true,
            "body": {
                "recId": 1,
                "orderNo": "RN2408050011",
                "warehouseNumber": "25"
            }
        }
        '''
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]

    def cancelorder(self,orderNO):
        #取消订单
        url = "/wms/vendorReturnOrder/cancel"
        body ={
            "warehouseNumber": "25",
            "orderNo": orderNO,
            "type": 2,
            "userId": "10953840",
            "userName": "hao.fan"
        }
        self.post(url=url, headers=header, json=body)
        '''response body
           {
            "messageId": "10000",
            "success": true,
            "body": {
                "recId": 1,
                "orderNo": "RN2408050002",
                "warehouseNumber": "25"
            }
        }
        '''
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]

    def startpreoccupy(self,orderNO):
        #自动预占
        url = "/wms/vendorReturn/preoccupy/start"
        body = {
            "returnNo": orderNO,
            "type": 2,
            "warehouseNumber": "25"
        }
        self.post(url=url, headers=header, json=body)
        '''response body
           {
            "messageId": "10000",
            "success": true,
            "body": {
                "recId": 1,
                "orderNo": "RN2408050002",
                "warehouseNumber": "25"
            }
        }
        '''
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]

    #预占结果获取
    def getpreoccupyresult(self, orderNO):
        """
        获取预占结果。

        本函数通过发送HTTP POST请求，获取给定退货单号的预占结果。
        参数:
        - orderNO (str): 退货单号，用于查询预占结果。

        返回:
        - dict: 包含预占结果的响应体。
        """
        # 定义请求URL
        url = "/wms/vendorReturn/preoccupy/result"
        # 构建请求体，包含退货单号、类型和仓库编号
        body = {
            "returnNo": orderNO,
            "type": 2,
            "warehouseNumber": "25"
        }
        # 发送POST请求
        self.post(url=url, headers=header, json=body)
        '''
        预期的响应示例：
        {
            "messageId": "",
            "success": false,
            "body": {
                "completed": false,
                "hasTask": false
            }
        }
        '''
        # 断言响应中的'success'字段为True，确保请求成功
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        # 返回响应体
        return self.response["body"]
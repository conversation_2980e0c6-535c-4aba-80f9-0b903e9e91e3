# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  <PERSON><PERSON> chen
@Version        :  V1.0.0
------------------------------------
@File           :  api.py
@Description    :
@CreateTime     :  2023/5/15 11:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/5/15 11:06
"""
import json
import weeeTest
from wms.test_dir.api.wms.wms import header


class OneItemPicking(weeeTest.TestCase):

    # 创建拣货任务
    def create_picking_task(self, cart_no: str, warehouse_number: str):
        url = "/wms/orderPick/createPickingTask"
        body = {
            "cart_no": cart_no,
            "warehouse_number": warehouse_number,
            "storage_type": "1",
            "picking_channel": 0
        }
        self.post(url=url, headers=header, json=body)
        result = weeeTest.jmespath(self.response, "success")
        if result:
            weeeTest.log.info(f'创建拣货任务成功。')
        else:
            weeeTest.log.error(f'创建拣货任务失败, Error response:{self.response}')
        return self.response

    # 库存调整
    def adjust_location(self, warehouse_number, tote_no, location_no, item_number, order_id, picking_quantity,
                        picking_type, picking_task_id):
        url = "/wms/orderPick/adjustLocation"
        body = {
            "warehouse_number": warehouse_number,
            "tote_no": tote_no,
            "location_no": location_no,
            "item_number": item_number,
            "order_id": order_id,
            "picking_quantity": picking_quantity,
            "picking_type": picking_type,
            "picking_task_id": picking_task_id,
            "long_press": 1,
            "scanLocationDtm": 1661243552,
            "scanUpcDtm": 1661243556,
            "scanToteDtm": 1661243576

        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def picking_task_finish(self, picking_task_id, picking_type, packing_line, warehouse_number):
        # {"picking_task_id":1290497,"picking_type":1,"packing_line":1,"warehouse_number":"35"}
        url = "/wms/orderPick/pickingTaskFinish"
        body = {
            "picking_task_id": picking_task_id,
            "picking_type": picking_type,
            "packing_line": packing_line,
            "warehouse_number": warehouse_number
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""

"""
import json
import weeeTest
from wms.test_dir.api.wms.wms import header


class FbwPicking(weeeTest.TestCase):

    # 创建拣货任务
    def create_picking_task(self, cart_no: str, warehouse_number: str, storage_type):
        url = "/wms/orderPick/createPickingTask"
        body = {
            "cart_no": cart_no,
            "warehouse_number": warehouse_number,
            "storage_type": storage_type,
            "picking_channel": 0
        }
        self.post(url=url, headers=header, json=body)
        return self.response

    # 绑定拣货框
    def bind_tote_number(self, picking_task_id, tote_no, order_id, shipping_type, warehouse_number):
        url = "/wms/orderPick/bindToteNumber"
        body = {
            "picking_task_id": picking_task_id,
            "tote_no": tote_no,
            "order_id": order_id,
            "shipping_type": shipping_type,
            "warehouse_number": warehouse_number
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    # 库存调整
    def adjust_location(self, warehouse_number, tote_no, location_no, item_number, order_id, picking_quantity,
                        picking_type, picking_task_id):
        url = "/wms/orderPick/adjustLocation"
        body = {
            "warehouse_number": warehouse_number,
            "tote_no": tote_no,
            "location_no": location_no,
            "item_number": item_number,
            "order_id": order_id,
            "picking_quantity": picking_quantity,
            "picking_type": picking_type,
            "picking_task_id": picking_task_id,
            "long_press": 1,
            "scanLocationDtm": 1661243552,
            "scanUpcDtm": 1661243556,
            "scanToteDtm": 1661243576

        }

        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def picking_task_finish(self, picking_task_id, picking_type, packing_line, warehouse_number):
        url = "/wms/orderPick/pickingTaskFinish"
        body = {
            "picking_task_id": picking_task_id,
            "picking_type": picking_type,
            "packing_line": packing_line,
            "warehouse_number": warehouse_number
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

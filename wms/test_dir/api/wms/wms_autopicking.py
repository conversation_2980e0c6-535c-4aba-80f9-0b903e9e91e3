# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  Author
@Version        :  V1.0.0
------------------------------------
@File           :  wms_autopicking.py
@Description    :  
@CreateTime     :  2025/5/27 15:36
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/5/27 15:36
"""


import logging
import time,json
import weeeTest
from wms.test_dir.api.wms.wms import wms
from wms.qa_config import global_data
from datetime import datetime, timezone


class WMS_AUTO(weeeTest.TestCase):

    def as_picking(self,order_id):

        warehouse_number = global_data.as_picking['warehouse_number']
        user_id, user_name = wms.wms_login.common_login()

        order_info = wms.wms_db.get_order_info(order_id=order_id, info=['shipping_status', 'delivery_dtm'])
        if order_info['shipping_status'] == 25:
            # 获取Pick List
            pick_list_id_info = wms.wms_db.get_order_as_task(order_id=order_id, info=["rec_id", "pick_list_rec_id"])
            pick_list_id = pick_list_id_info[0]["pick_list_rec_id"]
            pick_list_type_info = wms.wms_db.get_order_as_list(pick_list_id=pick_list_id,info=["rec_id","status","list_type"])
            pick_list_type = pick_list_type_info[0]["list_type"]

            # 取消前一天订单
            delivery_dtm = order_info['delivery_dtm']
            before_delivery_dtm = delivery_dtm - 86400
            wms.wms_db.update_order(order_status=2, delivery_dtm=before_delivery_dtm, warehouse_number=warehouse_number)

            # 更新AS Config Start_dtm = delivery_dtm，发送pick list
            start_dtm = wms.util.utc_to_day(delivery_dtm)
            config_value = self.get_as_config_value(start_dtm)
            wms.update_central_config(warehouse_number=warehouse_number, config_key="support_automation",config_value=config_value)

            # JOB发送Pick List
            self.send_as_pick_list(pick_list_id=pick_list_id)

            #geek拣货 回调1 Pick Result;回调2 bind container;回调3 Pick Complete
            wms_auto.as_process_pick_list(pick_list_id=pick_list_id,warehouse_number=warehouse_number)

            #wms拣货
            wms_auto.as_process_wms_pick(order_id=order_id,pick_list_id=pick_list_id,pick_list_type=pick_list_type,warehouse_number=warehouse_number)

        else:

            logging.info(f"订单非AS订单,订单状态为：{order_info['shipping_status']}")


    def geek_picking(self,order_id):

        warehouse_number = global_data.geekPlus_picking['warehouse_number']
        user_id, user_name = wms.wms_login.common_login()

        order_info = wms.wms_db.get_order_info(order_id=order_id, info=['shipping_status', 'delivery_dtm'])
        if order_info['shipping_status'] == 25:

            #获取Pick List
            pick_list_id_info = wms.wms_db.get_order_as_task(order_id=order_id, info=["rec_id", "pick_list_rec_id"])
            pick_list_id = pick_list_id_info[0]["pick_list_rec_id"]

            #获取wh_automation_pick_task_detail信息
            task_details = wms.wms_db.get_order_as_task_detail(pick_list_id=pick_list_id,info=['rec_id', 'item_number', 'batch_no', 'receive_dtm', 'expire_dtm', 'quantity'])

            #取消前一天订单
            delivery_dtm = order_info['delivery_dtm']
            before_delivery_dtm = delivery_dtm - 86400
            wms.wms_db.update_order(order_status=2, delivery_dtm=before_delivery_dtm, warehouse_number=warehouse_number)

            #更新AS Config Start_dtm = delivery_dtm，发送pick list
            start_dtm = wms.util.utc_to_day(delivery_dtm)
            config_value = self.get_geek_config_value(start_dtm)
            wms.update_central_config(warehouse_number=warehouse_number, config_key="support_automation",config_value=config_value)

            #JOB发送Pick List
            self.send_geek_pick_list(pick_list_id=pick_list_id)

            tote_no = wms.wms_db.get_wh_storage_location_info(warehouse=warehouse_number, location_type=6, flag=0, info="location_No")
            out_order_code = f"TB1_{pick_list_id}"

            self.geek_bind_container(warehouse_number, user_id, out_order_code, tote_no)
            feedback_data = self.geek_prepare_feedback_data(user_id, warehouse_number, out_order_code, tote_no,
                                                       task_details)
            #confirm
            wms.autostore_picking.geekPlusApi(data=feedback_data)

            #reset_data
            self.restore_geek_data(order_id, pick_list_id)
        else:
            logging.info(f"订单非GeekPlus订单,订单状态为：{order_info['shipping_status']}")




    def get_geek_config_value(self, start_dtm):
        data = {
            "active": True,
            "agingOrderMaxTime": 600,
            "oneItemOneOrderVolumeLimit": 1000,
            "oneItemVolumeMaxLimit": 2500,
            "oneItemVolumeMinLimit": 1,
            "pickCartConfigList": [
                {"count": 4, "size": 10, "weight": 10},
                {"count": 2, "size": 15, "weight": 15},
                {"size": 25, "count": 0, "weight": 25},
                {"size": 30, "count": 0, "weight": 30}
            ],
            "startDeliveryDate": start_dtm,
            "supplier": "GeekPlus",
            "sendDetailIdDeliveryDate": "2000-11-29",
            "supportOneItem": False,
            "oosLockInventory": True,
            "sendSkipWaveIds": [],
            "cancelOrderSetKpiComplete": False,
            "restockCallActive": True,
            "mailOrderSupportOneItem": True,
            "mailOrderOneItemOneOrderVolumeLimit": 1000,
            "asItemCanMarkCondition": {
                "max_sku_weight_lb": 10,
                "max_skuBox_weight_lb": 54,
                "max_skuBox_volume_in3": 1000
            }
        }
        return json.dumps(data)

    def get_as_config_value(self, start_dtm):
        data = {
            "active": True,
            "startDeliveryDate": start_dtm,
            "supplier": "AutoStore",
            "sendDetailIdDeliveryDate": "2000-11-29",
            "supportOneItem": True,
            "oneItemOneOrderVolumeLimit": 500,
            "oneItemVolumeMinLimit": 300,
            "oneItemVolumeMaxLimit": 1100,
            "agingOrderMaxTime": 3000,
            "oosLockInventory": True,
            "containerOccupy": True,
            "mailOrderSupportOneItem": True,
            "mailOrderOneItemOneOrderVolumeLimit": 460,
            "sendSkipWaveIds": [],
            "cancelOrderSetKpiComplete": True,
            "checkOrderTypeFinishedBeforeDay": [
                0
            ],
            "restockCallActive": True,
            "pickCartConfigList": [
                {"size": 10,"count": 4,"weight": 10},
                {"size": 15,"count": 2,"weight": 15},
                {"size": 25,"count": 0,"weight": 25},
                {"size": 30,"count": 0,"weight": 30}
            ],
            "asItemCanMarkCondition": {
                "max_sku_weight_lb": 8,
                "max_skuBox_weight_lb": 40,
                "max_skuBox_volume_in3": 4534.74,
                "max_sku_length_in": 23.6,
                "max_sku_width_in": 15.75,
                "max_sku_height_in": 12.3
            }
        }
        return json.dumps(data)

    def send_geek_pick_list(self,pick_list_id):
        wms.autostore_picking.geekPlusJob()
        time.sleep(5)
        # 获取拣选列表的状态
        pick_list_info = wms.wms_db.get_order_as_list(pick_list_id=pick_list_id, info=["rec_id", "status"])
        pick_list_status = pick_list_info[0]["status"]
        #10 已发送
        assert pick_list_status == 10, "Pick List发送KPI失败"

    def send_as_pick_list(self, pick_list_id):
        wms.autostore_picking.autoStoreJob()
        time.sleep(5)
        # 获取拣选列表的状态
        pick_list_info =  wms.wms_db.get_order_as_list(pick_list_id=pick_list_id, info=["rec_id", "status"])
        pick_list_status = pick_list_info[0]["status"]
        # 10 已发送
        assert pick_list_status == 10, "Pick List发送KPI失败"


    def geek_bind_container(self, warehouse_number, user_id, out_order_code, pallet_no):
        bind_container_data = {
            "body": {
                "warehouse_code": warehouse_number,
                "binding_list": [{
                    "id": 118,
                    "out_order_code": out_order_code,
                    "owner_code": "SayWeee",
                    "operate_time": 1736480158410,
                    "operator": user_id,
                    "container_code": pallet_no,
                    "wall_code": "YD3",
                    "workstation_no": "106",
                    "seeding_bin_code": "A1",
                    "status": 1
                }]
            },
            "header": {
                "interface_code": "feedback_bind_container",
                "user_id": "Weee",
                "user_key": "96e79218965eb72c92a549dd5a330112",
                "warehouse_code": warehouse_number
            }
        }
        wms.autostore_picking.geekPlusApi(data=bind_container_data)

    def geek_prepare_feedback_data(self, user_id, warehouse_number, out_order_code, pallet_no, task_details):
        sku_list = self.geek_create_sku_list(out_order_code, task_details, user_id, pallet_no)
        container_sku_list = self.geek_container_sku_list(task_details, pallet_no)

        return {
            "body": {
                "size": 1,
                "order_amount": 1,
                "order_list": [{
                    "picker": user_id,
                    "id": 118199,
                    "out_order_code": out_order_code,
                    "status": 3,
                    "warehouse_code": warehouse_number,
                    "owner_code": "SayWeee",
                    "plan_sku_amount": 4,
                    "pickup_sku_amount": 4,
                    "pick_type": 1,
                    "sync_date": 1736495880231,
                    "finish_date": 1736495802879,
                    "container_amount": 1,
                    "sku_list": sku_list,
                    "container_list": [{
                        "picker": user_id,
                        "id": 118200,
                        "wall_code": "YD3",
                        "out_order_code": out_order_code,
                        "container_code": pallet_no,
                        "sku_amount": 4,
                        "sku_type_amount": 2,
                        "creation_date": 1736495847038,
                        "start_time": 1736490510111,
                        "complete_time": 1736495802990,
                        "sku_list": container_sku_list,
                        "container_amount": 1,
                        "container_type": 2,
                        "wave_code": "W20250110-00000002",
                        "pick_type": 0,
                        "wave_type": 20,
                        "workstation_no": "106",
                        "seeding_bin_code": "A1",
                        "pick_seeding_bin_no": "A1",
                        "double_pick_type": 1
                    }],
                    "sku_amount": 4,
                    "data_source_platform": 200,
                    "carrier_code": "SayWeee",
                    "carrier_name": "SayWeee",
                    "start_time": 1736490510116
                }]
            },
            "header": {
                "interface_code": "feedback_outbound_order",
                "user_id": "Weee",
                "user_key": "96e79218965eb72c92a549dd5a330112",
                "warehouse_code": warehouse_number
            }
        }

    def geek_create_sku_list(self, out_order_code, task_details, user_id, pallet_no):
        sku_list = []
        for detail in task_details:
            sku_item = {
                "externalCode": out_order_code,
                "column": "id",
                "value": 215732,
                "sku_id": detail['item_number'],
                "packing_spec": "",
                "out_order_code": out_order_code,
                "item": 1,
                "sku_code": detail['item_number'],
                "plan_amount": detail['quantity'],
                "pickup_amount": detail['quantity'],
                "is_sequence_sku": 0,
                "sequence_no": "",
                "out_batch_code": detail['batch_no'],
                "production_date": 1751173200000,
                "expiration_date": detail['expire_dtm'] * 1000,
                "input_date": 1735189200000,
                "batch_property01": "1751173200000",
                "batch_property02": "1766725200000",
                "batch_property03": "241226000006",
                "batch_property06": "100",
                "batch_property10": "PA20241226000005",
                "batch_property11": "SayWeee",
                "owner_code": "SayWeee",
                "shelf_bin_list": [{
                    "quantity": detail['quantity'],
                    "shelf_code": "P00010",
                    "shelf_bin_code": "P00010B05A",
                    "operator": user_id,
                    "container_code": pallet_no,
                    "logic_area_code": "logic-P"
                }]
            }
            sku_list.append(sku_item)
        return sku_list

    def geek_container_sku_list(self, task_details, pallet_no):
        container_sku_list = []
        for detail in task_details:
            container_sku_item = {
                "sku_id": detail['item_number'],
                "sku_code": detail['item_number'],
                "amount": detail['quantity'],
                "out_batch_code": detail['batch_no'],
                "expiration_date": detail['expire_dtm'] * 1000,
                "owner_code": "SayWeee",
                "container_code": pallet_no
            }
            container_sku_list.append(container_sku_item)
        return container_sku_list

    def restore_geek_data(self, order_id, pick_list_id):
        if order_id == 242523270:
            wms.wms_db.update_order_ship_status(order_id=order_id, ship_status=25)
            wms.wms_db.update_automation_pick_list(status=1, pick_list_id=pick_list_id)
            wms.wms_db.delete_wh_automation_pick_result(pick_list_id=pick_list_id)
            wms.wms_db.update_pick_list_id(pick_list_id=pick_list_id)

    def restore_as_data(self,order_id,pick_list_id):
        if order_id == 242548069:
            wms.wms_db.update_order_ship_status(order_id=order_id, ship_status=25)
            wms.wms_db.update_automation_pick_list(status=1, pick_list_id=pick_list_id)
            wms.wms_db.delete_wh_automation_pick_result(pick_list_id=pick_list_id)
            wms.wms_db.update_pick_list_id(pick_list_id=pick_list_id)
            wms.wms_db.delete_wh_picking_order(order_id=order_id)

    def as_pick_result_payload(self,pick_list_id, task_details, container_complete=False, outbound_container_id=None):
        current_time = datetime.now(timezone.utc).astimezone().isoformat()

        payload = {
            "action": "PickResult",
            "instanceId": "SayWeee",
            "messageId": str(int(datetime.now().timestamp() * 1000)),
            "params": {
                "pickListId": pick_list_id,
                "containerComplete": container_complete,
                "createdBy": "HOST",
                "hostCreated": True,
                "listComplete": container_complete,
                "pickTaskInformation": []
            },
            "timestamp": current_time,
            "version": "2.0.0"
        }

        if container_complete and outbound_container_id:
            payload["params"]["outboundContainerId"] = outbound_container_id

        for task in task_details:
            pick_task_info = {
                "pickTaskId": str(task['rec_id']),
                "pickTaskComplete": True,
                "itemId": str(task['item_number']),
                "clientId": "TB1",
                "pickTaskCompleteCode": "PICKED",
                "compartmentInformation": [
                    {
                        "quantity": task['quantity'],
                        "portId": 14,
                        "portType": "CAROUSEL",
                        "startDatetime": current_time,
                        "endDatetime": current_time,
                        "userId": "7875714",
                        "sortBarPositionId": "A1"
                    }
                ]
            }

            if not container_complete:
                pick_task_info["compartmentInformation"][0].update({
                    "binId": "AS17390",
                    "compartmentId": "A1",
                    "fifoDate": datetime.fromtimestamp(task['receive_dtm']).strftime('%Y-%m-%d'),
                    "expiryDate": datetime.fromtimestamp(task['expire_dtm']).strftime('%Y-%m-%d'),
                    "group1": task['batch_no'],
                })
            else:
                pick_task_info["compartmentInformation"][0].update({
                    "binId": "",
                    "compartmentId": "",
                })

            payload["params"]["pickTaskInformation"].append(pick_task_info)

        return payload

    def as_list_activity_payload(self,pick_list_id, user_id="7875714", port_type="CAROUSEL", port_id="14"):
        current_time = datetime.now(timezone.utc).astimezone().isoformat()

        payload = {
            "action": "ListActivity",
            "instanceId": "SayWeee",
            "messageId": str(int(datetime.now().timestamp() * 1000)),
            "params": {
                "listId": pick_list_id,
                "listType": "PICK",
                "status": "COMPLETE",
                "userId": user_id,
                "operationDatetime": current_time,
                "portType": port_type,
                "portId": port_id
            },
            "timestamp": current_time,
            "version": "2.0.0"
        }

        return payload

    def as_process_pick_list(self,pick_list_id,warehouse_number):
        # 获取任务详情
        task_details = wms.wms_db.get_order_as_task_detail(pick_list_id,
            info=['rec_id', 'item_number', 'batch_no', 'receive_dtm', 'expire_dtm', 'quantity'])
        tb1_pick_list_id = f"TB1_{pick_list_id}"
        responses = []
        # 对每个任务进行两次 PickResult 调用
        for task in task_details:

            # 发送第一个 PickResult 请求（非容器完成）
            payload1 = wms_auto.as_pick_result_payload(pick_list_id = tb1_pick_list_id ,task_details = [task])
            response1 = wms.autostore_picking.autoStoreApi(data=payload1)
            responses.append(("PickResult1", response1))
            print(f"First PickResult response for task {task['rec_id']}:", response1)

            # 获取 outbound_container_id
            outbound_container_id = wms.wms_db.query_chute(warehouse_number=warehouse_number)['chute_id']

            # 发送第二个 PickResult 请求（容器完成）
            payload2 =wms_auto.as_pick_result_payload(pick_list_id = tb1_pick_list_id ,task_details = [task], container_complete=True,
                                                  outbound_container_id=outbound_container_id)
            response2 = wms.autostore_picking.autoStoreApi(data=payload2)
            responses.append(("PickResult2", response2))
            print(f"Second PickResult response for task {task['rec_id']}:", response2)

        # 所有任务处理完毕后，发送 ListActivity 请求
        list_activity_payload = wms_auto.as_list_activity_payload(pick_list_id = tb1_pick_list_id)
        list_activity_response = wms.autostore_picking.autoStoreApi(data=list_activity_payload)
        responses.append(("ListActivity", list_activity_response))
        print("ListActivity response:", list_activity_response)

        return responses


    def as_process_wms_pick(self,order_id,pick_list_id,pick_list_type,warehouse_number):
        #wms拣货AS Normal Picking

        if pick_list_type == 1:

            # 查询可用的M cart
            location_no = wms.wms_db.get_wh_storage_location_info(warehouse=warehouse_number, location_type=50,
                                                                  flag=0, info='location_no')
            # 创建拣货任务
            resp5 = wms.autostore_picking.create_picking_task(cart_no=location_no,
                                                              warehouse_number=warehouse_number, storage_type=1)
            box_size = resp5['body']['box_size']
            picking_orders = resp5["body"]["picking_order"]

            chute_id = None
            order_tote_no = None
            new_order_id = None
            picking_task_id = None

            for order in picking_orders:
                rec_id = order["rec_id"]
                order_id = order["order_id"]
                picking_task_id = order["picking_task_id"]
                print(f"rec_id: {rec_id}, order_id: {order_id}, picking_task_id: {picking_task_id}")

                # 查询可用的tote
                tote_nos = wms.wms_db.select_available_tote(location_type=6, flag=0, warehouse=warehouse_number,
                                                            box_size=box_size)
                tote_no = tote_nos[0]['location_no']

                response = wms.autostore_picking.bind_tote_number(
                    picking_task_id=picking_task_id,
                    tote_no=tote_no,
                    order_id=order_id,
                    shipping_type=None,
                    picking_order_rec_id=rec_id,
                    warehouse_number=warehouse_number
                )
                time.sleep(3)
                print(response['body'].get('nextOrder'))


                # 依次绑定tote，直至分配可拣订单，获取chute_id
                if response['body'].get('nextOrder') is not None:
                    chute_id = response['body']['nextOrder'].get('chute_id')
                    order_tote_no = response['body']['nextOrder'].get('tote_no')
                    new_order_id = response['body']['nextOrder'].get('order_id')
                    print(f"Chute ID: {chute_id}")
                    print(f"Tote No: {order_tote_no}")
                    print(f"order_id: {new_order_id}")
                    break

                # 扫描Chute
            time.sleep(3)
            wms.autostore_picking.updateDestinationStatus(warehouseNumber=warehouse_number,
                                                          moduleName="autostore_picking",
                                                          destinationId=chute_id, destinationStatus="Empty")
            # Adjust
            time.sleep(5)
            timestamp = int(time.time())
            wms.autostore_picking.adjustLocation(warehouse_number=warehouse_number, tote_no=order_tote_no,
                                                 chute_id=chute_id, order_id=new_order_id,
                                                 picking_type=11, picking_task_id=picking_task_id, long_press=0,
                                                 scanLocationDtm=timestamp,
                                                 scanUpcDtm=timestamp, scanToteDtm=timestamp, picking_channel=10)

            # Confirm
            wms.autostore_picking.pickingTaskFinish(picking_task_id=picking_task_id, picking_type=11,
                                                    packing_line=1,
                                                    warehouse_number=warehouse_number, picking_channel=10)
            # reset data
            self.restore_geek_data(order_id, pick_list_id)
        # AS OM Picking
        elif pick_list_type == 2:
            return
        # AS Replenish Picking
        else:
            return


wms_auto = WMS_AUTO()
# wms_auto.as_picking()
import json
import time

import weeeTest
from weeeTest import jmespath
from wms.test_dir.api.wms.wms import header
import random


class Moving(weeeTest.TestCase):
    # 查询商品信息
    def check_item(self, warehouse_number, location_from, upc_code, move_in):
        url = "/wms/general/checkItemUPC"
        body = {
            "warehouse_number": warehouse_number,
            "location_no": location_from,
            "upc_code": upc_code,  # LPN/UPC
            "isMoveIn": move_in  # True/False
        }

        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    # 查询库存
    def query_item_qty(self, item_number, location_to, warehouse_number):
        url = "/wms-inventory/inventoryDetail/inventoryDetails"
        body = {
            "itemNumbers": [
                item_number
            ],
            "locationNos": [
                location_to
            ],
            "warehouseNumber": warehouse_number
        }

        self.post(url=url, headers=header, json=body)
        assert self.response['result'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        qty = jmespath(self.response, "object[0].availableQty")
        expiration_date = jmespath(self.response, "object[0].expireDtm")
        case_spec = jmespath(self.response, "object[0].piecesPerPack")
        lpn_list = jmespath(self.response, "object[0].lpnDetailDTOList")
        available_qty = qty or 0  # 判断qty_old是否为空，为空则赋值为0
        available_qty = int(available_qty)
        return available_qty, lpn_list, expiration_date, case_spec

    # 通用工具转移接口：LPN
    def move_lpn(self, location_from, location_to, qty, item_number, warehouse_number, expire_dtm, receive_dtm, lpn_info_list, in_user):
        url = "/wms/general/confirmMove"
        body = {
            "locationFrom": location_from,
            "locationTo": location_to,
            "available_qty": qty,
            "item_number": item_number,
            "warehouseNumber": warehouse_number,
            "in_user": in_user,
            "moveMethod": 0,
            "expire_dtm": expire_dtm,
            "comment": "other,自动化测试",
            "imageList": [
                "https://img06.test.weeecdn.com/wms/image/950/148/19F80D259B6DB313.jpeg"
            ],
            "isMoveIn": False,
            "receive_dtm": receive_dtm,
            "reason": "other",
            "lpn_info_list": [
                lpn_info_list
            ]
        }

        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    # 通用工具转移接口：非LPN
    def move_upc(self, location_from, location_to, qty, item_number, warehouse_number, expire_dtm, move_in, receive_dtm, in_user):
        url = "/wms/general/confirmMove"
        body = {
            "locationFrom": location_from,
            "locationTo": location_to,
            "available_qty": qty,
            "item_number": item_number,
            "warehouseNumber": warehouse_number,
            "in_user": in_user,
            "moveMethod": 0,
            "expire_dtm": expire_dtm,
            "comment": "other,自动化测试",
            "imageList": [
                "https://img06.test.weeecdn.com/wms/image/950/148/19F80D259B6DB313.jpeg"
            ],
            "isMoveIn": move_in,
            "receive_dtm": receive_dtm,
            "reason": "other"
        }

        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    # 清除库位库存
    def clear_inv(self, warehouse_number, in_user, locationNos, operationVirtualLocation=True):
        url = "/wms-inventory/inventoryBatch/deduct/location/qty"
        body = {
            "traceId": f'test{str(int(round(time.time() * 1000)))}-hv6666',
            "warehouseNumber": warehouse_number,
            "referenceType": 56,
            "referenceNo": 56,
            "operationVirtualLocation": operationVirtualLocation,
            "comment": "备注:自动化test",
            "locationNos": locationNos,
            "user": in_user,
            "businessDtm": 1708588800,
            "targetId": "666666"
        }

        self.post(url=url, headers=header, json=body)
        assert self.response['result'] is True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

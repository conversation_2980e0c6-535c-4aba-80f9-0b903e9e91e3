# !/usr/bin/python3
# -*- coding: utf-8 -*-
import json
import weeeTest
from wms.test_dir.api.wms.wms import header


class RestockAPI(weeeTest.TestCase):

    def add_restock_task(self, warehouse,user,item_number, stock_location, bin_location, recommend_box=1, recommend_qty=1,
                         delivery_dtm_str=None, priority=99999999, is_urgent=0, type=7, is_second_confirm=False,
                         lpn_no=""):
        """
        创建补货任务
        :param item_number: 商品编号
        :param stock_location: 库存位置
        :param bin_location: 目标库位
        :param recommend_box: 推荐箱数
        :param recommend_qty: 推荐数量
        :param delivery_dtm_str: 交付日期字符串，格式：YYYY-MM-DD
        :param priority: 优先级
        :param is_urgent: 是否紧急
        :param type: 任务类型
        :param is_second_confirm: 是否需要二次确认
        :param lpn_no: LPN编号
        :return: 接口响应
        """
        url = "/wms/restock/addRestockTask"
        body = {
            "warehouse_number": warehouse,
            "in_user": user,
            "delivery_dtm_str": delivery_dtm_str,
            "item_number": item_number,
            "stock_location": stock_location,
            "bin_location": bin_location,
            "recommend_box": recommend_box,
            "recommend_qty": recommend_qty,
            "priority": priority,
            "is_urgent": is_urgent,
            "type": type,
            "isSecondConfirm": is_second_confirm,
            "lpnNo": lpn_no
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]

    def generate_restock_task_job(self, warehouse_number, user):
        """
        Job生成补货任务
        :param warehouse_number: 仓库编号
        :param user: 用户名
        :return: 接口响应
        """
        url = "/wms/job/manual/generateRestockTask"
        params = {
            "wh": warehouse_number,
            "user": user
        }
        self.get(url=url, headers=header, params=params)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]

    def pick_summary(self, storage_type, warehouse_number, inuser):
        url = "/wms/restockPick/pickSummary"
        body = {
            "storage_type": storage_type,
            "warehouseNumber": warehouse_number,
            "inUser": inuser
        }
        self.post(url=url, headers=header, json=body)
        '''response body
            {
            "messageId": "10000",
            "success": true,
            "body": {
                "groundPickCount": 15,
                "groundPickTotal": 15,
                "airPickCount": 6,
                "airPickTotal": 6,
                "fullPalletPickCount": 1,
                "fullPalletPickTotal": 1
            }
        }
        '''
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]

    def get_tasks(self, action=1, pick_type="2", warehouse_number=None, user_id=None, in_user=None, storage_type=None):
        """
        获取补货任务列表
        :param action: 0 query List ，1 get List
        :param pick_type: 1 Ground level, 2 Air,3 full pallet
        :param warehouse_number: 仓库编号
        :param user_id: 用户ID
        :param in_user: 登录用户
        :param storage_type: 存储类型
        :return: 接口响应
        """
        url = "/wms/restockPick/pickList"
        body = {
            "action": action,
            "warehouseNumber": warehouse_number,
            "userId": user_id,
            "inUser": in_user,
            "storage_type": storage_type
        }
        if action != 0:
            body["pickTaskType"] = pick_type
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]["list"]

    def scan_pallet(self, warehouse_number, user_id, in_user, pallet):
        """
        type : 0 check pallet, 1 check slot
        :param warehouse_number:
        :param user_id:
        :param in_user:
        :param location_num:
        :return:
        """
        url = "/wms/restockPick/checkLocation"
        body = {
            "warehouseNumber": warehouse_number,
            "userId": user_id,
            "inUser": in_user,
            "locationNum": pallet,
            "type": 0
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"

    def scan_lpn_location(self, task_id, stock_no, storage_type, warehouse_number, in_user):
        url = "/wms/restockPick/pickLpnDetail"
        body = {
            "stock_no": stock_no,
            "storage_type": storage_type,
            "task_id": str(task_id),
            "warehouse_number": warehouse_number,
            "in_user": in_user
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def scan_location(self, task_id, stock_no, warehouse_number, user_id, in_user):
        url = "/wms/restockPick/pickDetail"
        body = {
            "stock_no": stock_no,
            "task_id": str(task_id),
            "warehouse_number": warehouse_number,
            "user_id": user_id,
            "in_user": in_user
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def pick_confirm(self, task_id, stock_no, box_qty, quantity, pallet_no, warehouse_number, user_id, in_user, storage_type,lpn_flag=False, lpn_nos=None):
        """
        inventory_diff: 0 noraml,1 wrong inv, 2 can`t take
        :param task_id:
        :param stock_no:
        :param box_qty:
        :param quantity:
        :param pallet_no:
        :param lpn_flag:
        :param lpn_nos:
        :return:
        """
        url = "/wms/restockPick/pickConfirm"
        body = {
            "stock_no": stock_no,
            "warehouse_number": warehouse_number,
            "user_id": user_id,
            "in_user": in_user,
            "storage_type": storage_type,
            "box": box_qty,
            "quantity": quantity,
            "inventory_diff": 0,
            "new_size": 0,
            "palletNum": pallet_no,
            "task_id": task_id,
            "put_ids": [task_id],
            "memo": ""
        }
        if lpn_flag:
            body["lpnNos"] = lpn_nos
            body["mlpnNos"] = []
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"

    def pick_complete(self, pallet_no, warehouse_number, user_id, in_user, storage_type):
        url = "/wms/restockPick/canReleasePallet"
        body = {
            "warehouseNumber": warehouse_number,
            "userId": user_id,
            "inUser": in_user,
            "locationNum": pallet_no,
            "storage_type": storage_type
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"

    def bin_load_list(self, pallet_no, warehouse_number, user_id, in_user, storage_type, search_code='', upc_code='', bin_no=''):
        """
        search_code: upc/lpn/mlpn
        :param pallet_no:
        :param search_code:
        :param upc_code:
        :param bin_no:
        :return:
        """
        url = "/wms/restockBinLoad/binLoadList"
        body = {
            "put_ids": [],
            "pallet_no": pallet_no,
            "bin_no": bin_no,
            "upc_code": upc_code,
            "inUser": in_user,
            "userId": user_id,
            "warehouseNumber": warehouse_number,
            "storage_type": storage_type
        }
        # scan upc/lpn/mlpn, 开始对该SKU做binload,检查upc/lpn/mlpn是否在pallet上
        if search_code != "":
            body["search_code"] = search_code

        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]

    def bin_load_get_detail(self, task_id, pallet_no, bin_no, action, warehouse_number):
        """
        slotting传0代表不校验slotting logic 库位，不传或者传Null表示需要校验slotting logic属性, 非v4 不校验slotting参数
        :param task_id:
        :param pallet_no:
        :param bin_no:
        :param action:
        :return:
        """
        url = "/wms/restockBinLoad/binLoadDetail"
        body = {
            "bin_no": bin_no,
            "palletNum": pallet_no,
            "task_id": task_id,
            "warehouse_number": warehouse_number,
            "action": action
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]

    def lpn_bin_load_detail(self, task_id, pallet_no, bin_no, action, warehouse_number):
        """
        slotting传0代表不校验slotting logic 库位，不传或者传Null表示需要校验slotting logic属性, 非v4 不校验slotting参数
        :param task_id:
        :param pallet_no:
        :param bin_no:
        :param action:
        :return:
        """
        url = "/wms/restockBinLoad/binLoadDetailWitchLpn"
        body = {
            "bin_no": bin_no,
            "palletNum": pallet_no,
            "task_id": task_id,
            "warehouse_number": warehouse_number,
            "action": action
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]

    def bin_load_confirm(self, task_id, pallet_no, bin_no, actual_total_box, actual_total_qty, warehouse_number, user_id, in_user, action=0 ):
        """
        action: 0 noraml, 1 binfull, 2 product missing, 3 （废弃）， 4  no bin
        :param task_id:
        :param pallet_no:
        :param bin_no:
        :param actual_total_box:
        :param actual_total_qty:
        :param action:
        :return:
        """
        url = "/wms/restockBinLoad/binLoadConfirm"
        body = {
            "action": action,
            "put_ids": [task_id],
            "bin_no": bin_no,
            "warehouse_number": warehouse_number,
            "box": actual_total_box,
            "quantity": actual_total_qty,
            "palletNum": pallet_no,
            "upc_code": "",
            "user_id": user_id,
            "in_user": in_user
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"

    def lpn_bin_load_confirm(self, task_id, pallet_no, bin_no, lpn_nos, warehouse_number, user_id, in_user, action=0):
        """
        action: 0 normal, 1 bin full, 2 product missing, 3 （废弃）， 4  no bin
        :param task_id:
        :param pallet_no:
        :param bin_no:
        :param lpn_nos:
        :param action:
        :return:
        """
        url = "/wms/restockBinLoad/binLoadConfirmWitchLpn"
        body = {
            "action": action,
            "scannedLpnQuantity": len(lpn_nos),
            "lpnNos": lpn_nos,
            "user_id": user_id,
            "in_user": in_user,
            "warehouse_number": warehouse_number,
            "put_ids": [task_id],
            "bin_no": bin_no,
            "palletNum": pallet_no,
            "mlpnNos": [],
            "scanLocationRecommendQty": 1
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"

    def bin_load_complete(self, pallet_no, warehouse_number, in_user, user_id):
        url = "/wms/restockBinLoad/binLoadComplete"
        body = {
            "pallet_no": pallet_no,
            "warehouseNumber": warehouse_number,
            "inUser": in_user,
            "userId": user_id
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"

    def bin_load_force_complete(self, pallet_no, pwd, warehouse_number, in_user):
        url = "/wms/restockBinLoad/forceComplete"
        body = {
            "pallet_no": pallet_no,
            "warehouseNumber": warehouse_number,
            "inUser": in_user,
            "pwd": pwd
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"

    def no_bin_get_stock(self, item_number, task_id, warehouse_number):
        url = "/wms/restockBinLoad/noBin/listStock"
        body = {
            "item_number": item_number,
            "warehouse_number": warehouse_number,
            "task_id": task_id
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]

    def query_restock_task_list(self, warehouse_number, bin_location="", status="", task_sources=None,
                               task_categories=None, seller_type="", item_number="", stock_location="",
                               order_column="rec_id", order_rule="asc", start_column=0, page_size=10,
                               task_id="", date_type=1, start_time=None, end_time=None):
        """
        查询补货任务列表
        :param warehouse_number: 仓库编号
        :param bin_location: 库位
        :param status: 状态
        :param task_sources: 任务来源列表
        :param task_categories: 任务分类列表
        :param seller_type: 卖家类型
        :param item_number: 商品编号
        :param stock_location: 库存位置
        :param order_column: 排序字段
        :param order_rule: 排序规则 (asc/desc)
        :param start_column: 起始列
        :param page_size: 页面大小
        :param task_id: 任务ID
        :param date_type: 日期类型
        :param start_time: 开始时间戳
        :param end_time: 结束时间戳
        :return: 接口响应
        """
        url = "/wms/restock/queryRestockTaskList"
        body = {
            "warehouse_number": warehouse_number,
            "bin_location": bin_location,
            "status": status,
            "taskSources": task_sources if task_sources is not None else [],
            "taskCategories": task_categories if task_categories is not None else [],
            "sellerType": seller_type,
            "item_number": item_number,
            "stock_location": stock_location,
            "order": {
                "orderColumn": order_column,
                "orderRule": order_rule
            },
            "startColumn": start_column,
            "pageSize": page_size,
            "task_id": task_id,
            "dateType": date_type,
            "startTime": start_time,
            "endTime": end_time
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response["body"]["data"]

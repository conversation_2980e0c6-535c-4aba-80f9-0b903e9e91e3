# !/usr/bin/python3
# -*- coding: utf-8 -*-

import os
import weeeTest
import json
import boto3
from botocore.exceptions import ClientError
from weeeTest import log


def get_weixin_access_token():
    secret_name = "qa/weee-test"
    region_name = "us-east-2"

    session = boto3.session.Session()
    client = session.client(service_name='secretsmanager', region_name=region_name)
    try:
        get_secret_value_response = client.get_secret_value(SecretId=secret_name)
        wechat_token = json.loads(get_secret_value_response['SecretString'])
        return wechat_token
    except ClientError as e:
        log.info("获取企业微信token失败" + str(e))
        return {}


wx_token = get_weixin_access_token()


def run():
    weeeTest.main(
        base_url=os.getenv("base_url") if os.getenv("base_url", None) else "https://api.tb1.sayweee.net",
        debug=True,
        title="Job: " + os.getenv("JOB_NAME", None) + "[" + os.getenv("BUILD_NUMBER",
                                                                      None) + "]" + "  Env: tb1" if os.getenv(
            "JOB_NAME") else "ET",
        tester=os.getenv("BUILD_USER", 'Anybody') or "Anonymous",
        language="en",
        email_to=os.getenv("to") if os.getenv("to") else "jing.wang",
        test_data_dir_name="erp/test_data",
        weixin_access_token=wx_token.get('weixin_access_token') if wx_token.get('weixin_access_token') else None,
        case_list=json.loads(os.getenv("case_list")) if os.getenv("case_list") else [],
        ext=["-m", os.getenv("mark", "Smoke"), "--junitxml=tests/results.xml", "--alluredir=./allure-results"]
    )


if __name__ == '__main__':
    run()

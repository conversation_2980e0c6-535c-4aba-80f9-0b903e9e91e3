# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  kaichuan.wang
@Version        :  V1.0.0
------------------------------------
@File           :  __init__.py.py
@Description    :
@CreateTime     :  2023/9/7 13:35
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/9/7 13:35
"""
import json

from weeeTest.utils.logging import log


class DriverFee:
    def __init__(self, delivery_plan_id, group_point_id, con_db):
        """
        初始化参数
        @param delivery_plan_id:
        @param group_point_id:
        @param con_db: 数据库连接对象
        """
        self.delivery_plan_id = delivery_plan_id
        self.group_point_id = group_point_id
        self.con_db = con_db

        self.delivery_dispatch_info = con_db.get_delivery_type(self.delivery_plan_id, self.group_point_id)  # 派送类型
        self.delivery_type = self.delivery_dispatch_info[2]
        result = con_db.get_delivery_info(self.delivery_plan_id, self.group_point_id)
        self.sub_region_id = result[-2]  # 子区域ID
        self.con_db.get_sub_region_name(self.sub_region_id)
        self.engagement_time = con_db.get_engagement_time_by_point(self.delivery_plan_id, self.group_point_id)
        self.dispatch_type = result[-1]
        self.flex_flag = result[1]  # Flex状态
        if self.flex_flag and self.dispatch_type in ['S'] and self.delivery_type == 'delivery':
            dispatch_res = con_db.get_dispatch_distance(self.delivery_plan_id, self.group_point_id)[0]
            self.distance = float(json.loads(dispatch_res).get('distances').get('v'))
        elif self.flex_flag and self.dispatch_type in ['S'] and self.delivery_type == 'hot_delivery':
            dispatch_res = con_db.get_dispatch_distance(self.delivery_plan_id, self.group_point_id)[1]
            self.distance = float(json.loads(dispatch_res).get('distance'))
        else:
            self.distance = float(result[5])  # 派送距离
        self.driver_user_id = self.delivery_dispatch_info[4]  # 司机ID
        self.delivery_date = self.delivery_dispatch_info[5]  # 配送日期
        self.dispatch_tip = float(result[6])  # 司机小费
        self.count_of_points = result[7]  # 派送点数
        self.zipcode_fee = float(result[8])  # 区域费用
        self.apt_num = float(result[10])  # APT数量

        stop_count = con_db.get_point_count(self.delivery_plan_id, self.group_point_id)
        if self.count_of_points != stop_count and self.flex_flag == 0:
            log.info(f"\033[1;31m此路线有Cancel的站点,实际配送站点数量:{stop_count}\033[0m")
            self.count_of_points = stop_count
            cancel_fee = con_db.get_cancel_task_info(self.delivery_plan_id, self.group_point_id)
            self.zipcode_fee = self.zipcode_fee - float(cancel_fee[1])
            log.info(f"\033[1;31m实际zipcode_fee减少:{cancel_fee[1]}\033[0m")
            self.apt_num = self.apt_num - float(cancel_fee[0])
            log.info(f"\033[1;31m实际APT数量减少:{cancel_fee[0]}\033[0m")

        self.driver_type = con_db.get_driver_info(self.delivery_plan_id, self.group_point_id)
        self.driver_sub_type = con_db.get_driver_info(self.delivery_plan_id, self.group_point_id,
                                                      info='driver_sub_type')
        self.driver_company_id = con_db.get_driver_info(self.delivery_plan_id, self.group_point_id, info="company_id")
        self.zipcodes = con_db.get_delivery_zipcode(self.delivery_plan_id, self.group_point_id)
        self.new_york_zipcodes = con_db.get_new_york_zipcode()

    def get_distance_fee(self):
        """
        获取距离费用
        """
        if self.sub_region_id in [19]:
            # $1.3: (0, 40], $1: (40, 80], $0.5: (80, ∞)
            if self.distance <= 40:
                distance_fee = 1.3 * self.distance
            elif self.distance <= 80:
                distance_fee = self.distance + 12
            else:
                distance_fee = 0.5 * self.distance + 52

        elif self.sub_region_id in [16, 17]:
            # $1.55: [0, 50];$1.3: (50,100]; $1: (100, 130]; $0.5: (130, ∞)
            if self.distance <= 50:
                distance_fee = 1.55 * self.distance
            elif self.distance <= 100:
                distance_fee = 1.3 * self.distance + 12.5
            elif self.distance <= 130:
                distance_fee = self.distance + 42.5
            else:
                distance_fee = 0.5 * self.distance + 107.5

        elif self.sub_region_id in [1, 5, 6, 39, 47, 46]:
            #  $1.2: [0, 50] $1: (50, 130] $0.5: (130, ∞)
            if self.distance <= 50:
                distance_fee = 1.2 * self.distance
            elif self.distance <= 130:
                distance_fee = self.distance + 10
            else:
                distance_fee = 0.5 * self.distance + 75

        elif self.sub_region_id in [7, 45]:
            # $2.5: [0, 35) $1.5: [35, 50] $0.5: (50, ∞)
            if self.distance < 35:
                distance_fee = 2.5 * self.distance
            elif self.distance <= 50:
                distance_fee = 1.5 * self.distance + 35
            else:
                distance_fee = 0.5 * self.distance + 85

        elif self.sub_region_id in [14, 15, 40, 43, 44, 54, 55]:
            # $1.3: [0, 100] $1: (100, 180] $0.8: (180, ∞)
            if self.distance <= 100:
                distance_fee = 1.3 * self.distance
            elif self.distance <= 180:
                distance_fee = self.distance + 30
            else:
                distance_fee = 0.8 * self.distance + 66

        elif self.sub_region_id in []:
            # $2.0 (0, 10]; $1.5 (10,30] $1.0 (30,50], $0.5 (50,∞]
            if self.distance <= 10:
                distance_fee = 2 * self.distance
            elif self.distance <= 30:
                distance_fee = 1.5 * self.distance + 5
            elif self.distance <= 50:
                distance_fee = self.distance + 20
            else:
                distance_fee = 0.5 * self.distance + 45

        elif self.sub_region_id in [13, 20, 21, 22, 10, 11, 57]:
            # $1.3 [0, 50] $1 (50,100] $0.5 (100,∞)
            if self.distance <= 50:
                distance_fee = 1.3 * self.distance
            elif self.distance <= 100:
                distance_fee = self.distance + 15
            else:
                distance_fee = 0.5 * self.distance + 65

        elif self.sub_region_id in [12]:
            # $1.3 [0, 50] $1 (50,100] $0.75 (100,∞)
            if self.distance <= 50:
                distance_fee = 1.3 * self.distance
            elif self.distance <= 100:
                distance_fee = self.distance + 15
            else:
                distance_fee = 0.75 * self.distance + 40

        elif self.sub_region_id in [34, 35, 36, 37, 41]:
            # $1.8 [0, 50]; $1 (50,100] $0.5 (100,∞)
            if self.distance <= 50:
                distance_fee = 1.8 * self.distance
            elif self.distance <= 100:
                distance_fee = self.distance + 40
            else:
                distance_fee = 0.5 * self.distance + 90

        elif self.sub_region_id in [24, 25, 29, 30, 31, 32, 33]:
            # $1.5: [0, 50],$1.1: (50,100]$0.6: (100, ∞) 新
            if self.distance <= 50:
                distance_fee = 1.5 * self.distance
            elif self.distance <= 100:
                distance_fee = 1.1 * self.distance + 20
            else:
                distance_fee = 0.6 * self.distance + 70

        elif self.sub_region_id in [28]:
            # $1.5: [0, 50],$1: (50,100]$0.5: (100, ∞)
            if self.distance <= 50:
                distance_fee = 1.5 * self.distance
            elif self.distance <= 100:
                distance_fee = self.distance + 25
            else:
                distance_fee = 0.5 * self.distance + 75

        elif self.sub_region_id in [26]:
            # $1 [0,100] $0.5 (100,∞)
            if self.distance <= 100:
                distance_fee = self.distance
            else:
                distance_fee = 0.5 * self.distance + 50

        elif self.sub_region_id in [27, 48, 52]:
            # $2.8[0, 20]; $1.3(20, 50], $1(50, 100] $0.5(100,∞)
            if self.distance <= 20:
                distance_fee = 2.8 * self.distance
            elif self.distance <= 50:
                distance_fee = 1.3 * self.distance + 30
            elif self.distance <= 100:
                distance_fee = self.distance + 45
            else:
                distance_fee = 0.5 * self.distance + 95

        elif self.sub_region_id in [23]:
            # $1.3[0, 50); $1[50, 100) $0.77[100,∞)
            if self.distance <= 50:
                distance_fee = 1.3 * self.distance
            elif self.distance <= 100:
                distance_fee = self.distance + 15
            else:
                distance_fee = 0.77 * self.distance + 38

        elif self.sub_region_id in [10, 11, 13, 20, 21, 22, 57]:
            # $1.3 [0, 50]; $1 (50,100]; $0.5 (100,∞)
            if self.distance <= 50:
                distance_fee = 1.3 * self.distance
            elif self.distance <= 100:
                distance_fee = self.distance + 15
            else:
                distance_fee = 0.5 * self.distance + 65

        elif self.sub_region_id in [34, 35, 36, 37, 41]:
            # $1.8 [0, 50]; $1 (50,100] $0.5 (100,∞)
            if self.distance <= 50:
                distance_fee = 1.8 * self.distance
            elif self.distance <= 100:
                distance_fee = self.distance + 40
            else:
                distance_fee = 0.5 * self.distance + 90

        elif self.sub_region_id in [8, 9, 49]:
            # $1.3 [0, 50]; $1 (50,100]; $0.5 (100,160]; $1.0 (160,∞)
            if self.distance <= 50:
                distance_fee = 1.3 * self.distance
            elif self.distance <= 100:
                distance_fee = self.distance + 15
            elif self.distance <= 160:
                distance_fee = 0.5 * self.distance + 65
            else:
                distance_fee = self.distance - 15

        else:
            # $2.5: [0, 40] $1.5: (40, 50] $0.5: (50, ∞)
            if self.distance <= 40:
                distance_fee = 2.5 * self.distance
            elif self.distance <= 50:
                distance_fee = 1.5 * self.distance + 40
            else:
                distance_fee = 0.5 * self.distance + 90

        return distance_fee

    def prop_22_formula(self):
        """
        22法令公式参数
        @return:
        """

        if self.sub_region_id in [18]:
            formula_rate = 15, 2.5
            return formula_rate
        elif self.sub_region_id in [1, 2, 3, 4, 5, 6, 7, 38, 39, 45, 46, 47]:
            formula_rate = 15, 1.5
            return formula_rate
        elif self.sub_region_id in [51]:
            formula_rate = 17.2, 1.5
            return formula_rate
        elif self.sub_region_id in [34, 35, 41]:
            formula_rate = 14.25, 1
            return formula_rate
        elif self.sub_region_id in [14, 15, 40]:
            formula_rate = 15, 1.7
            return formula_rate
        else:
            return 0

    def get_fee_additional_of_apartment(self):
        """
        获取APT（公寓）系数
        :return:
        """
        if self.sub_region_id in [7, 14, 15, 40, 43, 44, 45, 54, 55]:
            return 0.8
        elif self.sub_region_id in [20, 13, 34, 35, 41, 36, 37, 21, 18, 42, 24, 29, 30, 31, 32, 33, 25, 28, 26, 23, 27,
                                    11, 8, 9, 12, 10, 48, 49, 52]:
            return 0
        elif self.sub_region_id in [16, 17]:
            return 0.5
        elif self.sub_region_id in [22, 57]:
            return 1
        else:
            return 1.3

    def get_rate_of_address(self):
        """
        送货点费率
        """
        if self.sub_region_id in [7, 10, 12, 13, 16, 17, 18, 19, 20, 21, 27, 42, 45, 48, 52]:
            # # $0.75: [0, 20] $0.85: (20, 30] $0.95:(30, 40) $1: [40, 50) $1.02: [50,∞)
            if self.count_of_points <= 20:
                rate = 0.75
            elif self.count_of_points <= 30:
                rate = 0.85
            elif self.count_of_points < 40:
                rate = 0.95
            elif self.count_of_points < 50:
                rate = 1
            else:
                rate = 1.02
            return rate

        elif self.sub_region_id in [14, 15, 40, 43, 44, 54, 55]:
            # 0.75: [0, 20]1: (20, 50] 1.02: (50, 55)1.08: [55, ∞ )
            if self.count_of_points <= 20:
                rate = 0.75
            elif self.count_of_points <= 50:
                rate = 1
            elif self.count_of_points < 55:
                rate = 1.02
            else:
                rate = 1.08
            return rate

        elif self.sub_region_id in [8, 9, 23, 49]:
            # $0.75: [0, 20); $0.85: [20, 30); $0.95: [30, 40);$1: [40, 50); $1.02: [50,∞)
            if self.count_of_points < 20:
                rate = 0.75
            elif self.count_of_points < 30:
                rate = 0.85
            elif self.count_of_points < 40:
                rate = 0.95
            elif self.count_of_points < 50:
                rate = 1
            else:
                rate = 1.02
            return rate

        elif self.sub_region_id in [11]:
            # $0.75: [0, 20); $0.85: [20, 30); $0.95: [30, ∞)
            if self.count_of_points < 20:
                rate = 0.75
            elif self.count_of_points < 30:
                rate = 0.85
            else:
                rate = 0.95
            return rate

        elif self.sub_region_id in [24, 25, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 41]:
            return 1

        elif self.sub_region_id in [22, 57]:
            # $0.75: [0, 20] $0.90: (20, 30] $0.95:(30, 40)$1: [40, 50) $1.02: [50,∞)
            if self.count_of_points <= 20:
                rate = 0.75
            elif self.count_of_points <= 30:
                rate = 0.9
            elif self.count_of_points < 40:
                rate = 0.95
            elif self.count_of_points < 50:
                rate = 1
            else:
                rate = 1.02
            return rate

        elif self.sub_region_id in [3] and self.flex_flag == 1:
            # 0.8(0, 36] 1 (36, 45] $1.06: (45, 55) $1.10: [55, ∞)
            if self.count_of_points <= 36:
                rate = 0.8
            elif self.count_of_points <= 45:
                rate = 1
            elif self.count_of_points < 55:
                rate = 1.06
            else:
                rate = 1.1
            return rate

        elif self.sub_region_id in [1, 2, 3, 5, 6, 38, 39, 46, 47, 51]:
            # $0.75: [0, 20] $0.85: (20, 30] $0.9: (30, 35) $0.95: [35, 40)$1: [40, 46) $1.06: [46, 55) $1.10: [55, ∞)
            if self.count_of_points <= 20:
                rate = 0.75
            elif self.count_of_points <= 30:
                rate = 0.85
            elif self.count_of_points < 35:
                rate = 0.9
            elif self.count_of_points < 40:
                rate = 0.95
            elif self.count_of_points < 46:
                rate = 1
            elif self.count_of_points < 55:
                rate = 1.06
            else:
                rate = 1.1
            return rate

        elif self.sub_region_id in [26]:
            # $0.75: [0, 20] $0.85: (20, 30] $0.95: (30, 40)$1: [40, ∞)
            if self.count_of_points <= 20:
                rate = 0.75
            elif self.count_of_points <= 30:
                rate = 0.85
            elif self.count_of_points < 40:
                rate = 0.95
            else:
                rate = 1
            return rate

        else:
            # 0.75:[0,20];0.85:(20,30];0.9:(30,35);0.95:[35,40);1:[40,45);1.06:[45,50);1.13:[50,55);1.15:[55,58);1.17:[58,62);1.19:[62,68];1.2:(68,~)
            if self.count_of_points <= 20:
                rate = 0.75
            elif self.count_of_points <= 30:
                rate = 0.85
            elif self.count_of_points < 35:
                rate = 0.9
            elif self.count_of_points < 40:
                rate = 0.95
            elif self.count_of_points < 45:
                rate = 1
            elif self.count_of_points < 50:
                rate = 1.06
            elif self.count_of_points < 55:
                rate = 1.13
            elif self.count_of_points < 58:
                rate = 1.15
            elif self.count_of_points < 62:
                rate = 1.17
            elif self.count_of_points <= 68:
                rate = 1.19
            else:
                rate = 1.2
            return rate

    def get_special_zipcode_fee(self):
        """
        获取特殊zipcode费用
        :return:
        """

        if self.sub_region_id in [14, 15, 40]:
            inter_zipcode = list(set(self.zipcodes) & set(self.new_york_zipcodes))
            if inter_zipcode:
                log.info("注意该派车单包含纽约特殊的zipcode，需加25$的过桥费")
                bridge_fee = 25
            else:
                bridge_fee = 0.0

            special_zipcode = [11351, 11352, 11375, 11354, 11355, 11356, 11357, 11358, 11359, 11360, 11361, 11362,
                               11363, 11364, 11365, 11366, 11367, 11368, 11369, 11370, 11371, 11372, 11373, 11374,
                               11378, 11379, 11380, 11385, 11386, 11411, 11412, 11413, 11414, 11415, 11416, 11417,
                               11418, 11419, 11420, 11421, 11422, 11423, 11424, 11426, 11427, 11428, 11429, 11430,
                               11431, 11432, 11433, 11434, 11435, 11436, 11004, 11005, 10001, 10002, 10003, 10004,
                               10005, 10006, 10007, 10009, 10010, 10011, 10012, 10013, 10014, 10016, 10017, 10018,
                               10019, 10020, 10021, 10023, 10024, 10025, 10026, 10027, 10028, 10029, 10030, 10031,
                               10032, 10033, 10035, 10036, 10037, 10038, 10039, 10040, 10048, 11101, 11102, 11103,
                               11104, 11105, 11106, 11377, 11109, 11120, 10128, 11690, 11691, 11692, 11693, 11694,
                               11695, 11697, 11201, 11203, 11204, 11205, 11206, 11207, 11208, 11209, 11210, 11211,
                               11212, 11213, 11214, 11215, 11216, 11217, 11218, 11219, 11220, 11221, 11222, 11223,
                               11224, 11225, 11226, 11228, 11229, 11230, 11231, 11232, 11233, 11234, 11235, 11236,
                               11237, 11238, 11239]
            inter_zipcode = []
            for i in self.zipcodes:
                if i in special_zipcode:
                    inter_zipcode.append(i)
            if len(inter_zipcode) > 0:
                log.info("注意该派车单包含特殊zipcode，每个加 0.5$ 的特殊zipcode区域运费")
            special_zipcode_fee = bridge_fee + len(inter_zipcode) * 0.25
            log.info(f"special_zipcode_fee({special_zipcode_fee}) = "
                     f"过桥费（{bridge_fee}） + zipcode费（{len(inter_zipcode)}）* 0.25")
            return special_zipcode_fee
        elif self.sub_region_id in [5, 6, 13, 39, 47]:
            # LA送货路线经过CA241、CA261收费高速公路的任何一个zipcode, add $9
            special_zipcode = [92602, 92606, 92610, 92618, 92620, 92630, 92637, 92653, 92679, 92688, 92691, 92692]
            inter_zipcode = list(set(self.zipcodes) & set(special_zipcode))
            if inter_zipcode:
                log.info(
                    f"注意该地区包含特殊zipcode：\033[1;35m{inter_zipcode}\033[0m，可能需加 9 $ 的特殊zipcode区域运费")
                special_zipcode_fee = 9
            else:
                special_zipcode_fee = 0.0
            return special_zipcode_fee

        elif self.sub_region_id in [11]:
            special_zipcode_1 = [30601, 30602, 30605, 30606, 30607, 30609, 30622, 30646, 30683]
            inter_zipcode_1 = list(set(self.zipcodes) & set(special_zipcode_1))
            log.info("inter_zipcode_1为：", inter_zipcode_1)
            special_zipcode_2 = [36801, 36830, 36832]
            inter_zipcode_2 = list(set(self.zipcodes) & set(special_zipcode_2))
            log.info("inter_zipcode_2为：", inter_zipcode_2)
            if bool(inter_zipcode_1):
                log.info("注意该地区是否包含特殊zipcode，需加 30$ 的特殊zipcode区域运费")
            if bool(inter_zipcode_2):
                log.info("注意该地区是否包含特殊zipcode，需加 50$ 的特殊zipcode区域运费")
            special_zipcode_fee = bool(inter_zipcode_1) * 30 + bool(inter_zipcode_2) * 50
            return special_zipcode_fee
        else:
            return 0.0

    def get_min_points_num(self):
        """
        获取最小派送点数
        """
        # 1,2,3,4,5,6,7,38,39,45,46,47
        if self.sub_region_id in [1, 2, 3, 4, 5, 6, 7, 38, 39, 45, 46, 47, 51]:
            min_num = 0
        else:
            min_num = 15
        log.info(f"此区域最小派送点数{min_num}")
        return min_num

    def get_distance_point_rate(self):
        """
        获取距离系数及派送点系数
        线路类型 0-all 1-normal 2-hot 3-alcohol 4-flex
        """
        if self.sub_region_id == 18 and self.delivery_type == "alcohol_delivery":
            rate = 0.56, 4.5
            log.info("此销售组织距离系数：{},派送点系数：{}".format(rate[0], rate[1]))
            return rate
        elif self.sub_region_id in [18, 42] and self.driver_type != 'F' and self.driver_sub_type != 2:
            rate = 0.5432, 3.395
            log.info("此销售组织距离系数：{},派送点系数：{}".format(rate[0], rate[1]))
            return rate
        elif self.sub_region_id == 4:
            rate = 0.8, 3.3
            log.info("此销售组织距离系数：{},派送点系数：{}".format(rate[0], rate[1]))
            return rate
        elif self.sub_region_id in [1, 2, 3, 38] and self.delivery_type == "hot_delivery":
            rate = 0.56, 3.2
            log.info("此销售组织距离系数：{},派送点系数：{}".format(rate[0], rate[1]))
            return rate
        elif self.sub_region_id in [14, 15, 40, 43, 44, 54, 55] and self.delivery_type == "hot_delivery":
            rate = 0.8, 4
            log.info("此销售组织距离系数：{},派送点系数：{}".format(rate[0], rate[1]))
            return rate
        elif self.sub_region_id in [34, 35, 41] and self.delivery_type == "hot_delivery":
            rate = 0.83, 3.5
            log.info("此销售组织距离系数：{},派送点系数：{}".format(rate[0], rate[1]))
            return rate
        elif self.sub_region_id in [14, 15, 54, 55] and self.flex_flag == 1:
            log.info("该条路线为Flex路线，按Flex运费公式计算！")
            rate = 0.5, 1.5
            log.info("此销售组织距离系数：{},派送点系数：{}".format(rate[0], rate[1]))
            return rate
        elif self.sub_region_id in [47] and self.flex_flag == 1:
            log.info("该条路线为Flex路线，按Flex运费公式计算！")
            rate = 1.12, 0.7
            log.info("此销售组织距离系数：{},派送点系数：{}".format(rate[0], rate[1]))
            return rate
        elif self.sub_region_id in [18, 42] and self.flex_flag == 1:
            log.info("该条路线为Flex路线，按Flex运费公式计算！")
            rate = 0.5432, 3.395
            log.info("此销售组织距离系数：{},派送点系数：{}".format(rate[0], rate[1]))
            return rate
        elif self.sub_region_id in [40, 43, 44] and self.flex_flag == 1:
            log.info("该条路线为Flex路线，按Flex运费公式计算！")
            rate = 1.3, 1.5
            log.info("此销售组织距离系数：{},派送点系数：{}".format(rate[0], rate[1]))
            return rate
        else:
            return False

    @staticmethod
    def get_seafood_fee():
        """
        获取活生鲜费用
        :return:
        """
        seafood_fee = 0  # 活生鲜费用
        return seafood_fee

    def get_prop_22_fee(self):
        """
        获取22法令费用
        @return:
        """
        formula_rate = self.prop_22_formula()
        if formula_rate:
            if self.delivery_type == 'delivery':
                prop_22_fee = 1.2 * formula_rate[0] * self.engagement_time + self.distance * 0.34
                log.info(
                    f"22法令基本费用: prop_22_fee({prop_22_fee:.2f}) = 1.2 * formula_rate({formula_rate[0]}) * "
                    f"engagement_time({self.engagement_time:.2f})  + distance({self.distance}) * 0.34")
                return prop_22_fee
            else:
                prop_22_fee = 1.2 * formula_rate[0] * (self.engagement_time + formula_rate[1]) + self.distance * 0.34
                log.info(
                    f"22法令基本费用: prop_22_fee({prop_22_fee:.2f}) = 1.2 * formula_rate({formula_rate[0]}) * (engagement_time("
                    f"{self.engagement_time:.2f}) + formula_rate({formula_rate[1]})) + distance({self.distance}) * 0.34")
                return prop_22_fee
        else:
            log.info('此地区无22法令运费要求')
            return 0

    def get_delivery_fee(self):
        """
        获取司机派送费用（不包含小费）
        :return:
        """
        distance_fee = self.get_distance_fee()  # 距离费用
        special_zipcode_fee = self.get_special_zipcode_fee()  # 特殊zipcode区域运费
        apt_fee = self.get_fee_additional_of_apartment() * self.apt_num  # APT运费
        address_rate = self.get_rate_of_address()  # 送货点系数
        min_num = self.get_min_points_num()  # 最小派送点数
        rate = self.get_distance_point_rate()  # 距离系数及派送点系数
        seafood_fee = self.get_seafood_fee()  # 活生鲜费用
        clock_time = self.con_db.driver_clock_time(self.driver_user_id, self.delivery_date)  # 打卡时长

        if self.driver_type == 'F' and self.driver_sub_type == 2:
            log.info(f'此司机为私家车W2司机,需要计算额外运费!')
            log.info(
                f"类型：{self.delivery_type},距离：{self.distance},小费：{self.dispatch_tip},派送数：{self.count_of_points},"
                f"工作时长：{clock_time:.2f}")
            working_hour_fee = clock_time * 19.97
            distance_fee = self.distance * 0.67
            delivery_fee = working_hour_fee + distance_fee
            log.info(f'working_hour_fee:{working_hour_fee:.2f}, distance_fee:{distance_fee:.2f}')
            log.info(
                f'delivery_fee({delivery_fee:.2f}) = working_time({clock_time:.2f}) * 19.97 + self.distance({self.distance}) * 0.67')
            return round(delivery_fee, 2)
        elif self.driver_type == 'F':
            log.info("此司机为全职司机，无配送费")
            return 0
        elif self.driver_sub_type == 3 and self.driver_type == 'P':
            log.info(f'此司机为DSP兼职司机')
            delivery_fee = 3.5 * self.count_of_points
            log.info(
                f'delivery_fee({delivery_fee:.2f}) = count_of_points({self.count_of_points}) * 3.5')
            return round(delivery_fee, 2)
        else:
            log.info("-" * 30 + "\033[1;35m 基础数据 \033[0m" + "-" * 30)
            log.info(
                f"类型：{self.delivery_type},距离：{self.distance},小费：{self.dispatch_tip},派送数：{self.count_of_points},zipcode费用：{self.zipcode_fee},APT数量：{self.apt_num}")
            # 该地区有特殊运费计算公式时，按公式计算
            if rate:
                # 司机运费 = 距离 * 系数 + 派送点数 * 系数 + 司机小费
                delivery_fee = self.distance * rate[0] + self.count_of_points * rate[1]
                log.info(
                    f"司机1配送费用({delivery_fee:.2f}) = 距离({self.distance}) * 距离系数({rate[0]}) + "
                    f"派送点数({self.count_of_points}） * 派送点系数({rate[1]})")

            elif self.sub_region_id in [5, 6, 13, 39, 47] and self.delivery_type == "hot_delivery":
                log.info(
                    f"订单为LA热送贴，不用加 活生鲜运费、APT运费、特殊区域运费，也不用乘以送货点系数及热送系数")
                delivery_fee = (self.zipcode_fee + distance_fee) * 0.88
                log.info(
                    f"司机2配送费用为：{delivery_fee:.2f} =  (zipcode运费({self.zipcode_fee}) + 距离运费({distance_fee:.2f})） * 0.88")

            elif self.count_of_points > min_num or self.sub_region_id in [3, 46]:
                # 司机运费 = {zipcode运费 + 距离运费 + 活生鲜运费 + 特殊zipcode区域运费 + APT运费} * 送货点系数 + 司机小费
                log.info("-" * 30 + "\033[1;35m 运费组成 \033[0m" + "-" * 30)
                log.info(
                    f"zipcode运费：{self.zipcode_fee},距离运费：{distance_fee:.2f}, 活生鲜运费：{seafood_fee}, APT运费：{apt_fee:.2f}, 特殊区域运费：{special_zipcode_fee},送货点系数：{address_rate}")

                # 销售组织条件判断
                if self.sub_region_id in [1, 2, 38] and self.driver_company_id == 2:
                    log.info("此司机为湾区Openforce 3P司机，需要扣除额外费用")
                    delivery_fee = (
                                           self.zipcode_fee + distance_fee + seafood_fee + apt_fee + special_zipcode_fee - 0.5 *
                                           self.count_of_points) * address_rate
                    log.info(
                        f"司机3配送费用：{delivery_fee:.2f} = [zipcode运费({self.zipcode_fee}) + 距离运费({distance_fee:.2f}) + 活生鲜运费"
                        f"({seafood_fee}) + APT运费({apt_fee:.2f}) + 特殊区域运费({special_zipcode_fee}) - 0.5 *"
                        f" {self.count_of_points}] * 送货点系数({address_rate})")

                else:
                    delivery_fee = (
                                           self.zipcode_fee + distance_fee + seafood_fee + apt_fee + special_zipcode_fee) * address_rate
                    log.info(
                        f"司机4配送费用：{delivery_fee:.2f} = [zipcode运费({self.zipcode_fee}) + 距离运费({distance_fee:.2f}) + "
                        f"活生鲜运费({seafood_fee}) + APT运费({apt_fee:.2f}) + 特殊区域运费({special_zipcode_fee})]"
                        f" * 送货点系数({address_rate})")
            else:
                # 小于等于最小派送数时，司机运费 = 派送数 * 5 + 司机小费
                delivery_fee = self.count_of_points * 5
                log.info(
                    f"小于最小派送数，按公式:司机5总费用({delivery_fee:.2f}) = 派送点数({self.count_of_points}) * 5")
            return delivery_fee

    def get_driver_free(self):
        """
        计算司机总运费
        """

        delivery_fee = self.get_delivery_fee()
        out_layer_rate = self.con_db.get_out_layer_rate(sub_region_id=self.sub_region_id,
                                                        version=self.delivery_dispatch_info[1],
                                                        flex_flag=self.flex_flag)
        log.info("-" * 30 + "\033[1;35m 运费计算 \033[0m" + "-" * 30)
        if self.driver_sub_type == 2 and self.driver_type == 'F':
            final_fee = delivery_fee * out_layer_rate + self.dispatch_tip
            log.info(
                f'私家车W2司机运费({final_fee}) = 公司成本({delivery_fee}) * 外围系数({out_layer_rate}) + 小费({self.dispatch_tip})')
            return round(final_fee, 2)
        elif self.driver_sub_type == 3 and self.sub_region_id in [18, 19, 42, 50]:
            final_fee = delivery_fee + self.dispatch_tip
            log.info(
                f'DSP兼职司机运费({final_fee}) = 派送费用({delivery_fee}) + 小费({self.dispatch_tip})')
            return round(final_fee, 2)
        elif self.delivery_type == "delivery" and self.driver_type != 'F':
            prop_22 = self.get_prop_22_fee()
            if self.flex_flag == 1 and self.sub_region_id in [1, 2, 3, 5, 51, 46]:
                log.info("此路线为满足合规要求的Flex路线, 单路线运费计算")
                delivery_route_fee = 2.2 * self.count_of_points + 0.3 * self.distance
                log.info(
                    f"\033[1;35m单路线Flex费用({delivery_route_fee:.2f}) = 2.2 * 站点数量({self.count_of_points}) + 0.3 * 距离({self.distance})\033[0m")
                final_fee = delivery_route_fee * out_layer_rate + self.dispatch_tip
                log.info(
                    f"\033[1;35mFlex路线100%比例：司机总费用 ({final_fee:.2f}) = 派送费用({delivery_route_fee:.2f}) * 外围系数({out_layer_rate}) + 司机小费({self.dispatch_tip})\033[0m")
                # Flex 110%运费比例
                final_fee_110 = delivery_route_fee * 1.1 * out_layer_rate + self.dispatch_tip
                log.info(
                    f"\033[1;35mFlex路线110%比例：司机总费用 ({final_fee_110:.2f}) = 派送费用({delivery_route_fee:.2f}) * 1.1 * 外围系数({out_layer_rate}) + 司机小费({self.dispatch_tip})\033[0m")

            if self.flex_flag == 1 and self.sub_region_id in [6]:
                log.info("此路线为满足合规要求的Flex路线, 单路线运费计算")
                delivery_route_fee = 3.16 * self.count_of_points + 0.34 * self.distance + 0.14 * self.apt_num
                log.info(
                    f"\033[1;35m单路线Flex费用({delivery_route_fee:.2f}) = 3.16 * 站点数量({self.count_of_points}) + 0.34 * 距离({self.distance}) + 0.14 * APT数量({self.apt_num}) \033[0m")

            if prop_22:
                prop_22_fee = prop_22 + self.dispatch_tip
                log.info(f"需要满足最新22法令最低工资要求，最低工资为：\033[1;35m{prop_22_fee:.2f}\033[0m")
            if self.flex_flag == 1:
                log.info("-" * 30 + "此路线为Flex路线" + "-" * 30)
                # Flex 100%运费比例
                final_fee = delivery_fee * out_layer_rate + self.dispatch_tip
                log.info(
                    f"\033[1;35mFlex路线100%比例：司机总费用 ({final_fee:.2f}) = 派送费用({delivery_fee:.2f}) * 外围系数({out_layer_rate}) + 司机小费({self.dispatch_tip})\033[0m")
                # Flex 110%运费比例
                final_fee_110 = delivery_fee * 1.1 * out_layer_rate + self.dispatch_tip
                log.info(
                    f"\033[1;35mFlex路线110%比例：司机总费用 ({final_fee_110:.2f}) = 派送费用({delivery_fee:.2f}) * 1.1 * 外围系数({out_layer_rate}) + 司机小费({self.dispatch_tip})\033[0m")
                # Flex 125%运费比例
                final_fee_125 = delivery_fee * 1.25 * out_layer_rate + self.dispatch_tip
                log.info(
                    f"\033[1;35mFlex路线125%比例：司机总费用 ({final_fee_125:.2f}) = 派送费用({delivery_fee:.2f}) * 1.25 * 外围系数({out_layer_rate}) + 司机小费({self.dispatch_tip})\033[0m")
                return round(final_fee, 2)
            else:
                if self.sub_region_id in [18, 19, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 42, 48, 50, 52]:
                    log.info("-" * 30 + "此地区运费需扣减 3%" + "-" * 30)
                    final_fee = delivery_fee * 0.97 + self.dispatch_tip
                    log.info(
                        f"\033[1;35m订单为普通单：司机总费用 ({final_fee:.2f}) = 派送费用({delivery_fee:.2f}) * 0.97 + 司机小费({self.dispatch_tip})\033[0m")
                    return round(final_fee, 2)

                elif self.sub_region_id in [14, 15, 16, 17, 20, 21, 22, 40, 43, 44, 5, 6, 7, 39, 45, 46, 47, 54, 55]:
                    if self.flex_flag == 0:
                        log.info("-" * 30 + "此地区运费需扣减 5%" + "-" * 30)
                        final_fee = delivery_fee * 0.95 + self.dispatch_tip
                        log.info(
                            f"\033[1;35m订单为普通单：司机总费用 ({final_fee:.2f}) = 派送费用({delivery_fee:.2f}) * 0.95 + 司机小费({self.dispatch_tip})\033[0m")
                        return round(final_fee, 2)
                else:
                    final_fee = delivery_fee * out_layer_rate + self.dispatch_tip
                    log.info(
                        f"\033[1;35m订单为普通单：司机总费用({final_fee:.2f}) = 派送费用({delivery_fee:.2f}) * 外围系数({out_layer_rate}) + 司机小费({self.dispatch_tip})\033[0m")
                    return round(final_fee, 2)
        elif self.delivery_type == "hot_delivery":
            prop_22 = self.get_prop_22_fee()
            if prop_22:
                prop_22_fee = prop_22 + self.dispatch_tip
                log.info(f"需要满足最新22法令最低工资要求，最低工资为：\033[1;35m{prop_22_fee:.2f}\033[0m")

            if self.sub_region_id in [34, 35, 41]:
                final_fee = delivery_fee * out_layer_rate + self.dispatch_tip
                log.info(
                    f"\033[1;35m订单为热送单：司机总费用({final_fee:.2f}) = 派送费用({delivery_fee:.2f}) * 外围系数({out_layer_rate}) + 司机小费({self.dispatch_tip})\033[0m")
                return round(final_fee, 2)
            elif self.sub_region_id in [14, 15, 40, 54, 55]:
                log.info("-" * 30 + "此地区运费需扣减 5%" + "-" * 30)
                final_fee = delivery_fee * 0.95 + self.dispatch_tip
                log.info(
                    f"\033[1;35m订单为热送单：司机总费用({final_fee:.2f}) = 派送费用({delivery_fee:.2f}) * 0.95 + 司机小费({self.dispatch_tip})\033[0m")
                return round(final_fee, 2)
            elif self.sub_region_id in [18]:
                log.info("-" * 30 + "此地区运费需扣减 3%" + "-" * 30)
                final_fee = delivery_fee * 0.97 + self.dispatch_tip
                log.info(
                    f"\033[1;35m订单为热送单：司机总费用({final_fee:.2f}) = 派送费用({delivery_fee:.2f}) * 0.97 + 司机小费({self.dispatch_tip})\033[0m")
                return round(final_fee, 2)
            elif self.sub_region_id in [38, 39]:
                flex_pay = 2.2 * self.count_of_points + 0.3 * self.distance
                pay = flex_pay * out_layer_rate + self.dispatch_tip
                log.info(
                    f"\033[1;35m订单为此地区为RTG合规单：Flex费用({flex_pay:.2f}) = 2.2 * 站点数量({self.count_of_points}) + 0.3 * 距离({self.distance})\033[0m")
                log.info(
                    f"\033[1;35m订单为此地区为RTG合规单：Flex总费用({pay:.2f}) = Flex运费({flex_pay}) * 外围系数({out_layer_rate}) + 司机小费({self.dispatch_tip})\033[0m")
                final_fee = delivery_fee * out_layer_rate + self.dispatch_tip
                log.info(
                    f"\033[1;35m订单为此地区为RTG合规单：100%司机总费用({final_fee:.2f}) = 派送费用({delivery_fee:.2f}) * 外围系数({out_layer_rate}) + 司机小费({self.dispatch_tip})\033[0m")
                final_fee_110 = delivery_fee * 1.1 * out_layer_rate + self.dispatch_tip
                log.info(
                    f"\033[1;35m订单为此地区为RTG合规单：110%司机总费用({final_fee_110:.2f}) = 派送费用({delivery_fee:.2f}) * 1.1 * 外围系数({out_layer_rate}) + 司机小费({self.dispatch_tip})\033[0m")
                return round(final_fee, 2)
            else:
                final_fee = delivery_fee * out_layer_rate + self.dispatch_tip
                log.info(
                    f"\033[1;35m订单为热送单：司机总费用({final_fee:.2f}) = 派送费用({delivery_fee:.2f}) * 外围系数({out_layer_rate}) + 司机小费({self.dispatch_tip})\033[0m")
                return round(final_fee, 2)
        else:
            final_fee = delivery_fee * 0.8 * out_layer_rate + self.dispatch_tip
            log.info(
                f"\033[1;35m订单可能为专贴：司机总费用({final_fee:.2f}) = 派送费用({delivery_fee:.2f}) * 0.8 * 外围系数({out_layer_rate}) + 司机小费({self.dispatch_tip})\033[0m")
            return round(final_fee, 2)

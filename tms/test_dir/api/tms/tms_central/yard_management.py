# Author: bo.liu
# Time: 2025/3/18 17:58
# E-Mail: <EMAIL>
import weeeTest
from weeeTest import jmespath,log
from tms.test_dir.api.tms.tms import tms
from tms.test_dir.api.tms import central_header


class YardManagement(weeeTest.TestCase):
    def list_park_slots(self, delivery_station_ids ,check_result=False):
        '''
        获取slot列表
        deliveryStationIds 仓库id
        '''
        body = {"pageNumber":1,"pageSize":10,"deliveryStationIds":delivery_station_ids}
        self.post(url='/tms/yard/parking/listParkSlots', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'获取slot成功。')
        elif check_result:
            raise Exception(f'获取slot失败, Error response:{self.response}')
        else:
            log.warning(f'获取slot失败, Error response:{self.response}')
        return self.response
    def get_park_route(self, delivery_station_ids ,check_result=False):
        '''
        获取对应的路线-当日的w2路线列表
        deliveryStationIds 仓库id
        '''
        params = {"deliveryStationIds":delivery_station_ids}
        self.get(url='/tms/yard/parking/listYardDispatch', headers=central_header, params=params)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'获取路线成功。')
        elif check_result:
            raise Exception(f'获取路线失败, Error response:{self.response}')
        else:
            log.warning(f'获取路线失败, Error response:{self.response}')
        return self.response
    def update_slot(self,slot_id,action,dispatch_id=None,check_result=False):
        '''
        指定slot
        dispatchId 路线id
        slotId slot id
        action 动作-assign or release
        '''
        if action == 'release':
            body = {"slotId":slot_id,"action":action}
        else:
            body = {"dispatchIds": dispatch_id, "slotId": slot_id, "action": action}
        self.post(url='/tms/yard/parking/action', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'指定/释放slot成功。')
        elif check_result:
            raise Exception(f'指定/释放slot失败, Error response:{self.response}')
        else:
            log.warning(f'指定/释放slot失败, Error response:{self.response}')
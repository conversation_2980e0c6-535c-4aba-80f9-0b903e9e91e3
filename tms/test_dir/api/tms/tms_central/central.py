# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  kaichuan.wang
@Version        :  V1.0.0
------------------------------------
@File           :  api.py
@Description    :
@CreateTime     :  2023/6/9 15:29
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/6/9 15:29
"""
import weeeTest
from weeeTest import log
from tms.test_dir.api.tms import central_header
from weeeTest.utils import jmespath

from tms.test_dir.api.tms.tms_central.address_check import Address
from tms.test_dir.api.tms.tms_central.driver_manage import DriverManage
from tms.test_dir.api.tms.tms_central.fleet_management import FleetManage
from tms.test_dir.api.tms.tms_central.payment import Payment
from tms.test_dir.api.tms.tms_central.route_plan import RoutePlan


class TmsCentral(weeeTest.TestCase):
    """
    Central公共方法封装
    """
    driver_manage = DriverManage()
    route_plan = RoutePlan()
    address = Address()
    fleet = FleetManage()
    payment = Payment()

    def anon_auth(self):
        """
        获取匿名token
        :return:
        """
        self.get(url='/ec/customer/login/token/generate')
        auth = jmespath(self.response, "object.token")
        log.info(f'匿名auth:{auth}')
        central_header["authorization"] = 'Bearer ' + auth
        return auth

    def central_login(self, user_id: int = None, password: str = None, login_platform: str = 'TMS'):
        """
        central登录
        :param login_platform: 平台
        :param user_id: 账户ID
        :param password: 密码
        :return:
        """
        if user_id is None or password is None:
            raise Exception('登录的user_id,password不能为空')
        body = {
            "account": user_id,
            "password": password,
            "realm_key": "ecommerce",
            "login_platform": login_platform
        }
        self.anon_auth()
        self.post(url='/hub/auth/user/login', headers=central_header, json=body)
        auth = jmespath(self.response, "object.token")
        if auth is not None and len(auth) > 0:
            log.info(f'Central登录成功,CentralToken:{auth}')
            central_header["authorization"] = 'Bearer ' + auth
        else:
            raise Exception(f'Central登录失败,msg:{jmespath(self.response, "message")}')
        return self.response

    def update_dispatch_distance(self, dispatch_id, distance, check_result=False):
        """
        更新派车单距离
        :param dispatch_id: 派车单ID
        :param distance: 派车单距离
        :param check_result:
        :return:
        """

        body = {
            "updateType": 3,
            "dispatchId": dispatch_id,
            "distance": distance
        }
        self.post(url='/tms/dispatch/updateDispatchDistance', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'更新派车单{dispatch_id}距离为{distance}成功。')
        elif check_result:
            raise Exception(f'更新派车单{dispatch_id}距离为{distance}失败, Error response:{self.response}')
        else:
            log.warning(f'更新派车单{dispatch_id}距离为{distance}失败, Error response:{self.response}')
        return self.response

    def allow_route_modification(self, draft_id, flex_unlock, check_result=False):
        """
        允许Flex路线可调整
        :param draft_id: 草稿ID
        :param flex_unlock: 1可调整, 0不可调整
        :param check_result:
        :return:
        """

        body = {
            "draftId": "456",
            "flexUnlock": flex_unlock,
            "partUnlock": 1,
            "fullUnlock": 1,
            "comment": "Autotest"
        }
        self.post(url='/tms/draftUnlockConfig/save', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'设置草稿ID:{draft_id}的Flex路线为可调整成功。')
        elif check_result:
            raise Exception(f'设置草稿ID:{draft_id}的Flex路线为可调整失败, Error response:{self.response}')
        else:
            log.warning(f'设置草稿ID:{draft_id}的Flex路线为可调整失败, Error response:{self.response}')
        return self.response

    def change_delivery_date(self, dispatch_id, delivery_date, driver_user_id, pass_word, check_result=False):
        """
        更改派车单日期
        :param dispatch_id:
        :param delivery_date:
        :param driver_user_id:
        :param pass_word:
        :param check_result:
        :return:
        """
        body = {
            "updateType": 2,
            "dispatchId": dispatch_id,
            "deliveryDate": delivery_date,
            "driverUserId": driver_user_id,
            "password": pass_word
        }
        self.post(url='/tms/dispatch/changeDeliveryDate', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'更改派车单{dispatch_id}日期为{delivery_date}成功。')
        elif check_result:
            raise Exception(f'更改派车单{dispatch_id}日期为{delivery_date}失败, Error response:{self.response}')
        else:
            log.warning(f'更改派车单{dispatch_id}日期为{delivery_date}失败, Error response:{self.response}')
        return self.response

    def change_route_status(self, route_id, delivery_id, status, check_result=False):
        """
        更改路线状态
        :param route_id:
        :param delivery_id:
        :param status:
        :param check_result:
        :return:
        """
        body = {
            "route_id": route_id,
            "delivery_id": delivery_id,
            "status": status,
            "in_user": "Jesus Contreras(14511467)",
            "cancel_tracking_num_list": [],
            "is_block_list": [],
            "is_cancel_list": []
        }
        self.post(url='/tms/dispatch/route/update', headers=central_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'更改路线{route_id}状态为{status}成功。')
        elif check_result:
            raise Exception(f'更改路线{route_id}状态为{status}失败, Error response:{self.response}')
        else:
            log.warning(f'更改路线{route_id}状态为{status}失败, Error response:{self.response}')
        return self.response

    def release_all_slot(self, check_result=False):
        """
        释放所有slot
        :param check_result:
        :return:
        """
        self.get(url='/tms/job/parking/clearParkSlots', headers=central_header)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'释放车位job执行成功。')
        elif check_result:
            raise Exception(f'释放车位job执行失败, Error response:{self.response}')
        else:
            log.warning(f'释放车位job执行失败, Error response:{self.response}')
        return self.response

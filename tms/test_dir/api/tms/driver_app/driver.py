# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  kaichuan.wang
@Version        :  V1.0.0
------------------------------------
@File           :  api.py
@Description    :  
@CreateTime     :  2023/6/9 15:29
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/6/9 15:29
"""
import weeeTest
from weeeTest import log
from tms.test_dir.api.tms import driver_header
from weeeTest.utils import jmespath

from tms.test_dir.api.tms.driver_app.driver_delivery import DriverDelivery
from tms.test_dir.api_case.tms import utils


class TmsDriver(weeeTest.TestCase):
    """
    Driver相关方法封装
    """
    driver_delivery = DriverDelivery()

    def anon_auth(self):
        """
        获取匿名token
        :return:
        """
        self.get(url='/ec/customer/login/token/generate')
        auth = jmespath(self.response, "object.token")
        log.info(f'匿名auth:{auth}')
        driver_header["authorization"] = 'Bearer ' + auth
        return auth

    def driver_login(self, email: str, password: str):
        """
        登录司机账户
        :param email: 司机邮箱
        :param password: 密码
        :return:
        """
        if email is None or password is None:
            raise Exception('登录的email,password不能为空')
        body = {
            "email": email,
            "password": password,
            "mock": False
        }
        self.post(url='/ec/tms/v1/driver/login/email', headers=driver_header, json=body)
        auth = jmespath(self.response, "object.token")
        if auth is not None and len(auth) > 0:
            log.info(f'Driver登录成功,Driver Token:{auth}')
            driver_header["authorization"] = 'Bearer ' + auth
            return self.response
        else:
            raise Exception(f'Driver登录失败,msg:{jmespath(self.response, "message")}')

    def get_driver_info(self, check_result=False):
        """
        获取司机账号信息
        :param check_result: 是否校验结果
        :return:
        """
        self.get(url='/ec/tms/v1/driver/account/info', headers=driver_header)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'获取司机账号信息成功。')
            return self.response
        elif check_result:
            raise Exception(f'获取司机账号信息失败, Error response:{self.response}')
        else:
            log.warning(f'获取司机账号信息失败, Error response:{self.response}')

    def add_feedback(self, content, check_result=False):
        """
        司机提交feedback
        :param content:
        :param check_result:
        :return:
        """
        if content is None:
            raise Exception('content不能为空')
        body = {
            "content": content
        }
        self.post(url='/ec/tms/v1/driver/feedback/add', headers=driver_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'司机提交feedback成功。')
        elif check_result:
            raise Exception(f'司机提交feedback失败, Error response:{self.response}')
        else:
            log.warning(f'司机提交feedback失败, Error response:{self.response}')
        return self.response

    def save_signup(self, signup_type, address_total, delivery_date=utils.current_date(), comment=None, hot_type=None,
                    check_result=False):
        """
        司机提交signup
        :param signup_type: signup类型 fresh,hotdish
        :param address_total: 地址数量 35
        :param delivery_date: 配送日期
        :param comment: 备注
        :param hot_type: 热送类型, All, Dinner, Lunch
        :param check_result:
        :return:
        """
        body = {
            "delivery_date": delivery_date,
            "signup_type": signup_type,
            "address_total": address_total,
            "comment": comment,
            "date": utils.get_time(),
            "hotDishDeliveryType": hot_type
        }
        self.post(url='/ec/tms/v1/driver/signup/save', headers=driver_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'司机提交signup成功。')
        elif check_result:
            raise Exception(f'司机提交signup失败, Error response:{self.response}')
        else:
            log.warning(f'司机提交signup失败, Error response:{self.response}')
        return self.response

    def cancel_signup(self, signup_id, check_result=False):
        """
        司机取消signup
        :param signup_id: signup ID
        :param check_result:
        :return:
        """
        self.put(url=f'/ec/tms/v1/driver/signup/{signup_id}/cancel', headers=driver_header)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'司机取消signup成功。')
        elif check_result:
            raise Exception(f'司机取消signup失败, Error response:{self.response}')
        else:
            log.warning(f'司机取消signup失败, Error response:{self.response}')
        return self.response

    def get_dispatch_list(self, check_result=False):
        """
        获取司机派车单列表
        :param check_result:
        :return:
        """
        self.get(url=f'/ec/tms/v1/dispatch/list', headers=driver_header)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'获取司机派车单列表成功。')
        elif check_result:
            raise Exception(f'获取司机派车单列表失败, Error response:{self.response}')
        else:
            log.warning(f'获取司机派车单列表失败, Error response:{self.response}')
        return self.response

    def get_schedule_list(self, check_result=False):
        """
        获取司机抢单列表
        :param check_result:
        :return:
        """
        self.get(url=f'/ec/tms/v1/dispatchSchedule/getOfferList', headers=driver_header)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'获取司机抢单列表。')
        elif check_result:
            raise Exception(f'获取司机抢单列表失败, Error response:{self.response}')
        else:
            log.warning(f'获取司机抢单列表失败, Error response:{self.response}')
        return self.response

    def schedule_route(self, dispatch_id, fee_id, check_result=False):
        """
        司机抢单
        :param dispatch_id:
        :param fee_id:
        :param check_result:
        :return:
        """
        body = {
            "dispatchId": dispatch_id,
            "feeId": fee_id
        }
        self.post(url=f'/ec/tms/v1/driver/route/schedule', headers=driver_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'司机抢单成功, 派车单ID:{dispatch_id}, 运费ID:{fee_id}。')
        elif check_result:
            raise Exception(f'司机抢单失败, Error response:{self.response}')
        else:
            log.warning(f'司机抢单失败, Error response:{self.response}')
        return self.response

    def unschedule_route(self, dispatch_id, check_result=False):
        """
        司机Drop路线
        :param dispatch_id:
        :param check_result:
        :return:
        """
        body = {
            "dispatchId": dispatch_id,
            "unscheduleCode": "",
            "unscheduleReason": ""
        }
        self.post(url=f'/ec/tms/v1/driver/route/unschedule', headers=driver_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'司机Drop路线成功, 派车单ID:{dispatch_id}')
        elif check_result:
            raise Exception(f'司机Drop路线失败, Error response:{self.response}')
        else:
            log.warning(f'司机Drop路线失败, Error response:{self.response}')
        return self.response

    def rescue_route(self, check_result=False):
        """
        Rescue路线
        :param check_result:
        :return:
        """
        self.get(url=f'/tms/job/refreshFlexStatusToRescue', headers=driver_header)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'Rescue路线请求成功。')
        elif check_result:
            raise Exception(f'Rescue路线请求失败, Error response:{self.response}')
        else:
            log.warning(f'Rescue路线请求失败, Error response:{self.response}')
        return self.response

    def get_dispatch_detail(self, check_result=False):
        """
        获取司机派车单详情
        :param check_result:
        :return:
        """
        self.get(url=f'/ec/tms/v1/dispatch/detail', headers=driver_header)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'获取司机派车单详情成功。')
        elif check_result:
            raise Exception(f'获取司机派车单详情失败, Error response:{self.response}')
        else:
            log.warning(f'获取司机派车单详情失败, Error response:{self.response}')
        return self.response

    def get_package_progress(self, check_result=False):
        """
        获取打包进度
        :param check_result:
        :return:
        """

        self.get(url=f'/ec/tms/v1/task/packageProgressDisplay', headers=driver_header)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'获取打包进度成功。')
        elif check_result:
            raise Exception(f'获取打包进度失败, Error response:{self.response}')
        else:
            log.warning(f'获取打包进度失败, Error response:{self.response}')
        return self.response

    def driver_clock_in(self, latitude, longitude, second_flag=False):
        """
        自营司机的打卡上班
        """
        body = {"latitude": latitude, "longitude": longitude, "checkGeofence": True, "secondFlag": second_flag}
        self.post(url=f'/ec/tms/v1/driver/timeCard/clockIn', headers=driver_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'司机打卡上班成功。')
        else:
            log.warning(f'司机打卡上班失败, Error response:{self.response}')
        return self.response

    def driver_clock_out(self, latitude, longitude, second_flag=False, check_result=False):
        """
        自营司机的打卡下班
        """
        body = {"latitude": latitude, "longitude": longitude, "checkGeofence": True, "secondFlag": second_flag}
        self.post(url=f'/ec/tms/v1/driver/timeCard/clockOut', headers=driver_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'司机打卡下班成功。')
        elif check_result:
            raise Exception(f'司机打卡下班失败, Error response:{self.response}')
        else:
            log.warning(f'司机打卡下班失败, Error response:{self.response}')
        return self.response
    def driver_get_course_list(self):
        self.get(url=f'/ec/tms/safetyTraining/index', headers=driver_header)
        result = jmespath(self.response, "result")
        if result is not None and result:
            log.info("获取课程列表成功")
        else:
            log.warning("获取课程列表失败")
        return self.response
    def driver_get_course_detail(self , id):
        '''
        司机获取课程详情
        '''
        self.get(url=f'/ec/tms/safetyTraining/trainingDetail?trainingTaskId={id}', headers=driver_header)
        result = jmespath(self.response, "object.courseTitle")
        if result is not None and result:
            log.info(f"课程详情获取成功，id: {id}")
        else:
            log.warning(f"课程详情获取失败，id: {id}")
        return self.response
    def update_driver_course_finish(self, courseTaskId, startTime, endTime, durationTime = 1):
        """
        更新司机的课程任务为完成状态
        """
        body = {"courseTaskId": courseTaskId, "startTime": startTime, "endTime": endTime, "durationTime": durationTime}
        self.post(url=f'/ec/tms/safetyTraining/finish', headers=driver_header, json=body)
        result = jmespath(self.response, "result")
        if result is not None and result:
            log.info(f"更新课程状态成功，id: {courseTaskId}")
        else:
            log.warning(f"更新课程状态失败，id: {courseTaskId}")
        return self.response
    def report_start_mileage(self, dispatch_id,start_mileage_distance,check_result=False):
        """
        上送路线开始的里程信息
        """
        body = {"latitude": 37.418024,
                "longitude": -121.886013,
                "checkGeofence": False,
                "secondFlag": False,
                "dispatchId": dispatch_id,
                "startMileageDistance": start_mileage_distance,
                "startMileageUrl": "https://img06.test.weeecdn.com/tms/image/110/811/54DE7258B0B45CAD.jpeg"
                }
        self.post(url=f'/ec/tms/v1/dispatch/reportStartMileage', headers=driver_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'司机上报开始里程成功, 派车单ID:{dispatch_id}')
        elif check_result:
            raise Exception(f'司机上报开始里程失败, Error response:{self.response}')
        else:
            log.warning(f'司机上报开始里程失败, Error response:{self.response}')
    def report_end_mileage(self, dispatch_id,end_mileage_distance,check_result=False):
        """
        上送路线结束的里程信息
        """
        body = {"latitude":37.418024,
                "longitude":-121.886013,
                "checkGeofence":False,
                "secondFlag":False,
                "dispatchId":dispatch_id,
                "endMileageDistance":end_mileage_distance,
                "endMileageUrl":"https://img06.test.weeecdn.com/tms/image/633/107/4F9B889BD23172FA.jpeg"
                }
        self.post(url=f'/ec/tms/v1/dispatch/reportEndMileage', headers=driver_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'司机上报结束里程成功, 派车单ID:{dispatch_id}')
            return self.response
        elif check_result:
            raise Exception(f'司机上报结束里程失败, Error response:{self.response}')
        else:
            log.warning(f'司机上报结束里程失败, Error response:{self.response}')
    def has_unfinished_courses(self,check_result=False):
        """
        获取是否存在未完成课程
        :param check_result:
        :return:
        """
        self.get(url=f'/ec/tms/safetyTraining/hasUnfinishedCourses', headers=driver_header)
        result = jmespath(self.response, "result")
        if result is not None and result:
            log.info(f"获取司机是否有未完成的课程成功: {result}")
            return self.response
        elif check_result:
            raise Exception(f"司机是否有未完成的课程失败, Error response:{self.response}")
        else:
            log.warning(f"司机是否有未完成的课程失败, Error response:{self.response}")
# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  kaichuan.wang
@Version        :  V1.0.0
------------------------------------
@File           :  driver_delivery.py
@Description    :  
@CreateTime     :  2023/8/14 15:08
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/8/14 15:08
"""

import weeeTest
from weeeTest import log
from tms.test_dir.api.tms import driver_header
from weeeTest.utils import jmespath

from tms.test_dir.api_case.tms import utils


class DriverDelivery(weeeTest.TestCase):
    """
    司机配送相关方法封装
    """
    def get_checkin_status(self, dispatch_id=None, check_result=False):
        """
        获取司机pre-tipe步骤和参数
        :param dispatch_id: 派车单ID
        :param check_result:
        :return:
        """
        params = {"dispatchId":dispatch_id}
        self.get(url=f"/ec/tms/v1/driver/checkIn/status", params=params,headers=driver_header)
        result = jmespath(self.response, "result")
        if result:
            log.info('获取司机pre-tipe步骤和参数成功。')
        elif check_result:
            raise Exception(f'获取司机pre-tipe步骤和参数失败, Error response:{self.response}')
        else:
            log.warning(f'获取司机pre-tipe步骤和参数失败, Error response:{self.response}')
        return self.response
    def get_dispatch_detail(self, dispatch_id=None, load_type=None, check_result=False):
        """
        获取派车单详情
        :param dispatch_id: 派车单ID
        :param load_type: 扫描方式 All,Apart
        :param check_result:
        :return: 
        """
        url = '/ec/tms/v1/dispatch/detail'
        if dispatch_id:
            url = url + f'?dispatch_id={dispatch_id}'
        if load_type:
            url = url + f'&load_type={load_type}'
        self.get(url=url, headers=driver_header)
        result = jmespath(self.response, "result")
        if result:
            log.info('获取派车单详情成功。')
        elif check_result:
            raise Exception(f'获取派车单详情失败, Error response:{self.response}')
        else:
            log.warning(f'获取派车单详情失败, Error response:{self.response}')
        return self.response

    def get_inventory_detail(self, task_id, load_type=None, check_result=False):
        """
        获取仓库详情
        :param task_id:
        :param load_type: 扫描方式 All,Apart
        :param check_result: 
        :return:
        """
        url = f'/ec/tms/v1/task/inventory/detail?task_id={task_id}'
        if load_type:
            url = url + f'&load_type={load_type}'
        self.get(url=url, headers=driver_header)
        result = jmespath(self.response, "result")
        if result:
            log.info('获取仓库详情成功。')
        elif check_result:
            raise Exception(f'获取仓库详情失败, Error response:{self.response}')
        else:
            log.warning(f'获取仓库详情失败, Error response:{self.response}')
        return self.response

    def report_risk(self, lat, lon, user_id, task_id, check_result=False):
        """
        司机上报风险地址
        :param lat: 纬度
        :param lon: 经度
        :param user_id: 司机ID
        :param task_id: task ID
        :param check_result: 
        :return:
        """
        body = {
            "exepLatitude": float(lat),
            "inUser": user_id,
            "exepLongitude": float(lon),
            "taskId": task_id
        }
        self.post(url='/tms/address/reportRisk', headers=driver_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'司机上报风险地址成功。')
        elif check_result:
            raise Exception(f'司机上报风险地址失败, Error response:{self.response}')
        else:
            log.warning(f'司机上报风险地址失败, Error response:{self.response}')
        return self.response

    def report_address_note(self, task_id, address_note, gate_code, check_result=False):
        """
        司机添加地址备注
        :param address_note: 地址备注
        :param gate_code: 门禁密码
        :param task_id: task ID
        :param check_result: 
        :return:
        """
        body = {
            "driverAddressNote": address_note,
            "gateCode": gate_code,
            "taskId": task_id
        }
        self.post(url='/ec/tms/v1/task/editGatecodeAndComment', headers=driver_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'司机添加地址备注成功。')
        elif check_result:
            raise Exception(f'司机添加地址备注失败, Error response:{self.response}')
        else:
            log.warning(f'司机添加地址备注失败, Error response:{self.response}')
        return self.response

    def update_task(self, task_id, task_type, task_status, task_step, latitude, longitude, dispatch_id=None,
                    delivered_photo=None, note=None, failure_note=None, cancel_alcohol=None, check_result=False):
        """
        更新task状态
        :param task_id: task ID
        :param task_type: task类型,inventory,stop
        :param task_status:状态数值,30,60,70
        :param task_step: 状态描述 finish,failure,partially_finish
        :param latitude: 纬度
        :param longitude: 经度
        :param dispatch_id: 派车单ID
        :param delivered_photo: 配送照片
        :param note: 备注
        :param failure_note: 失败备注
        :param cancel_alcohol: 是否取消酒类 True/False
        :param check_result: 
        :return:
        """
        body = {
            "dispatch_id": dispatch_id,
            "task_id": task_id,
            "task_type": task_type,
            "task_status": task_status,
            "task_step": task_step,
            "delivered_photo": delivered_photo,
            'cancel_alcohol': cancel_alcohol,
            'note': note,
            'failure_note': failure_note,
            "latitude": float(latitude),
            "longitude": float(longitude),
            "update_dtm": utils.get_time()
        }
        self.put(url=f'/ec/tms/v1/task/update', headers=driver_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f"更新{task_type},task_id:{task_id}状态为{task_step}成功！")
        elif check_result:
            raise Exception(
                f"更新{task_type},task_id:{task_id}状态为{task_step}失败失败, Error response:{self.response}")
        else:
            log.warning(f"更新{task_type},task_id:{task_id}状态为{task_step}失败失败, Error response:{self.response}")
        return self.response
    def update_drop_off_location(self,task_id,dispatch_id,drop_off_location,check_result=False):
        """
        更新drop_off_location
        :param task_id: task ID
        :param dispatch_id: 派车单ID
        :param drop_off_location: 卸货地址
        :param check_result:
        :return:
        """
        body = {
            "task_id": task_id,
            "dispatch_id": dispatch_id,
            "drop_off_location": drop_off_location,
            "task_type": "stop",
            "signature_type": "Alcohol",
            "update_dtm": 1742368150
        }
        self.put(url=f'/ec/tms/v1/task/update', headers=driver_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f"更新包裹位置成功！")
        elif check_result:
            raise Exception(f"更新包裹位置失败, Error response:{self.response}")
        else:
            log.warning(f"更新包裹位置失败, Error response:{self.response}")
        return self.response
    def skip_package(self, task_id, storage_type, load_type, supervisor_pwd, latitude, longitude, check_result=False):
        """
        跳过包裹扫描
        :param task_id: Task ID
        :param storage_type: 包裹类型 F,D,Z
        :param load_type: 扫描类型,部分Apart,全部All
        :param supervisor_pwd: 超级密码
        :param latitude:
        :param longitude:
        :param check_result: 
        :return:
        """
        body = {
            "task_id": task_id,
            "storage_type": storage_type,
            "load_type": load_type,
            "supervisor_pwd": supervisor_pwd,
            "latitude": float(latitude),
            "longitude": float(longitude)
        }
        self.put(url=f'/ec/tms/v1/package/skip', headers=driver_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f"跳过{storage_type}类型包裹扫描成功！")
        elif check_result:
            raise Exception(f"跳过{storage_type}类型包裹扫描失败失败, Error response:{self.response}")
        else:
            log.warning(f"跳过{storage_type}类型包裹扫描失败失败, Error response:{self.response}")
        return self.response

    def load_package(self, task_id, scan_type, storage_type, latitude, longitude, is_fast_load_skip=True,
                     tracking_num_list=None, check_result=False):
        """
        扫描包裹
        :param task_id: Task ID
        :param scan_type: 扫描类型,1 fastload扫描,0逐个扫描
        :param storage_type: 包裹类型 F,D,Z
        :param is_fast_load_skip: 是否快速扫描
        :param tracking_num_list: 包裹码列表
        :param latitude:
        :param longitude:
        :param check_result: 
        :return:
        """
        body = {
            "type": scan_type,
            "isFastLoadSkip": is_fast_load_skip,
            "requestStatus": "loaded",
            "task_id": task_id,
            "storage_type": storage_type,
            'trackingNumList': tracking_num_list,
            "latitude": float(latitude),
            "longitude": float(longitude)
        }
        self.put(url=f'/ec/tms/v1/package/load', headers=driver_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f"扫描{storage_type}类型包裹成功！")
        elif check_result:
            raise Exception(f"扫描{storage_type}类型包裹失败失败, Error response:{self.response}")
        else:
            log.warning(f"扫描{storage_type}类型包裹失败失败, Error response:{self.response}")
        return self.response

    def translate_order_comment(self, task_ref_id, check_result=False):
        """
        翻译订单备注
        :param task_ref_id:
        :param check_result: 
        :return:
        """
        body = {
            "task_ref_id": task_ref_id
        }
        self.put(url='/ec/tms/v1/stop/order_comment/translate', headers=driver_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'翻译订单备注成功。')
        elif check_result:
            raise Exception(f'翻译订单备注失败, Error response:{self.response}')
        else:
            log.warning(f'翻译订单备注失败, Error response:{self.response}')
        return self.response

    def update_task_photo(self, task_id, latitude, longitude, check_result=False):
        """
        上传站点图片
        :param task_id:
        :param latitude:
        :param longitude:
        :param check_result: 
        :return:
        """
        body = {
            "task_id": task_id,
            "delivered_photo": "https://cdn.sayweee.net/tms/51bec3c41e87eb5bfaea6a5c4e4d7d48_0x0.jpeg",
            "latitude": float(latitude),
            "longitude": float(longitude),
            "update_dtm": utils.get_time(),
        }
        self.put(url='/ec/tms/v1/task/update_photo', headers=driver_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'上传站点图片成功。')
        elif check_result:
            raise Exception(f'上传站点图片失败, Error response:{self.response}')
        else:
            log.warning(f'上传站点图片失败, Error response:{self.response}')
        return self.response

    def finish_pickup_task(self, task_id, invoice_id=None, check_result=False):
        """
        完成取货任务
        :param task_id:
        :param invoice_id:
        :param check_result: 
        :return:
        """
        self.get(url=f'tms/v1/dispatch/pickUpFinis?task_id={task_id}&invoice_id={invoice_id}', headers=driver_header)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'完成取货任务成功。')
        elif check_result:
            raise Exception(f'完成取货任务失败, Error response:{self.response}')
        else:
            log.warning(f'完成取货任务失败, Error response:{self.response}')
        return self.response

    def finish_dispatch(self, dispatch_id, dispatch_status, check_result=False):
        """
        完成派车单
        :param dispatch_id: 派车单ID
        :param dispatch_status: 派车单状态 30完成
        :param check_result: 
        :return:
        """
        body = {
            "dispatch_id": dispatch_id,
            "dispatch_status": dispatch_status,
            "latitude": 34.1888894,
            "longitude": -108.8793095,
            "update_dtm": utils.get_time()
        }
        self.put(url=f'/ec/tms/v1/dispatch/update', headers=driver_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f"关闭派车单,派车单ID:{dispatch_id}配送完成！")
        elif check_result:
            raise Exception(f"派车单ID:{dispatch_id}完成失败失败, Error response:{self.response}")
        else:
            log.warning(f"派车单ID:{dispatch_id}完成失败失败, Error response:{self.response}")
        return self.response

    def report_dispatch_track(self, dispatch_id, latitude, longitude, check_result=False):
        """
        司机轨迹上报
        :param dispatch_id:
        :param latitude:
        :param longitude:
        :param check_result: 
        :return:
        """
        body = {
            "dispatchId": dispatch_id,
            "latitude": float(latitude),
            "longitude": float(longitude),
            "inDtm": utils.get_time()
        }
        self.post(url=f'/ec/tms/driverDispatchTrack/save', headers=driver_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'司机轨迹上报成功。')
        elif check_result:
            raise Exception(f'司机轨迹上报失败, Error response:{self.response}')
        else:
            log.warning(f'司机轨迹上报失败, Error response:{self.response}')
        return self.response

    def check_package_arrived(self, task_id, tracking_num, package_status, latitude, longitude, package_remarks=None,
                              check_result=False):
        """
        校验包裹送达
        :param tracking_num: 包裹码
        :param task_id: 站点ID
        :param package_remarks: 包裹备注
        :param package_status: 包裹状态
        :param latitude: 纬度
        :param longitude: 经度
        :param check_result: 
        :return:
        """
        body = {
            "latitude": float(latitude),
            "longitude": float(longitude),
            "package_remarks": package_remarks,
            "package_status": package_status,
            "task_id": task_id,
            "tracking_num": tracking_num,
            "update_dtm": utils.get_time()
        }
        response = self.put(url=f'/ec/tms/v1/package/check_arrived', headers=driver_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f"更新站点包裹:{tracking_num}状态成功！")
        elif check_result:
            raise Exception(f"更新站点包裹{tracking_num}状态失败失败, Error response:{self.response}")
        else:
            log.warning(f"更新站点包裹{tracking_num}状态失败失败, Error response:{self.response}")
        return response
    def get_assign_park_info(self,dispatch_id,check_result=False):
        """
        获取后台assign车位
        :param dispatch_id:
        :param check_result:
        :return:
        """
        self.get(url=f'/ec/tms/yard/parking/assignedParkSlot', headers=driver_header, params={"dispatchId":dispatch_id})
        result = jmespath(self.response, "result")
        if result:
            log.info(f'获取后台assign车位成功。')
        elif check_result:
            raise Exception(f'获取后台assign车位失败, Error response:{self.response}')
        else:
            log.warning(f'获取后台assign车位失败, Error response:{self.response}')
        return self.response
    def get_free_park_info(self,dispatch_id,check_result=False):
        """
        获取free车位
        :param dispatch_id:
        :param check_result:
        :return:
        """
        self.get(url=f'/ec/tms/yard/parking/freeParkSlots', headers=driver_header, params={"dispatchId":dispatch_id})
        result = jmespath(self.response, "result")
        if result:
            log.info(f'获取可用车位成功。')
        elif check_result:
            raise Exception(f'获取可用车位失败, Error response:{self.response}')
        else:
            log.warning(f'获取可用车位失败, Error response:{self.response}')
        return self.response
    def park_slot(self,dispatch_id,slot_id,latitude,longitude,check_result=False):
        """
        确认停车位
        :param dispatch_id:
        :param slot_id:
        :param check_result:
        :param latitude:
        :param longitude:
        :return:
        """
        body = {
            "dispatchId": dispatch_id,
            "slotId": slot_id,
            "latitude":latitude,
            "longitude":longitude
        }
        self.post(url=f'/ec/tms/yard/parking/parkSlot', headers=driver_header, json=body)
        result = jmespath(self.response, "result")
        if result:
            log.info(f'确认停车位成功。')
        elif check_result:
            raise Exception(f'确认停车位失败, Error response:{self.response}')
        else:
            log.warning(f'确认停车位失败, Error response:{self.response}')
        return self.response
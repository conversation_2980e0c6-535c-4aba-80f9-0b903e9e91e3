# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  kaichuan.wang
@Version        :  V1.0.0
------------------------------------
@File           :  __init__.py
@Description    :
@CreateTime     :  2023/6/9 15:27
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/6/9 15:27
"""
import datetime
import time

import jsonpath
import pytz
from weeeTest import log
from datetime import timedelta


def current_date():
    """
    获取当前日期
    """
    return str(datetime.date.today())


def get_date(days=2):
    """
    获取days天之后的日期
    :param days: 天数, 为负数时返回当前日期之前几天, 正数为之后几天
    :return:
    """
    days_later = datetime.date.today() + datetime.timedelta(days=days)
    return str(days_later)


def current_time():
    """
    获取当前时间
    """
    return time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())


def us_current_date(zone='US/Pacific'):
    """
    获取美国当前日期
    :param zone: US/Eastern: 东海岸; US/Pacific: 西海岸
    :return:
    """
    us_eastern = pytz.timezone(zone)
    current_us_date = datetime.datetime.now(us_eastern).date()
    return current_us_date


def get_time():
    """
    获取时间戳（秒）
    """
    return int(time.time())


def get_time_stamp(delivery_date):
    """
    获取指定日期的时间戳
    :param delivery_date: 2024-09-10
    :return:
    """
    date_obj = datetime.date.fromisoformat(delivery_date)
    timestamp = datetime.datetime(date_obj.year, date_obj.month, date_obj.day).timestamp()
    log.info(f"日期:{delivery_date}的时间戳为{timestamp}")
    return timestamp


def calculate_time(time_interval=15, time_format='%H:%M:%S'):
    """
    默认返回当前时间前后time_interval的时间点
    :param time_interval: 时间间隔 单位: min
    :param time_format: 时间格式 '%Y-%m-%d %H:%M:%S'
    :return:
    """
    # 获取当前系统时间
    now = datetime.datetime.now(pytz.utc)  # 获取UTC时间，因为datetime.now()默认是系统本地时间
    # 设定目标时区为太平洋时区（美国/洛杉矶）
    pacific_tz = pytz.timezone('America/Los_Angeles')
    # 将UTC时间转换为太平洋时区时间
    now_pacific = now.astimezone(pacific_tz)
    # 格式化时间到秒
    formatted_now = now_pacific.strftime(f'{time_format}')
    # 计算 2 * time_interval分钟之前的时间
    time_interval_minutes2_ago_pacific = now_pacific - datetime.timedelta(minutes=2 * time_interval)
    formatted_time_interval_minutes2_ago = time_interval_minutes2_ago_pacific.strftime(f'{time_format}')
    # 计算time_interval分钟之前的时间
    time_interval_minutes_ago_pacific = now_pacific - datetime.timedelta(minutes=time_interval)
    formatted_time_interval_minutes_ago = time_interval_minutes_ago_pacific.strftime(f'{time_format}')
    # 计算time_interval分钟之后的时间
    time_interval_minutes_later_pacific = now_pacific + datetime.timedelta(minutes=time_interval)
    formatted_time_interval_minutes_later = time_interval_minutes_later_pacific.strftime(f'{time_format}')
    # 计算 2 * time_interval分钟之后的时间
    time_interval_minutes2_later_pacific = now_pacific + datetime.timedelta(minutes=2 * time_interval)
    formatted_time_interval_minutes2_later = time_interval_minutes2_later_pacific.strftime(f'{time_format}')
    log.info(
        f'两倍时间区间之前:{formatted_time_interval_minutes2_ago}, 时间区间之前的时间:{formatted_time_interval_minutes_ago}, '
        f'当前时间:{formatted_now}, 时间区间之后的时间:{formatted_time_interval_minutes_later}, 2倍时间区间之后:{formatted_time_interval_minutes2_later}')
    return formatted_time_interval_minutes2_ago, formatted_time_interval_minutes_ago, formatted_now, formatted_time_interval_minutes_later, formatted_time_interval_minutes2_later


def wait(sleep_time=1, reason=None):
    """
    等待时间
    :param sleep_time: 等待时间,单位:秒
    :param reason: 等待原因
    :return:
    """
    if reason:
        log.info(f"等待{sleep_time}秒, 原因为:{reason}")
    else:
        log.info(f"等待{sleep_time}秒.")
    time.sleep(sleep_time)


def print_log(message, level='info'):
    """
    脚本中打印log
    :param message: 日志内容
    :param level: 日志等级
    :return:
    """
    if level == 'error':
        log.error(message)
    else:
        log.info(message)


def get_response_values(response, *args):
    """
    使用jsonpath方式获取响应值中的对应参数值
    @param response: 响应体
    @param args: 参数列表,获取的参数有多个相同参数名时,可用 value[index]选择指定参数值,'[?(@.stop_type =="stop"&&@.seq ==2)].stop_id'
    @return:
    """
    log.info(f"传入的response值为:{response}")
    response_value = []
    try:
        for value in args:
            if "[" in value and "?" not in value:
                re_value = value.split("[")[0]
                index = int(value.split("[")[-1].split(']')[0])
                json_values = jsonpath.jsonpath(response, f"$..{re_value}")[index]
            elif "?" in value:
                if '/' in value:
                    re_value = value.split('/')[0]
                    index = int(value.split("/")[-1])
                    json_values = jsonpath.jsonpath(response, f"$..{re_value}")[index]
                else:
                    json_values = jsonpath.jsonpath(response, f"$..{value}")[0]
            else:
                json_values = jsonpath.jsonpath(response, f"$..{value}")
                if len(json_values) == 1:
                    json_values = json_values[0]
            log.info(f"{value}取值为:{json_values}")
            response_value.append(json_values)  # 获取接口返回信息中的变量
        log.info(f"{args}最终取值为:{response_value}")
        return response_value
    except Exception:
        raise Exception(f'Response解析失败, 值为{response}')


def get_json_values(json_str, key):
    """
    使用get_key方式获取响应值中的对应参数值
    @param json_str: 响应体
    @param key:
    @return:
    """
    value = None
    if isinstance(json_str, dict):
        if '.' in key:
            for k in key.split('.'):
                if '[' in k:
                    j = k.split('[')
                    json_str = json_str.get(j[0])[int(j[1].split(']')[0])]
                else:
                    json_str = json_str.get(k)
                    value = json_str
        else:
            value = json_str.get(key)
        log.info(f"key值为:{value}")
    else:
        log.error(f"{json_str}不是一个dict!")
    return value


def get_package_list(package_json):
    """
    获取包裹列表
    @param package_json: 包裹信息json
    @return:
    """
    tracking_info = []
    tracking_num = []
    if isinstance(package_json, dict):
        print("1")
        for storage_list in package_json:
            stop_list = storage_list.get('stop_list')
            for stop_package in stop_list:
                package_list = stop_package.get('package_list')
                for tracking_nums in package_list:
                    tracking_num.append(tracking_nums.get('tracking_num') + '_' + tracking_nums.get('storage_type'))
                tracking_info.append(tracking_num)
                print(tracking_info)
    print("0")
    return tracking_info


def get_driver_nums(json_str, index=0):
    """
    获取司机数量
    :param json_str:
    :param index:
    :return:
    """
    driver_num = {}
    driver_num_info = get_json_values(json_str, f'object[{index}].detailInfos')
    for driver_info in driver_num_info:
        if driver_info['driverType'] == 'F':
            driver_num['w2_num'] = driver_info['driverNum']
            driver_num['w2_stop'] = driver_info['driverStopNum']
        elif driver_info['driverType'] == 'P':
            driver_num['ic_num'] = driver_info['driverNum']
            driver_num['ic_stop'] = driver_info['driverStopNum']
        elif driver_info['driverType'] == 'D':
            driver_num['df_num'] = driver_info['driverNum']
            driver_num['df_stop'] = driver_info['driverStopNum']
        elif driver_info['driverTypeDesc'] == 'FlexSuv':
            driver_num['suv_num'] = driver_info['driverNum']
            driver_num['suv_stop'] = driver_info['driverStopNum']
        elif driver_info['driverTypeDesc'] == 'FlexMiniVan':
            driver_num['mini_van_num'] = driver_info['driverNum']
            driver_num['mini_van_stop'] = driver_info['driverStopNum']
    return driver_num


def get_driver_nums_2(json_str):
    """
    获取司机数量-双Region
    :param json_str:
    :return:
    """
    driver_num = {}
    for driver_info in json_str:
        if driver_info['driverType'] == 'F':
            driver_num['w2_num'] = driver_info['driverNum']
            driver_num['w2_stop'] = driver_info['driverStopNum']
        elif driver_info['driverType'] == 'P':
            driver_num['ic_num'] = driver_info['driverNum']
            driver_num['ic_stop'] = driver_info['driverStopNum']
        elif driver_info['driverType'] == 'D':
            driver_num['df_num'] = driver_info['driverNum']
            driver_num['df_stop'] = driver_info['driverStopNum']
    return driver_num


def get_tracking_num_info(packages):
    """
    获取各站点包裹信息
    :param packages:
    :return:
    """
    grouped_data = {}
    for item in packages:
        id_ = item[0]
        if id_ not in grouped_data:
            grouped_data[id_] = []
        grouped_data[id_].append(item)
    # 将字典转换为列表
    package_info = list(grouped_data.values())
    return package_info


def get_index_by_value(list_value, value):
    """
    获取列表中指定元素值的下标
    :param list_value: 列表
    :param value: 元素值
    :return:
    """
    index = [i for i, j in enumerate(list_value) if j == value]
    return index


# 获取指定时区的时间，offset代表偏移多少分钟，往前用负数
def get_timezone_time(timezone, offset_hours=0, offset_minutes=0):
    your_time = datetime.datetime.now(pytz.timezone(timezone)) + timedelta(hours=offset_hours) + timedelta(
        minutes=offset_minutes)
    return your_time.strftime('%H:%M:%S')


def get_timezone_date(timezone, offset_days=0):
    your_date = datetime.datetime.now(pytz.timezone(timezone)) + timedelta(days=offset_days)
    return your_date.strftime('%Y-%m-%d')


def get_timezone_datetime(timezone, offset_days=0, offset_hours=0, offset_minutes=0):
    your_datetime = datetime.datetime.now(pytz.timezone(timezone)) + timedelta(days=offset_days) + timedelta(
        hours=offset_hours) + timedelta(minutes=offset_minutes)
    return your_datetime.strftime('%Y-%m-%d %H:%M:%S')


if __name__ == '__main__':
    wait(sleep_time=30, reason='等待路线数据异步计算完成')

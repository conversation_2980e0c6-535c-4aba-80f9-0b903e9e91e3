# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  Author
@Version        :  V1.0.0
------------------------------------
@File           :  test_fleet_management.py
@Description    :  
@CreateTime     :  2024/12/13 11:41
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2024/12/13 11:41
"""
import weeeTest
import allure
from tms.test_dir.api.tms.tms import tms


class TestFleetManage(weeeTest.TestCase):
    """
    车队管理测试
    """

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'FleetManage')
    @allure.title("更新车辆信息")
    def test_edit_vehicle_info(self):
        """
        更新车辆信息
        """
        # 账号登录
        tms.login()
        #  获取车辆列表
        vehicle_list_res = tms.central.fleet.get_vehicle_list(region_id=1, delivery_station=1, check_result=True)
        vehicle_data = tms.util.get_response_values(vehicle_list_res, 'data')[0]
        # 获取某个车辆具体信息
        tms.central.fleet.get_vehicle_info(vehicle_id=vehicle_data[0]["id"], check_result=True)
        vehicle_info = tms.tms_db.get_vehicle_info(vehicle_data[0]["id"])
        # 更新车辆信息
        tms.central.fleet.update_vehicle_info(*vehicle_info, check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'FleetManage')
    @allure.title("添加车辆信息")
    def test_add_vehicle_info(self):
        """
        添加车辆信息
        """
        # 账号登录
        tms.login()
        #  获取车辆列表
        vehicle_list_res = tms.central.fleet.get_vehicle_list(region_id=1, delivery_station=1, check_result=True)
        vehicle_data = tms.util.get_response_values(vehicle_list_res, 'data')[0]
        vehicle_info = tms.tms_db.get_vehicle_info(vehicle_data[0]["id"])
        #  删除车辆数据
        tms.tms_db.del_vehicle(vehicle_data[0]["id"])
        # 添加车辆信息
        add_vehicle_res = tms.central.fleet.add_vehicle(delivery_station_id=vehicle_info[1],
                                                        current_mileage=vehicle_info[2],
                                                        acquisition=vehicle_info[3], license_plate=vehicle_info[4],
                                                        vehicle_number=vehicle_info[5], vehicle_type_id=vehicle_info[6],
                                                        vehicle_vin=vehicle_info[7], check_result=True)
        add_vehicle_id = tms.util.get_response_values(add_vehicle_res, 'id')[0]
        #  根据新增接口返回的的车辆ID获取相关信息
        add_vehicle_info = tms.tms_db.get_vehicle_info(add_vehicle_id)
        #  校验表中的vehicle_vin与添加的值一致
        assert vehicle_info[7] == add_vehicle_info[7]

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'FleetManage')
    @allure.title("删除及添加车辆类型")
    def test_del_and_add_vehicle_type(self):
        """
        删除及添加车辆类型
        """
        # 账号登录
        tms.login()
        #  获取车辆类型列表
        vehicle_list_res = tms.central.fleet.get_vehicle_type_list(vehicle_make='Ford Test', check_result=True)
        vehicle_type_data = tms.util.get_response_values(vehicle_list_res, 'data')[0]
        vehicle_type_info = tms.tms_db.get_vehicle_type_info(vehicle_type_data[0]["id"])
        #  删除车辆数据
        tms.central.fleet.del_vehicle_type(vehicle_type_data[0]["id"])
        # 添加车辆信息
        add_vehicle_type_res = tms.central.fleet.add_vehicle_type(vehicle_year=vehicle_type_info[1],
                                                                  vehicle_make=vehicle_type_info[2],
                                                                  vehicle_model=vehicle_type_info[3],
                                                                  vehicle_capacity=vehicle_type_info[4],
                                                                  payload=vehicle_type_info[5],
                                                                  mpg_city=vehicle_type_info[6],
                                                                  mpg_highway=vehicle_type_info[7], check_result=True)
        add_vehicle_type_id = tms.util.get_response_values(add_vehicle_type_res, 'id')[0]
        #  根据新增接口返回的的车辆ID获取相关信息
        add_vehicle_type_info = tms.tms_db.get_vehicle_type_info(add_vehicle_type_id)
        #  校验表中的vehicle_make与添加的值一致
        assert vehicle_type_info[2] == add_vehicle_type_info[2]

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'FleetManage')
    @allure.title("获取车辆DVIR历史记录")
    def test_get_vehicle_history(self):
        """
        获取车辆DVIR历史记录
        """
        # 账号登录
        tms.login()
        #  获取车辆列表
        vehicle_list_res = tms.central.fleet.get_vehicle_list(region_id=1, delivery_station=1, check_result=True)
        vehicle_data = tms.util.get_response_values(vehicle_list_res, 'data')[0]
        # 获取某个车辆DVIR历史记录
        tms.central.fleet.get_vehicle_inspection_list(vehicle_id=vehicle_data[0]["id"], check_result=True)


if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True,
                  ext=['-m', 'TMS', "--junitxml=tests/results.xml"])

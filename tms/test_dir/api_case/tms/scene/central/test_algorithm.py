# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  kaichuan.wang
@Version        :  V1.0.0
------------------------------------
@File           :  test_algorithm.py
@Description    :
@CreateTime     :  2023/11/2 11:30
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/11/2 11:30
"""
import weeeTest
import allure

from tms.test_dir.api.tms.tms import tms


class TestAlgorithm(weeeTest.TestCase):
    """
    算法测试
    """

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'Algorithm')
    @allure.title("v10算法测试")
    def test_v10_algorithm(self):
        """
        v10算法测试
        """
        # 获取发货计划
        tms.login()
        delivery_plan_res = tms.central.route_plan.get_delivery_plan_list(delivery_date='2022-12-24',
                                                                          sub_region_ids=[47])
        delivery_plan_id = delivery_plan_res['object'][0]['id']
        # 生成draft
        draft_res = tms.central.route_plan.v10_run_draft(delivery_plan_id)
        draft_id = draft_res['object']['draft_id']
        # 等待算法执行完成
        tms.wait_algorithm_finish(draft_id)
        # 获取配送点信息
        draft_routes_res = tms.central.route_plan.get_draft_routes(draft_id)
        draft_routes_info = draft_routes_res['object']
        draft_points_res = tms.central.route_plan.get_draft_points(draft_id=draft_id,
                                                                   delivery_plan_id=delivery_plan_id,
                                                                   route_nums=draft_routes_info)
        draft_points_info = tms.util.get_response_values(draft_points_res, 'route_rec_id[0]', 'stop_id')
        # 新增路线
        tms.central.route_plan.route_change(route_rec_id=draft_points_info[0],
                                            stop_ids=draft_points_info[1][-5:-1],
                                            draft_id=draft_id, driver_id=7239449)
        tms.check_route_change_result(draft_id)
        # 删除draft
        tms.central.route_plan.delete_draft(draft_id)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'Algorithm')
    @allure.title("v18算法测试")
    def test_v18_algorithm_region_1(self):
        """
        v18算法测试
        """
        tms.login()
        sub_region_id = 1
        delivery_date = '2024-01-30'
        delivery_list_res = tms.central.route_plan.get_delivery_list(sub_region_ids=[sub_region_id],
                                                                     delivery_date=delivery_date)
        delivery_list_info = tms.util.get_response_values(delivery_list_res, 'id', 'deliveryType')
        # 生成Delivery Plan
        if isinstance(delivery_list_info[0], int):
            ids = delivery_list_info[0]
        else:
            ids = ','.join(map(str, delivery_list_info[0]))
        delivery_plan_id = tms.generate_delivery(delivery_ids=ids)
        # 获取排车草稿配置信息
        draft_info_res = tms.central.route_plan.get_pre_draft_info(delivery_plan_id)
        draft_info = tms.util.get_response_values(draft_info_res, f'delivery_area_stop_num_setting')[0]

        # 获取V18算法查询排车司机信息
        driver_res = tms.central.route_plan.get_driver_info_2(delivery_plan_id=delivery_plan_id,
                                                              stop_num_setting=draft_info)
        # 获取算法执行参数
        sub_region_name = tms.tms_db.get_sub_region_name(sub_region_id)
        driver_num_info = tms.util.get_driver_nums(driver_res)
        # 获取config配置
        flex_flag = tms.tms_db.get_flex_config(sub_region_id=sub_region_id)
        # 算法执行
        if flex_flag:
            draft_res = tms.central.route_plan.run_ic_complaince_v18(delivery_plan_id=delivery_plan_id,
                                                                     sub_region_id=sub_region_id,
                                                                     sub_region_name=sub_region_name,
                                                                     mini_van_stop=driver_num_info['mini_van_stop'],
                                                                     mini_van_num=driver_num_info['mini_van_num'],
                                                                     w2_stop=driver_num_info['w2_stop'],
                                                                     w2_num=driver_num_info['w2_num'],
                                                                     df_stop=driver_num_info['df_stop'],
                                                                     df_num=driver_num_info['df_num'],
                                                                     suv_stop=driver_num_info['suv_stop'],
                                                                     suv_num=driver_num_info['suv_num'])
        else:
            draft_res = tms.central.route_plan.run_v18(delivery_plan_id=delivery_plan_id,
                                                       sub_region_id=sub_region_id,
                                                       sub_region_name=sub_region_name,
                                                       ic_stop=driver_num_info['ic_stop'],
                                                       ic_num=driver_num_info['ic_num'],
                                                       w2_stop=driver_num_info['w2_stop'],
                                                       w2_num=driver_num_info['w2_num'],
                                                       df_stop=driver_num_info['df_stop'],
                                                       df_num=driver_num_info['df_num'])
        draft_id = draft_res['object']['draft_id']
        # 等待算法执行完成
        tms.wait_algorithm_finish(draft_id)
        # 1.获取配送点信息
        draft_routes_res = tms.central.route_plan.get_draft_routes(draft_id)
        draft_routes_info = draft_routes_res['object']
        draft_points_res = tms.central.route_plan.get_draft_points(draft_id=draft_id,
                                                                   delivery_plan_id=delivery_plan_id,
                                                                   route_nums=draft_routes_info[:1])
        draft_points_info = tms.util.get_response_values(draft_points_res, 'route_rec_id[0]', 'stop_id')
        # 1.新增路线
        tms.central.route_plan.route_change(route_rec_id=draft_points_info[0],
                                            stop_ids=draft_points_info[1][-5:-1],
                                            draft_id=draft_id, driver_id=7239449)
        tms.check_route_change_result(draft_id)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'Algorithm')
    @allure.title("普通单路线站点调整")
    def test_delivery_route_point_change(self):
        """
        普通单路线站点调整
        """
        tms.login()
        # 查询派车计划草稿列表
        draft_list_res = tms.central.route_plan.get_draft_list(delivery_date='2024-01-30', sub_region_ids=[1],
                                                               delivery_type='delivery', status=30)
        draft_id_info = tms.util.get_response_values(draft_list_res, 'draft_id[0]', 'delivery_plan_id[0]')
        # 获取草稿路线信息
        draft_routes_res = tms.central.route_plan.get_draft_routes(draft_id=draft_id_info[0])
        draft_routes_info = tms.util.get_response_values(draft_routes_res, 'object')
        # 获取配送点信息
        draft_points_res = tms.central.route_plan.get_draft_points(draft_id=draft_id_info[0],
                                                                   delivery_plan_id=draft_id_info[1],
                                                                   route_nums=draft_routes_info[0])
        draft_points_info = tms.util.get_response_values(draft_points_res, 'route_rec_id[0]', 'route_rec_id[0]',
                                                         'stop_id[1]')
        # 修改路线站点
        tms.central.route_plan.route_change(route_rec_id=draft_points_info[0], stop_ids=[draft_points_info[2]],
                                            draft_id=draft_id_info[0], new_route_rec_id=draft_points_info[1])
        # 排车草稿信息查询
        tms.central.route_plan.get_pre_draft_info(delivery_plan_id=draft_id_info[1], check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'Algorithm')
    @allure.title("普通单路线司机调整")
    def test_delivery_route_driver_change(self):
        """
        普通单路线司机调整
        """
        tms.login()
        # 查询派车计划草稿列表
        draft_list_res = tms.central.route_plan.get_draft_list(delivery_date='2024-01-30', sub_region_ids=[1],
                                                               delivery_type='delivery', status=30)
        draft_id_info = tms.util.get_response_values(draft_list_res, 'draft_id[0]', 'delivery_plan_id[0]')
        # 获取草稿路线信息
        draft_routes_res = tms.central.route_plan.get_draft_routes(draft_id=draft_id_info[0])
        draft_routes_info = tms.util.get_response_values(draft_routes_res, 'object')
        # 获取配送点信息
        draft_points_res = tms.central.route_plan.get_draft_points(draft_id=draft_id_info[0],
                                                                   delivery_plan_id=draft_id_info[1],
                                                                   route_nums=draft_routes_info[0])
        draft_points_info = tms.util.get_response_values(draft_points_res, 'route_rec_id[0]', 'route_rec_id[0]',
                                                         'stop_id[1]')
        # 修改路线站点
        tms.central.route_plan.route_change(route_rec_id=draft_points_info[0], stop_ids=[draft_points_info[2]],
                                            draft_id=draft_id_info[0], new_route_rec_id=draft_points_info[1])
        # 排车草稿信息查询
        tms.central.route_plan.get_pre_draft_info(delivery_plan_id=draft_id_info[1], check_result=True)
        # 删除draft
        tms.central.route_plan.delete_draft(draft_id=draft_id_info[0])

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'Algorithm')
    @allure.title("v18算法测试-双Region测试")
    def test_v18_algorithm_region_2(self):
        """
        v18算法测试-双Region测试
        """
        sub_region_id = 18
        sub_region_id_2 = 19
        # 获取派车计划ID
        delivery_plan_res = tms.central.route_plan.get_delivery_plan_list(delivery_date='2022-12-12',
                                                                          sub_region_ids=[sub_region_id,
                                                                                          sub_region_id_2])
        delivery_plan_id = tms.util.get_response_values(delivery_plan_res, 'id[0]')[0]
        # 获取排车草稿配置信息
        draft_info_res = tms.central.route_plan.get_pre_draft_info(delivery_plan_id)
        draft_info = tms.util.get_response_values(draft_info_res, f'delivery_area_stop_num_setting[0]')[0]

        # 获取V18算法查询排车司机信息
        driver_res = tms.central.route_plan.get_driver_info_2(delivery_plan_id=delivery_plan_id,
                                                              stop_num_setting=draft_info)
        # 获取算法执行参数
        stop_info = tms.util.get_response_values(driver_res, 'totalStop')[0]
        driver_num_info = tms.util.get_driver_nums(driver_res)
        driver_num_info_2 = tms.util.get_driver_nums(driver_res, index=1)
        # 算法执行
        draft_res = tms.central.route_plan.run_v18_2(delivery_plan_id=delivery_plan_id,
                                                     sub_region_id=draft_info[0]['sub_region_id'],
                                                     sub_region_name=draft_info[0]['delivery_area'],
                                                     ic_stop=driver_num_info['ic_stop'],
                                                     ic_num=driver_num_info['ic_num'],
                                                     w2_stop=driver_num_info['w2_stop'],
                                                     w2_num=driver_num_info['w2_num'],
                                                     df_stop=driver_num_info['df_stop'],
                                                     df_num=driver_num_info['df_num'],
                                                     sub_region_id_2=draft_info[1]['sub_region_id'],
                                                     sub_region_name_2=draft_info[1]['delivery_area'],
                                                     ic_stop_2=driver_num_info_2['ic_stop'],
                                                     ic_num_2=driver_num_info_2['ic_num'],
                                                     w2_stop_2=driver_num_info_2['w2_stop'],
                                                     w2_num_2=driver_num_info_2['w2_num'],
                                                     df_stop_2=driver_num_info_2['df_stop'],
                                                     df_num_2=driver_num_info_2['df_num'],
                                                     total_stop=stop_info[0],
                                                     total_stop_2=stop_info[4]
                                                     )
        draft_id = draft_res['object']['draft_id']
        # 等待算法执行完成
        tms.wait_algorithm_finish(draft_id)
        # 1.获取配送点信息
        draft_routes_res = tms.central.route_plan.get_draft_routes(draft_id)
        draft_routes_info = draft_routes_res['object']
        draft_points_res = tms.central.route_plan.get_draft_points(draft_id=draft_id,
                                                                   delivery_plan_id=delivery_plan_id,
                                                                   route_nums=draft_routes_info[:1])
        draft_points_info = tms.util.get_response_values(draft_points_res, 'route_rec_id[0]', 'stop_id')
        # 1.新增路线
        tms.central.route_plan.route_change(route_rec_id=draft_points_info[0],
                                            stop_ids=draft_points_info[1][-5:-1],
                                            draft_id=draft_id, driver_id=7239449)
        tms.check_route_change_result(draft_id)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'Algorithm')
    @allure.title("生成派车计划,调用v18算法生成生鲜派车单(芝加哥)")
    def test_chicago_create_delivery_dispatch(self):
        """
        生成派车计划,调用v18算法生成生鲜派车单(芝加哥)
        """
        tms.login()
        delivery_date = '2024-08-23'
        sub_region_ids = [24]  # 芝加哥
        # 重置排车计划相关数据,删除dispatch,draft,delivery plan
        tms.delete_delivery_data(delivery_date=delivery_date, sub_region_ids=sub_region_ids)
        delivery_list_res = tms.central.route_plan.get_delivery_list(sub_region_ids=sub_region_ids,
                                                                     delivery_date=delivery_date)
        delivery_list_info = tms.util.get_response_values(delivery_list_res, 'id[0]', 'deliveryType[0]')
        # 生成Delivery Plan
        delivery_plan_id = tms.generate_delivery(delivery_ids=delivery_list_info[0])
        # 获取排车草稿配置信息
        draft_info_res = tms.central.route_plan.get_pre_draft_info(delivery_plan_id)
        draft_info = tms.util.get_response_values(draft_info_res, f'delivery_area_stop_num_setting[-1]')[0]
        # 获取V18算法查询排车司机信息
        driver_res = tms.central.route_plan.get_driver_info_2(delivery_plan_id=delivery_plan_id,
                                                              stop_num_setting=draft_info)
        # 获取算法执行参数
        sub_region_name = tms.tms_db.get_sub_region_name(sub_region_ids[0])
        driver_num_info = tms.util.get_driver_nums(driver_res)
        # 算法执行
        draft_res = tms.central.route_plan.run_v18(delivery_plan_id=delivery_plan_id,
                                                   sub_region_id=sub_region_ids[0],
                                                   sub_region_name=sub_region_name,
                                                   ic_stop=driver_num_info['ic_stop'],
                                                   ic_num=driver_num_info['ic_num'],
                                                   w2_stop=driver_num_info['w2_stop'],
                                                   w2_num=driver_num_info['w2_num'],
                                                   df_stop=driver_num_info['df_stop'],
                                                   df_num=driver_num_info['df_num'])
        draft_id = tms.util.get_response_values(draft_res, 'draft_id[0]')[0]
        # 等待算法执行完成
        tms.wait_algorithm_finish(draft_id)
        # 应用派车单
        tms.create_dispatch_check(draft_id)
        # 校验可正常生成派车单列表
        dispatch_list_res = tms.central.route_plan.get_delivery_plan_dispatch_list(delivery_plan_id=delivery_plan_id)
        dispatch_list_info = tms.util.get_response_values(dispatch_list_res, 'dispatchList')
        assert len(dispatch_list_info) > 0

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'Algorithm')
    @allure.title("生成派车计划,调用v18算法生成生鲜派车单(休斯敦)")
    def test_houston_create_delivery_dispatch(self):
        """
        生成派车计划,调用v18算法生成生鲜派车单(休斯敦)
        """
        tms.login()
        delivery_date = '2024-06-26'
        sub_region_ids = [20]  # 休斯敦
        # 重置排车计划相关数据,删除dispatch,draft,delivery plan
        tms.delete_delivery_data(delivery_date=delivery_date, sub_region_ids=sub_region_ids)
        delivery_list_res = tms.central.route_plan.get_delivery_list(sub_region_ids=sub_region_ids,
                                                                     delivery_date=delivery_date)
        delivery_list_info = tms.util.get_response_values(delivery_list_res, 'id[0]', 'deliveryType[0]')
        # 生成Delivery Plan
        delivery_plan_id = tms.generate_delivery(delivery_ids=delivery_list_info[0])
        # 获取排车草稿配置信息
        draft_info_res = tms.central.route_plan.get_pre_draft_info(delivery_plan_id)
        draft_info = tms.util.get_response_values(draft_info_res, f'delivery_area_stop_num_setting[-1]')[0]
        # 获取V18算法查询排车司机信息
        driver_res = tms.central.route_plan.get_driver_info_2(delivery_plan_id=delivery_plan_id,
                                                              stop_num_setting=draft_info)
        # 获取算法执行参数
        sub_region_name = tms.tms_db.get_sub_region_name(sub_region_ids[0])
        driver_num_info = tms.util.get_driver_nums(driver_res)
        # 算法执行
        draft_res = tms.central.route_plan.run_v18(delivery_plan_id=delivery_plan_id,
                                                   sub_region_id=sub_region_ids[0],
                                                   sub_region_name=sub_region_name,
                                                   ic_stop=driver_num_info['ic_stop'],
                                                   ic_num=driver_num_info['ic_num'],
                                                   w2_stop=driver_num_info['w2_stop'],
                                                   w2_num=driver_num_info['w2_num'],
                                                   df_stop=driver_num_info['df_stop'],
                                                   df_num=driver_num_info['df_num'])
        draft_id = tms.util.get_response_values(draft_res, 'draft_id[0]')[0]
        # 等待算法执行完成
        tms.wait_algorithm_finish(draft_id)
        # 应用派车单
        tms.create_dispatch_check(draft_id)
        # 校验可正常生成派车单列表
        dispatch_list_res = tms.central.route_plan.get_delivery_plan_dispatch_list(delivery_plan_id=delivery_plan_id)
        dispatch_list_info = tms.util.get_response_values(dispatch_list_res, 'dispatchList')
        assert len(dispatch_list_info) > 0

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'Algorithm')
    @allure.title("v18算法IC Complaince业务测试")
    def test_v18_ic_complaince_new_route(self):
        """
        v18算法IC Complaince业务测试
        """
        # 获取发货计划
        tms.login()
        delivery_date = '2022-12-24'
        sub_region_ids = [47]
        flex_cap = 12
        mini_van_cap = 48
        tms.tms_db.update_flex_cap(suv_cap=flex_cap, minivan_cap=mini_van_cap, sub_region_id=sub_region_ids[0])
        # 重置已应用的Draft
        tms.reset_deployed_draft(delivery_date, sub_region_ids)
        # 获取Delivery Plan ID
        delivery_plan_res = tms.central.route_plan.get_delivery_plan_list(delivery_date=delivery_date,
                                                                          sub_region_ids=sub_region_ids)
        delivery_plan_id = tms.util.get_response_values(delivery_plan_res, 'id[0]')[0]
        # 获取排车草稿配置信息
        draft_info_res = tms.central.route_plan.get_pre_draft_info(delivery_plan_id)
        draft_info = tms.util.get_response_values(draft_info_res, f'delivery_area_stop_num_setting[0]')[0][6]
        driver_res = tms.central.route_plan.get_driver_info_2(delivery_plan_id=delivery_plan_id,
                                                              stop_num_setting=draft_info)
        # 获取算法执行参数
        sub_region_name = tms.tms_db.get_sub_region_name(sub_region_ids[0])
        driver_num_info = tms.util.get_driver_nums(driver_res)
        # 算法执行
        draft_id = tms.central.route_plan.run_ic_complaince_v18(delivery_plan_id=delivery_plan_id,
                                                                sub_region_id=sub_region_ids[0],
                                                                sub_region_name=sub_region_name,
                                                                mini_van_stop=driver_num_info['mini_van_stop'],
                                                                mini_van_num=driver_num_info['mini_van_num'],
                                                                w2_stop=driver_num_info['w2_stop'],
                                                                w2_num=driver_num_info['w2_num'],
                                                                df_stop=driver_num_info['df_stop'],
                                                                df_num=driver_num_info['df_num'],
                                                                suv_stop=driver_num_info['suv_stop'],
                                                                suv_num=driver_num_info['suv_num'])['object'][
            'draft_id']
        # 等待算法执行完成
        tms.wait_algorithm_finish(draft_id)
        # 获取司机列表
        route_drivers_info = tms.central.route_plan.get_route_drivers(delivery_plan_id, draft_id)['object']
        flex_driver_list = [11947692, 12797561]
        route_drivers_list = [driver_info['driver_user_id'] for driver_info in route_drivers_info]
        # 校验司机列表中包含Flex司机
        assert set(flex_driver_list).issubset(set(route_drivers_list))
        # 1.获取配送点信息
        route_info = tms.tms_db.get_route_info(draft_id=draft_id)
        draft_routes_res = tms.central.route_plan.get_draft_routes(draft_id)
        draft_routes_info = tms.util.get_response_values(draft_routes_res, 'object')
        draft_points_res = tms.central.route_plan.get_draft_points(draft_id=draft_id,
                                                                   delivery_plan_id=delivery_plan_id,
                                                                   route_nums=[route_info[1][1]])
        draft_points_info = tms.util.get_response_values(draft_points_res, 'route_rec_id[0]', 'stop_id')
        #  更新Flex司机配送能力防止分配失败
        flex_cap = 4
        mini_van_cap = 12
        tms.tms_db.update_flex_cap(suv_cap=flex_cap, minivan_cap=mini_van_cap, sub_region_id=sub_region_ids[0])
        # 1.新增 FlexSUV路线
        tms.central.route_plan.route_change(route_rec_id=draft_points_info[0],
                                            stop_ids=draft_points_info[1][-int(flex_cap):-1],
                                            draft_id=draft_id, driver_id=11947692)
        tms.check_route_change_result(draft_id)

        # 2.获取配送点信息
        draft_routes_res1 = tms.central.route_plan.get_draft_routes(draft_id)
        draft_routes_info1 = tms.util.get_response_values(draft_routes_res1, 'object')
        assert max(draft_routes_info[0]) + 1 == max(draft_routes_info1[0])

        draft_points_res1 = tms.central.route_plan.get_draft_points(draft_id=draft_id,
                                                                    delivery_plan_id=delivery_plan_id,
                                                                    route_nums=[route_info[0][1]])
        draft_points_info1 = tms.util.get_response_values(draft_points_res1, 'route_rec_id[0]', 'stop_id')

        # 2.新增 Minivan路线
        tms.central.route_plan.route_change(route_rec_id=draft_points_info1[0],
                                            stop_ids=draft_points_info1[1][-int(mini_van_cap):-1],
                                            draft_id=draft_id, driver_id=12797561)
        tms.check_route_change_result(draft_id)
        # 校验路线新增成功
        draft_routes_res2 = tms.central.route_plan.get_draft_routes(draft_id)
        draft_routes_info2 = tms.util.get_response_values(draft_routes_res2, 'object')
        assert max(draft_routes_info1[0]) + 1 == max(draft_routes_info2[0])
        #  应用派车单前不拆分路线
        create_res = tms.central.route_plan.create_dispatch(draft_id)
        assert create_res['message'] == 'exist not split route'
        # 等待路线数据异步计算完成
        tms.util.wait(sleep_time=30, reason='需要等待路线数据异步计算完成')
        #  拆分路线再次应用
        tms.central.route_plan.split_flex_route(draft_id, check_result=True)
        tms.create_dispatch_check(draft_id)
        dispatch_list = tms.central.route_plan.get_delivery_plan_dispatch_list(delivery_plan_id)['object'][
            'dispatchList']
        tms.util.print_log(f'dispatch_list:{dispatch_list}')
        assert dispatch_list


if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True,
                  ext=['-m', 'TMS', "--junitxml=tests/results.xml"])

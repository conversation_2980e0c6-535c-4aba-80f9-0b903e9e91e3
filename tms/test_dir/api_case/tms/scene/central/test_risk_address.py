# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  kaichuan.wang
@Version        :  V1.0.0
------------------------------------
@File           :  test_risk_address.py
@Description    :  
@CreateTime     :  2023/7/10 16:38
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/7/10 16:38
"""
import weeeTest
import allure
from tms.qa_config import global_data
from tms.test_dir.api.tms.tms import tms


class TestRiskAddress(weeeTest.TestCase):
    """
    风险地址测试
    """

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'RiskAddress')
    @allure.title("APP上报错误风险地址测试")
    def test_report_risk_error_address(self):
        """
        APP上报错误风险地址测试
        """
        # 账户登录
        tms.login()
        tms.central.driver_manage.update_drivers(dispatch_id=global_data.dispatch_id,
                                                 driver_id=global_data.driver_user_id,
                                                 driver_name=global_data.driver_user_name)
        # 获取task信息
        task_list_res = tms.tms_db.get_dispatch_task_info(dispatch_id=global_data.dispatch_id,
                                                          info='task_id,user_address,latitude,longitude')[0]
        # 清除地址相关数据
        tms.tms_db.delete_risk_address(address=task_list_res[1])
        # Central端查询
        risk_list_res = tms.central.address.get_risk_list(address=task_list_res[1], risk_type='driverReport',
                                                          status='')
        risk_list_info = tms.util.get_response_values(risk_list_res, 'data')[0]
        # 司机地址上报
        tms.driver.driver_delivery.report_risk(lat=task_list_res[3], lon=task_list_res[2],
                                               user_id=global_data.driver_user_id,
                                               task_id=task_list_res[0])
        tms.util.wait(3)
        # sql查询获取上报的地址数据
        address_list_res = tms.tms_db.get_address(address=task_list_res[1], info='fixed, risk')[0]
        risk_address_list_res = tms.tms_db.get_risk_address(address=task_list_res[1], info='status,audit_user')[0]
        # Central端查询
        risk_list_res_1 = tms.central.address.get_risk_list(address=task_list_res[1], risk_type='driverReport',
                                                            status='')
        risk_list_info_1 = tms.util.get_response_values(risk_list_res_1, 'data')[0]
        # 数据校验
        assert address_list_res[0] == 'N' and address_list_res[1] == 'N'
        assert risk_address_list_res[0] == 'A' and risk_address_list_res[1] is None
        assert len(risk_list_info) == 0 and len(risk_list_info_1) == 1

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'RiskAddress')
    @allure.title("APP上报正确风险地址测试")
    def test_report_risk_address(self):
        """
        APP上报正确风险地址测试
        """
        # 账户登录
        tms.login()
        # 获取task信息
        task_list_res = tms.tms_db.get_dispatch_task_info(dispatch_id=global_data.dispatch_id,
                                                          info='task_id,user_address,latitude,longitude')[1]
        # 清除地址相关数据
        tms.tms_db.delete_risk_address(address=task_list_res[1])
        # Central端查询
        risk_list_res = tms.central.address.get_risk_list(address=task_list_res[1], risk_type='driverReport',
                                                          status='')
        risk_list_info = tms.util.get_response_values(risk_list_res, 'data')[0]
        # 司机地址上报
        tms.driver.driver_delivery.report_risk(lat=task_list_res[2], lon=task_list_res[3],
                                               user_id=global_data.driver_user_id,
                                               task_id=task_list_res[0])
        tms.util.wait(3)
        # sql查询获取上报的地址数据
        address_list_res = tms.tms_db.get_address(address=task_list_res[1], info='fixed, risk')[0]
        risk_address_list_res = tms.tms_db.get_risk_address(address=task_list_res[1])
        # Central端查询
        risk_list_res_1 = tms.central.address.get_risk_list(address=task_list_res[1], risk_type='driverReport',
                                                            status='')
        risk_list_info_1 = tms.util.get_response_values(risk_list_res_1, 'data')[0]
        # 数据校验(tms_address字段状态, 不写入tms_address_risk表, central查询为空)
        assert address_list_res[0] == 'Y' and address_list_res[1] == 'N'
        assert not risk_address_list_res
        assert len(risk_list_info) == 0 and len(risk_list_info_1) == 0

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'RiskAddress')
    @allure.title("司机上报地址备注")
    def test_report_address_note(self):
        """
        司机上报地址备注
        """
        # 账户登录
        tms.login()
        # 获取task_id
        task_list_res = tms.tms_db.get_dispatch_task_info(dispatch_id=global_data.dispatch_id,
                                                          info='task_id,user_address')[0]
        # 上报地址备注
        tms.driver.driver_delivery.report_address_note(task_id=task_list_res[0], address_note='Address Note Report',
                                                       gate_code='123456')
        address_list_res = tms.central.address.get_driver_note_list(address=task_list_res[1])
        address_list_info = tms.util.get_response_values(address_list_res, 'driverComment')[0]
        # 接口返回校验
        assert address_list_info == 'Address Note Report'

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'RiskAddress')
    @allure.title("删除地址备注")
    def test_delete_address_note(self):
        """
        删除地址备注
        """
        tms.login()
        address_list_res = tms.central.address.get_driver_note_list()
        address_list_info = tms.util.get_response_values(address_list_res, 'recId[0]', 'userId[0]', 'address[0]',
                                                         'editUser[0]')
        tms.central.address.delete_address_note(rec_id=address_list_info[0], user_id=address_list_info[1],
                                                address=address_list_info[2], edit_user=7642085)
        # 接口返回校验
        address_list_res = tms.central.address.get_driver_note_list(address=address_list_info[2])
        address_list_info = tms.util.get_response_values(address_list_res, 'data')[0]
        assert len(address_list_info) == 0


if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True, ext=['-m', 'TMS', "--junitxml=tests/results.xml"])

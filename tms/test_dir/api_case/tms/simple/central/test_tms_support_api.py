# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  kaichuan.wang
@Version        :  V1.0.0
------------------------------------
@File           :  test_tms_support_api.py
@Description    :  
@CreateTime     :  2023/8/11 15:42
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/8/11 15:42
"""
import weeeTest
import allure

from tms.qa_config import global_data
from tms.test_dir.api.tms.tms import tms
from tms.test_dir.api_case.tms.utils import us_current_date


class TestTmsSupportApi(weeeTest.TestCase):
    """
    TMS Support菜单相关接口
    """

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'TmsSupportApi')
    @allure.title("删除风险地址")
    def test_delete_risk_address(self):
        """
        删除风险地址
        """
        # 账户登录
        tms.login()
        # 删除风险地址
        tms.central.address.delete_risk_address(risk_id=58276, check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'TMS-Online', 'TmsSupportApi')
    @allure.title("获取风险地址列表")
    def test_get_risk_list(self):
        """
        获取风险地址列表
        """
        tms.login()
        tms.central.address.get_risk_list(risk_type="job", status='A', check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'TmsSupportApi')
    @allure.title("将risk设置为不确定状态")
    def test_uncertain_address(self):
        """
        将risk设置为不确定状态
        """
        tms.login()
        tms.central.address.uncertain_address(rec_id=19397, check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'TMS-Online', 'TmsSupportApi')
    @allure.title("获取地址备注列表")
    def test_get_driver_note_list(self):
        """
        获取地址备注列表
        """
        tms.login()
        tms.central.address.get_driver_note_list(check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'TmsSupportApi')
    @allure.title("编辑司机地址备注")
    def test_edit_driver_address_note(self):
        """
        编辑司机地址备注
        """
        tms.central.address.edit_driver_address_note(rec_id=2, driver_comment='Driver Comment edit', user_id=1186996,
                                                     address='12255 West Avenue 104 QQ SPA, San Antonio, Texas, 78216, United States',
                                                     check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'TMS-Online', 'TmsSupportApi')
    @allure.title("获取地址地图信息")
    def test_get_address_map_info(self):
        """
        获取地址地图信息
        """
        tms.login()
        tms.central.address.get_address_map_info(address='10365 Hite Cir, Elk Grove, California, 95757, United States', check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'TMS-Online', 'TmsSupportApi')
    @allure.title("根据address获取地址信息")
    def test_get_address_by_address(self):
        """
        根据address获取地址信息
        """
        address = ["10 Jones Court, Norwich, Connecticut, 06360, United States",
                   "116 North Primrose Avenue Apt4, Alhambra, California, 91801, United States"]
        tms.central.address.get_address_by_address(addresses=address, check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'TMS-Online', 'TmsSupportApi')
    @allure.title("获取地址历史经纬度集合")
    def test_get_history_address_info(self):
        """
        获取地址历史经纬度集合
        """
        tms.login()
        risk_list_res = tms.central.address.get_risk_list(status='A')
        risk_info = tms.util.get_response_values(risk_list_res, 'recId[0]', 'address[0]')
        tms.central.address.get_history_address_info(rec_id=risk_info[0], address=risk_info[1], check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'TmsSupportApi')
    @allure.title("审批risk地址")
    def test_approve_risk_address(self):
        """
        审批risk地址
        """
        address = '1649 Whispering Wind Drive, Tracy, California, 95377, United States'
        tms.central.address.approve_risk_address(rec_id=58283, address=address, init_lat=37.6988988,
                                                 init_long=-121.4457741, fix_lat=37.6888988, fix_long=-122.4457741,
                                                 status='P', audit_user=7862430, check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'TMS-Online', 'TmsSupportApi')
    @allure.title("获取w2司机运费基础数据")
    def test_get_w2_driver_payment_list(self):
        """
        获取w2司机运费基础数据
        """
        tms.login()
        tms.central.address.get_w2_driver_payment_list(start_date='2023-08-01', end_date='2023-08-14',
                                                       check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'TMS-Online', 'TmsSupportApi')
    @allure.title("获取ic司机运费基础数据")
    def test_get_ic_driver_payment_list(self):
        """
        获取ic司机运费基础数据
        """
        tms.login()
        tms.central.address.get_ic_driver_payment_list(start_date='2023-08-01', end_date='2023-08-14',
                                                       check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'TmsSupportApi')
    @allure.title("更新派车单距离")
    def test_update_dispatch_distance(self):
        """
        更新派车单距离
        """
        tms.login()
        tms.central.update_dispatch_distance(dispatch_id=global_data.dispatch_id, distance=18.5, check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'TmsSupportApi')
    @allure.title("设置草稿的Flex路线为可调整")
    def test_allow_route_modification(self):
        """
        设置草稿的Flex路线为可调整
        """
        tms.central.allow_route_modification(draft_id=13068, flex_unlock=1, check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'TmsSupportApi')
    @allure.title("修改派车单日期")
    def test_change_delivery_date(self):
        """
        修改派车单日期
        """
        delivery_date = str(us_current_date(zone="America/Los_Angeles"))
        dispatch_id = global_data.dispatch_id
        driver_user_id = tms.tms_db.get_driver_user_id_by_dispatch(dispatch_id)
        password = tms.tms_db.get_tms_config_value(key="UPDATE_DISPATCH_SECRET_TEXT")
        tms.central.change_delivery_date(dispatch_id, delivery_date, driver_user_id, password, check_result=True)


if __name__ == '__main__':
    weeeTest.main(base_url="https://api.sayweee.net", debug=True,
                  ext=['-m', 'TMS', "--junitxml=tests/results.xml"])

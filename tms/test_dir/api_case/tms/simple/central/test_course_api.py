# Author: bo.liu
# Time: 2025/2/6 17:41
# E-Mail: <EMAIL>
# 实现新增课程、查询课程、修改课程、预览课程、执行生成课程task job 流程
import allure
import weeeTest
from weeeTest import jmespath
from tms.qa_config import global_data
from tms.test_dir.api.tms.tms import tms
from tms.test_dir.api.tms.tms_central.course import Course


class TestCourseApi(weeeTest.TestCase):
    course = Course()
    tms.login()
    title = global_data.course_title
    fileLink = global_data.fileLink
    driver_user_id = global_data.course_driver_user_id

    # 新增课程
    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'CourseApi')
    @allure.title('新增课程')
    @allure.description('删除表中已有的课程和task,然后新增课程')
    def test_add_course(self):
        """
         新增课程
         清除表中已有的课程和task
        """
        # 处理历史数据
        tms.tms_db.delete_course_and_task(title=self.title)
        # 调接口
        res = self.course.add_and_edit_course(title=self.title,
                                              fileLink='https://video.weeecdn.com/tms/video/516/344/12C22669FAECC318.mp4',
                                              id=None,
                                              deliveryRegionIds='2',
                                              fileExtension='mp4',
                                              cycleType=1,
                                              miniDurationTime=1)
        # 检验返回result是True
        assert res["result"] is True

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'CourseApi')
    @allure.title('查询课程')
    @allure.description('查询课程列表，并校验title是否传入的一致')
    def test_get_course_list(self):
        """
        获取课程列表
        获取成功后取出id存入临时文件下一步编辑课程使用
        """
        # 调接口
        res = self.course.get_course_list(title=self.title)
        # 将拿到的课程id存入临时文件
        with open("course_temp_data.txt", "w", encoding="utf-8") as f:
            f.write(str(jmespath(res, "object.data[0].id")))
        # 需要校验返回的title是否与上一步创建课程输入的title一致
        assert jmespath(res, "object.data[0].title") == self.title

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'CourseApi')
    @allure.title('修改课程')
    @allure.description('修改课程，修改title和fileLink')
    def test_edit_course(self):
        """
        修改课程
        修改title和fileLink
        """
        # 从临时文件拿到id
        with open("course_temp_data.txt", "r", encoding="utf-8") as f:
            id = int(f.read())
        res = self.course.add_and_edit_course(title=self.title + 'Edit',
                                              fileLink=self.fileLink,
                                              id=id,
                                              deliveryRegionIds='2,5',
                                              fileExtension='mp4',
                                              cycleType=1,
                                              miniDurationTime=1)
        # 检验返回result是True
        assert res["result"] is True

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'CourseApi')
    @allure.title('预览课程')
    @allure.description('预览修改后的课程，可以同步验证fileLink的修改是成功的')
    def test_preview_course(self):
        """
        预览修改后的课程
        可以同步验证fileLink的修改是成功的
        """
        # 从临时文件拿到id
        with open("course_temp_data.txt", "r", encoding="utf-8") as f:
            id = int(f.read())
        # 调接口
        res = self.course.preview_course(id=id)
        # 检验返回的fileLink是否与修改后的fileLink一致
        assert res["object"] == self.fileLink

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'CourseApi')
    @allure.title('执行生成课程task job')
    @allure.description('执行生成课程task job')
    def test_create_course_task(self):
        """
        执行生成课程task job
        """
        # 先要处理司机的创建时间为前1小时--1-3 循环类型的课程需要处理司机创建时间为7天内
        tms.tms_db.update_driver_create_time(driver_user_id=self.driver_user_id, offset_hour=-1)
        # 执行job接口
        self.course.create_course_task()

# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  kaichuan.wang
@Version        :  V1.0.0
------------------------------------
@File           :  test_route_plan_api.py
@Description    :  
@CreateTime     :  2023/8/3 10:48
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/8/3 10:48
"""
import weeeTest
import allure
from tms.qa_config import global_data
from tms.test_dir.api.tms.tms import tms


class TestRoutePlanApi(weeeTest.TestCase):
    """
    Route Plan菜单相关接口
    """

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'RoutePlanApi')
    @allure.title("删除发货计划")
    def test_delete_delivery_plan(self):
        """
        删除发货计划
        """
        # 账户登录
        tms.login()
        # 删除发货计划
        tms.central.route_plan.delete_delivery_plan(delivery_plan_id=17476)
        # 接口返回校验
        self.assert_status_code(200)
        self.assert_json_path('message', "current delivery plan id does not meet the DELETE condition")
        self.assert_json_path("result", False)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'TMS-Online', 'RoutePlanApi')
    @allure.title("获取发货批次列表")
    def test_get_delivery_list(self):
        """
        获取发货批次列表
        """
        # 账户登录
        tms.login()
        tms.central.route_plan.get_delivery_list(sub_region_ids=[1, 2, 3, 4], delivery_date='2022-12-12',
                                                 check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'TMS-Online', 'RoutePlanApi')
    @allure.title("获取派车计划的派车单列表")
    def test_get_delivery_plan_dispatch_list(self):
        """
        获取派车计划的派车单列表
        """
        # 账户登录
        tms.login()
        tms.central.route_plan.get_delivery_plan_dispatch_list(delivery_plan_id=17485, check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'TMS-Online', 'RoutePlanApi')
    @allure.title("获取Region下的派车单列表")
    def test_get_region_dispatch_list(self):
        """
        获取Region下的派车单列表
        """
        # 账户登录
        tms.login()
        tms.central.route_plan.get_region_dispatch_list(sub_region_ids=[1, 2, 3, 4, 38], delivery_date="2022-12-12",
                                                        check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'TMS-Online', 'RoutePlanApi')
    @allure.title("获取可用司机列表")
    def test_get_available_drivers(self):
        """
        获取可用司机列表
        """
        # 账户登录
        tms.login()
        tms.central.route_plan.get_available_drivers(sub_region_id=1, delivery_date="2022-12-12", check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'RoutePlanApi')
    @allure.title("打印派车单")
    def test_print_dispatch_info(self):
        """
        打印派车单
        """
        # 账户登录
        tms.login()
        tms.central.route_plan.print_dispatch_info(delivery_plan_id=17485, check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'RoutePlanApi')
    @allure.title("更新派车单司机")
    def test_update_dispatch_driver(self):
        """
        更新派车单司机
        """
        tms.login()
        tms.reset_dispatch(dispatch_id=global_data.dispatch_id)
        tms.central.route_plan.update_dispatch_driver(dispatch_id=global_data.dispatch_id,
                                                      driver_user_id=global_data.driver_user_id,
                                                      driver_user_name=global_data.driver_user_name,
                                                      check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'RoutePlanApi')
    @allure.title("更改配送中派车单司机")
    def test_update_processing_dispatch_driver(self):
        """
        更改配送中派车单司机
        """
        tms.login()
        tms.reset_dispatch(dispatch_id=global_data.dispatch_id)
        dispatch_id = global_data.dispatch_id
        inventory_info = tms.tms_db.get_dispatch_task_info(dispatch_id=dispatch_id, info='task_id,latitude,longitude',
                                                           task_type="inventory")
        tms.driver.driver_delivery.update_task(task_id=inventory_info[0][0], task_type='inventory', task_status=30,
                                               task_step='finish', latitude=inventory_info[0][1],
                                               longitude=inventory_info[0][2])
        stop_info = tms.tms_db.get_dispatch_task_info(dispatch_id=dispatch_id, info='task_id,latitude,longitude',
                                                      task_type="stop")

        tms.driver.driver_delivery.update_task(task_id=stop_info[0][0], task_type='stop', task_status=20,
                                               task_step='travel', latitude=stop_info[0][1],
                                               longitude=stop_info[0][2])
        res = tms.central.route_plan.update_dispatch_driver(dispatch_id=global_data.dispatch_id,
                                                            driver_user_id=global_data.driver_user_id,
                                                            driver_user_name=global_data.driver_user_name)
        message = tms.util.get_response_values(res, 'message')
        assert 'Route delivery is in progress, change driver is not allowed.' == message[0]

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'RoutePlanApi')
    @allure.title("风险地址预校验")
    def test_risk_address_pre_check(self):
        """
        风险地址预校验
        """
        # 账户登录
        tms.login()
        tms.central.route_plan.risk_address_pre_check(delivery_plan_id=17485, check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'TMS-Online', 'RoutePlanApi')
    @allure.title("V18算法查询排车司机信息")
    def test_get_driver_info_v18(self):
        """
        V18算法查询排车司机信息
        """
        tms.login()
        sub_region_id = 2
        # 获取派车计划ID
        delivery_plan_res = tms.central.route_plan.get_delivery_plan_list(delivery_date='2024-07-04',
                                                                          sub_region_ids=[sub_region_id])
        delivery_plan_info = tms.util.get_response_values(delivery_plan_res, 'id[0]')
        # 获取排车草稿配置信息
        draft_info_res = tms.central.route_plan.get_pre_draft_info(delivery_plan_id=delivery_plan_info[0])
        draft_info = tms.util.get_response_values(draft_info_res, 'delivery_plan_id[0]', f'setting[{sub_region_id}]')
        # 获取V18算法查询排车司机信息
        tms.central.route_plan.get_driver_info_1(delivery_plan_id=draft_info[0], sub_region_id=sub_region_id,
                                                 driver_nums=draft_info[1], check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'RoutePlanApi')
    @allure.title("删除draft对应的派车单")
    def test_delete_dispatch(self):
        """
        删除draft对应的派车单
        """
        # 获取草稿列表
        draft_id = tms.get_draft_id(delivery_date='2022-12-24', sub_region_ids=[47],
                                    delivery_type='delivery', status=30)
        # 删除draft对应的派车单
        tms.central.route_plan.delete_dispatch(draft_id=draft_id)
        # 接口返回校验
        self.assert_status_code(200)
        if not self.jsonpath('result'):
            self.assert_json_path("result", False)
            self.assert_json_path("false", 'The current dispatch draft is not used.')

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'RoutePlanApi')
    @allure.title("删除draft")
    def test_delete_draft(self):
        """
        删除draft
        """
        # 获取草稿列表
        draft_id = tms.get_draft_id(delivery_date='2022-12-24', sub_region_ids=[47],
                                    delivery_type='delivery', status=30)
        # 删除draft
        tms.central.route_plan.delete_draft(draft_id=draft_id, check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'TMS-Online', 'RoutePlanApi')
    @allure.title("获取计划详情")
    def test_get_plan_detail(self):
        """
        获取计划详情
        """
        # 账户登录
        tms.login()
        # 获取草稿列表
        draft_id = tms.get_draft_id(delivery_date='2024-07-04', sub_region_ids=[1, 2, 3, 4],
                                    delivery_type='delivery', status=30)
        # 获取计划详情
        tms.central.route_plan.get_plan_detail(draft_id=draft_id, check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'TMS-Online', 'RoutePlanApi')
    @allure.title("获取draft对应的subRegion信息")
    def test_get_subregion_by_draft(self):
        """
        获取draft对应的subRegion信息
        """
        # 账户登录
        tms.login()
        # 获取草稿列表
        draft_id = tms.get_draft_id(delivery_date='2024-07-04', sub_region_ids=[1, 2, 3, 4],
                                    delivery_type='delivery', status=30)
        tms.central.route_plan.get_subregion_by_draft(draft_id=draft_id, check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'TMS-Online', 'RoutePlanApi')
    @allure.title("修改路线司机")
    def test_change_route_driver(self):
        """
        修改路线司机
        """
        tms.login()
        route_info = tms.route_info(delivery_date='2025-03-20', sub_region_ids=[20],
                                    delivery_type='delivery', status=30)
        # 修改路线司机
        tms.central.route_plan.change_route_driver(route_rec_id=route_info['route_rec_ids'][0],
                                                   draft_id=route_info['draft_id'],
                                                   driver_user_id=route_info['driver_id'],
                                                   driver_name=route_info['driver_name'], check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'TMS-Online', 'RoutePlanApi')
    @allure.title("获取草稿路线信息")
    def test_get_draft_routes(self):
        """
        获取草稿路线信息
        """
        # 账户登录
        tms.login()
        # 获取草稿ID
        draft_id = tms.get_draft_id(delivery_date='2025-03-20', sub_region_ids=[20],
                                    delivery_type='delivery', status=30)
        tms.central.route_plan.get_draft_routes(draft_id, check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'RoutePlanApi')
    @allure.title("路线一键排序")
    def test_re_sequence_route(self):
        """
        路线一键排序
        """
        # 获取草稿ID
        tms.login()
        draft_id = tms.get_draft_id(delivery_date='2022-12-24', sub_region_ids=[47],
                                    delivery_type='delivery', status=30)
        # 更新路线拆分状态为未拆分
        tms.tms_db.update_draft_split_flag(draft_id=draft_id, split_flag=0)
        tms.central.route_plan.re_sequence_route(draft_id=draft_id, check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'RoutePlanApi')
    @allure.title("清除所有路线司机")
    def test_clear_driver(self):
        """
        清除所有路线司机
        """
        tms.login()
        # 获取草稿ID
        route_info = tms.route_info(delivery_date='2022-12-12', sub_region_ids=[18],
                                    delivery_type='delivery', status=30)
        tms.central.route_plan.clear_driver(draft_id=route_info['draft_id'],
                                            route_rec_ids=route_info['route_rec_ids'])
        tms.central.route_plan.delete_draft(draft_id=route_info['draft_id'], check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'RoutePlanApi')
    @allure.title("将所有路线一键设置为默认司机")
    def test_set_routes_default_driver(self):
        """
        将所有路线一键设置为默认司机
        """
        # 获取草稿ID
        tms.login()
        draft_id = tms.get_draft_id(delivery_date='2024-07-04', sub_region_ids=[1, 2, 3, 4],
                                    delivery_type='delivery', status=30)
        tms.central.route_plan.set_routes_default_driver(draft_id=draft_id, check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'TMS-Online', 'RoutePlanApi')
    @allure.title("获取可用的司机列表")
    def test_get_route_drivers(self):
        """
        获取可用的司机列表
        """
        tms.login()
        # 获取草稿ID
        route_info = tms.route_info(delivery_date='2025-03-20', sub_region_ids=[20],
                                    delivery_type='delivery', status=30)
        tms.central.route_plan.get_route_drivers(delivery_plan_id=route_info['delivery_plan_id'],
                                                 draft_id=route_info['draft_id'], check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'TMS-Online', 'RoutePlanApi')
    @allure.title("修改路线Region")
    def test_change_route_region(self):
        """
        修改路线Region
        """
        # 获取草稿ID
        tms.login()
        route_info = tms.route_info(delivery_date='2025-03-20', sub_region_ids=[20],
                                    delivery_type='delivery', status=30)
        if isinstance(route_info['route_rec_ids'], int):
            route_rec_id = route_info['route_rec_ids']
        else:
            route_rec_id = route_info['route_rec_ids'][0]
        tms.central.route_plan.change_route_region(sub_region_id=20, route_rec_id=route_rec_id,
                                                   check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'TMS-Online', 'RoutePlanApi')
    @allure.title("获取路线的RegionID")
    def test_get_route_region_id(self):
        """
        获取路线的RegionID
        """
        # 账户登录
        tms.login()
        tms.central.route_plan.get_route_region_id(check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'TMS-Online', 'RoutePlanApi')
    @allure.title("获取配送点信息")
    def test_get_draft_points(self):
        """
        获取配送点信息
        """
        tms.login()
        route_info = tms.route_info(delivery_date='2024-01-30', sub_region_ids=[1],
                                    delivery_type='delivery', status=30)
        tms.central.route_plan.get_draft_points(delivery_plan_id=route_info['delivery_plan_id'],
                                                draft_id=route_info['draft_id'], route_nums=route_info['route_ids'],
                                                check_result=True)


if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True,
                  ext=['-m', 'TMS', "--junitxml=tests/results.xml"])

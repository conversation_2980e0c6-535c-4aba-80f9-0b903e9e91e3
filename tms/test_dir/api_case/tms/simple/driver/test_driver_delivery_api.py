# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  kaichuan.wang
@Version        :  V1.0.0
------------------------------------
@File           :  test_driver_delivery_api.py
@Description    :  
@CreateTime     :  2023/8/14 15:19
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/8/14 15:19
"""
import weeeTest
import allure
from weeeTest import jmespath

from tms.qa_config import global_data
from tms.test_dir.api.tms.tms import tms
from tms.test_dir.api_case.tms.utils import us_current_date


class TestDriverDeliveryApi(weeeTest.TestCase):
    """
    司机配送相关接口
    """

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'DriverDeliveryApi')
    def test_get_checkin_status(self):
        """
        获取司机pre-tipe步骤和参数
        """
        # 登录w2司机
        tms.login(driver_email="<EMAIL>")
        dispatch_id = global_data.dispatch_id
        res = tms.driver.driver_delivery.get_checkin_status(dispatch_id=dispatch_id, check_result=True)
        assert jmespath(res, "object.date") == str(us_current_date(zone="America/Los_Angeles"))
        # 切换回原始司机
        tms.login(driver_email="<EMAIL>")

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'DriverDeliveryApi')
    @allure.title("获取指定派车单详情信息")
    def test_dispatch_detail(self):
        """
        获取指定派车单详情信息
        """
        tms.login(driver_email="<EMAIL>")
        dispatch_id = global_data.dispatch_id
        tms.reset_dispatch(dispatch_id)
        tms.driver.driver_delivery.get_dispatch_detail(dispatch_id, check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'DriverDeliveryApi')
    @allure.title("获取仓库详情信息")
    def test_get_inventory_detail(self):
        """
        获取仓库详情信息
        """
        dispatch_id = global_data.dispatch_id
        dispatch_info = tms.tms_db.get_dispatch_task_info(dispatch_id=dispatch_id, info='task_id',
                                                          task_type="inventory")
        tms.driver.driver_delivery.get_inventory_detail(task_id=dispatch_info[0][0], check_result=True)

    # 由于包裹在load的时候skip操作已经从app下掉了，所以不再测试这个接口 2025-3-19 bo.liu
    # @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'DriverDeliveryApi')
    # @allure.title("跳过包裹扫描")
    # def test_skip_package(self):
    #     """
    #     跳过包裹扫描
    #     """
    #     dispatch_id = global_data.dispatch_id
    #     dispatch_info = tms.tms_db.get_dispatch_task_info(dispatch_id=dispatch_id, info='task_id,latitude,longitude',
    #                                                       task_type="inventory")
    #     tms.driver.driver_delivery.skip_package(task_id=dispatch_info[0][0], storage_type='F', load_type='Apart',
    #                                             supervisor_pwd='123456', latitude=dispatch_info[0][1],
    #                                             longitude=dispatch_info[0][2], check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'DriverDeliveryApi')
    @allure.title("包裹逐个扫描")
    def test_load_package(self):
        """
        包裹逐个扫描
        """
        tms.login()
        dispatch_id = global_data.dispatch_id
        dispatch_info = tms.tms_db.get_dispatch_task_info(dispatch_id=dispatch_id, info='task_id,latitude,longitude',
                                                          task_type="inventory")
        package_info = tms.get_packages(dispatch_id=dispatch_id, package_type='D', stop_seq=4)
        tms.driver.driver_delivery.load_package(task_id=dispatch_info[0][0], scan_type=0, storage_type='D',
                                                latitude=dispatch_info[0][1], longitude=dispatch_info[0][2],
                                                is_fast_load_skip=True, tracking_num_list=[package_info[0][1]],
                                                check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'DriverDeliveryApi')
    @allure.title("配送完成仓库任务")
    def test_update_inventory_task(self):
        """
        配送完成仓库任务
        """
        dispatch_id = global_data.dispatch_id
        dispatch_info = tms.tms_db.get_dispatch_task_info(dispatch_id=dispatch_id, info='task_id,latitude,longitude',
                                                          task_type="inventory")
        tms.driver.driver_delivery.update_task(task_id=dispatch_info[0][0], task_type='inventory', task_status=30,
                                               task_step='finish', latitude=dispatch_info[0][1],
                                               longitude=dispatch_info[0][2], check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'DriverDeliveryApi')
    @allure.title("更新站点包裹放置位置")
    def test_update_drop_off_location(self):
        """
        更新站点包裹放置位置
        """
        dispatch_id = global_data.dispatch_id
        drop_off_location = "With Doorman / Security / Receptionist"
        dispatch_info = tms.tms_db.get_dispatch_task_info(dispatch_id=dispatch_id, info='task_id,latitude,longitude')
        tms.driver.driver_delivery.update_drop_off_location(task_id=dispatch_info[0][0], dispatch_id=dispatch_id,
                                                            drop_off_location=drop_off_location, check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'DriverDeliveryApi')
    @allure.title("配送完成站点任务")
    def test_finish_stop_task(self):
        """
        配送完成站点任务
        """
        dispatch_id = global_data.dispatch_id
        dispatch_info = tms.tms_db.get_dispatch_task_info(dispatch_id=dispatch_id, info='task_id,latitude,longitude')
        tms.driver.driver_delivery.update_task(task_id=dispatch_info[0][0], task_type='stop', task_status=30,
                                               task_step='finish', latitude=dispatch_info[0][1], cancel_alcohol=False,
                                               longitude=dispatch_info[0][2], dispatch_id=dispatch_id,
                                               delivered_photo='https://img06.test.weeecdn.com/tms/image/522/686/39DB3F8480271ABD.jpeg',
                                               check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'DriverDeliveryApi')
    @allure.title("配送失败站点任务")
    def test_failure_stop_task(self):
        """
        配送失败站点任务
        """
        dispatch_id = global_data.dispatch_id
        dispatch_info = tms.tms_db.get_dispatch_task_info(dispatch_id=dispatch_id, info='task_id,latitude,longitude')
        tms.driver.driver_delivery.update_task(task_id=dispatch_info[1][0], task_type='stop', task_status=60,
                                               task_step='failure', latitude=dispatch_info[1][1],
                                               longitude=dispatch_info[1][2], dispatch_id=dispatch_id,
                                               failure_note='failureReason', check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'DriverDeliveryApi')
    @allure.title("部分配送完成站点任务")
    def test_partially_finish_stop_task(self):
        """
        部分配送完成站点任务
        """
        dispatch_id = global_data.dispatch_id
        dispatch_info = tms.tms_db.get_dispatch_task_info(dispatch_id=dispatch_id, info='task_id,latitude,longitude')
        tms.driver.driver_delivery.update_task(task_id=dispatch_info[2][0], task_type='stop', task_status=70,
                                               task_step='partially_finish', latitude=dispatch_info[2][1],
                                               longitude=dispatch_info[2][2], dispatch_id=dispatch_id,
                                               cancel_alcohol=False,
                                               delivered_photo='https://img06.test.weeecdn.com/tms/image/676/102/58BB311C83006767.jpeg',
                                               check_result=True)

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'DriverDeliveryApi')
    @allure.title("上报地址备注")
    def test_report_address_note(self):
        """
        上报地址备注
        """
        dispatch_id = global_data.dispatch_id
        dispatch_info = tms.tms_db.get_dispatch_task_info(dispatch_id=dispatch_id, info='task_id')
        tms.driver.driver_delivery.report_address_note(task_id=dispatch_info[3][0], address_note='test address note',
                                                       gate_code='123456', check_result=True)

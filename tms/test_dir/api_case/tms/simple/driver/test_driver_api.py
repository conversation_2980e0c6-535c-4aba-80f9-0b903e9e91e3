# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  kaichuan.wang
@Version        :  V1.0.0
------------------------------------
@File           :  test_driver_api.py
@Description    :  
@CreateTime     :  2023/8/2 17:56
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2023/8/2 17:56
"""
import random

import allure
import weeeTest
from weeeTest import jmespath
from tms.qa_config import global_data
from tms.test_dir.api.tms.tms import tms


class TestDriverApi(weeeTest.TestCase):
    """
    DriverApp相关接口
    """
    title = global_data.course_title + 'Edit'
    fileLink = global_data.fileLink
    driver_user_id = global_data.course_driver_user_id

    @weeeTest.mark.list('Regression', 'Smoke', 'TMS', 'DriverApi', 'bo.liu')
    @allure.title('司机上班打卡')
    @allure.description('司机上班打卡,打卡前清除掉当天的打卡记录，且需要2次打卡成功')
    def test_driver_clock_in(self):
        """
        clock in 不在范围需要2次打卡成功
        """
        tms.login(driver_email="<EMAIL>")
        # 获取司机时区
        time_zone = tms.driver.get_driver_info()['object']['timezone']
        # 获取司机打卡时区的当前时间
        my_date = tms.util.get_timezone_date(timezone=time_zone)
        # 清除已有的当天打卡记录
        tms.tms_db.delete_driver_time_card_log(self.driver_user_id, my_date)
        res = tms.driver.driver_clock_in("37.418024", "-121.886013", second_flag=False)
        if not res["result"]:
            res_second = tms.driver.driver_clock_in("37.418024", "-121.886013", second_flag=True)
            assert res_second["object"]["workingStatus"] == "WORKING"
        tms.login(driver_email='<EMAIL>')

    @weeeTest.mark.list('Regression', 'Smoke', 'TMS', 'DriverApi', 'bo.liu')
    @allure.title('司机下班打卡')
    @allure.description('司机下班打卡,打卡前需要更新上班打卡的时间，一次打卡成功')
    def test_driver_clock_out(self):
        """
        clock out 范围内1次打卡
        """
        tms.login(driver_email="<EMAIL>")
        # 获取司机时区
        time_zone = tms.driver.get_driver_info()['object']['timezone']
        # 获取司机打卡时区的当前时间
        my_time = tms.util.get_timezone_datetime(timezone=time_zone, offset_minutes=-10)
        # 修改CLOCKIN时间为10分钟之前
        tms.tms_db.update_driver_time_card_log(self.driver_user_id, my_time)
        # 打卡下班
        res = tms.driver.driver_clock_out("34.054429", "-117.4195172", second_flag=False)
        assert res["object"]["workingStatus"] == "READY TO WORK"
        tms.login(driver_email='<EMAIL>')

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'CourseApi')
    @allure.title('判断司机是否有未完成课程')
    def test_has_unfinished_courses(self):
        """
         判断司机是否有未完成课程
        """
        # 登陆指定司机的账号
        tms.login(driver_email="<EMAIL>")
        # 通过返回的结果是true判断获取司机有未完成课程
        res = tms.driver.has_unfinished_courses(check_result=True)
        assert jmespath(self.response, "object") == True, "司机没有未完成课程"
        tms.login(driver_email='<EMAIL>')

    # 新增课程
    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'CourseApi')
    @allure.title('获取司机的培训课程')
    def test_get_course_list(self):
        """
         获取司机的所有培训课程
        """
        # 登陆指定司机的账号
        tms.login(driver_email="<EMAIL>")
        # 获取司机账号信息，校验enforceTrainingCourse是True-此时app需要弹窗提示司机进行课程学习
        res_account = tms.driver.get_driver_info()
        assert res_account['object']['enforceTrainingCourse']
        # 获取司机的培训课程
        res = tms.driver.driver_get_course_list()
        # 循环课程列表，获取课程名称为指定的课程的courseTaskId并保存到临时文件
        courses = jmespath(res, "object")
        for course in courses:
            if course['courseTitle'] == self.title:
                with open("course_temp_list.txt", "w", encoding="utf-8") as f:
                    f.write(str(course['courseTaskId']))
                # 校验课程的状态是todo
                assert course['taskStatus'] == 'todo'
        tms.login(driver_email='<EMAIL>')

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'CourseApi')
    @allure.title('获取司机的培训课程详情')
    def test_get_course_detail(self):
        """
         获取司机的培训课程详情
        """
        # 从临时文件中获取课程taskid
        course_task_id = open("course_temp_list.txt", "r", encoding="utf-8").read()
        # 获取司机的培训课程
        res = tms.driver.driver_get_course_detail(id=course_task_id)
        # 检验获取到的课程内容
        course_content = jmespath(res, "object.courseContent")
        assert course_content == self.fileLink

    @weeeTest.mark.list('TMS-Regression', 'TMS-Smoke', 'TMS', 'CourseApi')
    @allure.title('司机结束课程并重新获取课程状态')
    def test_update_course_status(self):
        """
         司机结束课程并重新获取课程状态
        """
        # 从临时文件中获取课程taskid
        course_task_id = open("course_temp_list.txt", "r", encoding="utf-8").read()
        # 获取司机的培训课程
        start_time = tms.util.get_time() - 65
        end_time = tms.util.get_time()
        res = tms.driver.update_driver_course_finish(courseTaskId=course_task_id, startTime=start_time,
                                                     endTime=end_time, durationTime=1)
        # 重新获取司机的培训课程列表
        res = tms.driver.driver_get_course_list()
        # 循环课程列表，获取课程名称为指定的课程的courseTaskId并保存到临时文件
        courses = jmespath(res, "object")
        for course in courses:
            if course['courseTitle'] == self.title:
                # 校验课程的状态是todo
                assert course['taskStatus'] == 'finished'

    @weeeTest.mark.list('Regression', 'Smoke', 'TMS', 'DriverApi')
    @allure.title('司机Feedback')
    def test_add_feedback(self):
        """
        司机Feedback
        """
        # 账户登录
        tms.login(driver_email="<EMAIL>")
        # 提交司机反馈
        tms.driver.add_feedback(content="Feedback By Api Test.", check_result=True)

    @weeeTest.mark.list('Regression', 'Smoke', 'TMS', 'DriverApi')
    @allure.title('获取司机账号信息')
    def test_get_driver_info(self):
        """
        获取司机账号信息
        """
        tms.driver.get_driver_info(check_result=True)

    @weeeTest.mark.list('Regression', 'Smoke', 'TMS', 'DriverApi')
    @allure.title('司机signup普通单')
    def test_save_signup_delivery(self):
        """
        司机signup普通单
        """
        date = tms.util.get_date(days=1)
        tms.driver.save_signup(signup_type='fresh', address_total=35, delivery_date=date, check_result=True)

    @weeeTest.mark.list('Regression', 'Smoke', 'TMS', 'DriverApi')
    @allure.title('司机取消signup')
    def test_cancel_signup(self):
        """
        司机取消signup
        """
        tms.login()
        date = tms.util.get_date(days=1)
        driver_user_id = tms.get_driver_info(info='driver_user_id')
        signup_id = tms.tms_db.get_signup_id(driver_user_id=driver_user_id, delivery_date=date)
        tms.driver.cancel_signup(signup_id=signup_id, check_result=True)

    @weeeTest.mark.list('Regression', 'Smoke', 'TMS', 'DriverApi')
    @allure.title('获取当前账户的派车单列表')
    def test_get_dispatch_list(self):
        """
        获取当前账户的派车单列表
        """
        tms.driver.get_dispatch_list(check_result=True)

    @weeeTest.mark.list('Regression', 'Smoke', 'TMS', 'DriverApi')
    @allure.title('获取当前账户的派车单详情')
    def test_get_dispatch_detail(self):
        """
        获取当前账户的派车单详情
        """
        tms.login()
        tms.driver.get_dispatch_detail(check_result=True)

    @weeeTest.mark.list('Regression', 'Smoke', 'TMS', 'DriverApi')
    @allure.title('获取打包进度')
    def test_get_package_progress(self):
        """
        获取打包进度
        """
        tms.driver.get_package_progress(check_result=True)

    @weeeTest.mark.list('Regression', 'Smoke', 'TMS', 'DriverApi')
    @allure.title('上送配送开始里程表信息')
    def test_report_start_mileage(self):
        """
        上送配送开始里程表信息
        """
        dispatch_id = global_data.dispatch_id
        # 大于900小于1000的随机数进行初始开始里程数据
        start_mileage_distance = random.randint(900, 999)
        tms.driver.report_start_mileage(dispatch_id=dispatch_id,start_mileage_distance=start_mileage_distance,check_result=True)
        #校验开始里程数据入库成功
        assert tms.tms_db.get_start_end_mileage_distance(dispatch_id=dispatch_id)[0] == start_mileage_distance , "结果不一致"
    @weeeTest.mark.list('Regression', 'Smoke', 'TMS', 'DriverApi')
    @allure.title('上送配送结束里程表信息')
    def test_report_end_mileage(self):
        """
        上送配送结束里程表信息
        """
        dispatch_id = global_data.dispatch_id
        # 大于1000小于1100的随机数进行初始结束里程数据
        end_mileage_distance = random.randint(1000, 1100)
        tms.driver.report_end_mileage(dispatch_id=dispatch_id, end_mileage_distance=end_mileage_distance, check_result=True)
        # 校验结束里程数据入库成功
        assert tms.tms_db.get_start_end_mileage_distance(dispatch_id=dispatch_id)[1] == end_mileage_distance , "结果不一致"
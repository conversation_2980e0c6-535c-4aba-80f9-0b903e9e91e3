import weeeTest
from weeeTest import weeeConfig
from erp.test_dir.api_case import desk_header

class DeskCsDashboard(weeeTest.TestCase):
    def get_my_cs_agent_dashboard(self, data):
        """查询get_my_cs_agent_dashboard接口"""
        self.get(url=f"/cs/desk/dashboard/my/csAgentDashboard", json=data, headers=desk_header)
        return self.response


    def get_daily_dashboard(self,data):
        """查询get_daily_dashboard接口"""
        self.get(url=f"/cs/desk/dashboard/daily/digital", json=data, headers=desk_header)
        return self.response


    def agent_status_monitoring_dashboard(self,data):
        """"查询agent_status_monitoring_dashboard接口"""
        self.post(url=f"/cs/desk/dashboard/statusMonitoring", json=data, headers=desk_header)
        return self.response


if __name__ == '__main__':
    weeeConfig.base_url = 'https://api.tb1.sayweee.net'
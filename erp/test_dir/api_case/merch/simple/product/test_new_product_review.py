import weeeTest
import datetime
from _pytest.stash import D

from erp.test_dir.api.merch.product.new_product_review import NewProductReview
from erp.test_dir.api.merch.product.product_pim_api import ProductPIMApi

from erp.test_dir.db_utils import DBConnect


class TestNewProductReview(weeeTest.TestCase):

    # 创建新品
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    @weeeTest.mark.skip(reason="暂时跳过")
    def test_create_new_product(self):
        res1 = NewProductReview().create_new_productH(product_area='5', ethnicity='chinese',
                                                      storage_temperate_zone='R1',
                                                      division_num='06',
                                                      catalogue_num='0101', sub_catalogue_num='010101', price=100,
                                                      unit='G', unit_min=10,
                                                      volume=99, brand_key='BwhpDddW',
                                                      vender_id='8', purchase_price=10, purchase_unit='unit',
                                                      vendor_product_title='test-Vendor Product Title',
                                                      vendor_SKU_code='test-Vendor SKU', short_title='test-product',
                                                      short_title_en='test-product',
                                                      check_shelf_life='Y',
                                                      receiving_date_type='expiration_date', shelf_life=123,
                                                      receiving_shelf_life_limit=62,
                                                      reminder_shelf_life_limit=31,
                                                      outbound_shelf_life_limit=12, sample='N', is_delicate='N',
                                                      is_fragile_product='N', imported_sku='N', crossdock_sku='N',
                                                      special_reason='Delicious and delicious bread！',
                                                      is_purchase_product='N', need_detail_description='N')
        audit_id = str(res1["object"]["id"])

        assert res1["result"] is True
        assert res1["object"]["id"] is not None

        # 提交新品时 校验是否有buyer权限
        res2 = NewProductReview().isbuyer_newProduct(newProduct_id=audit_id)
        assert res2["result"] is True
        assert res2["object"]["is_owner"] is True

        # submit 新品
        resSubmit = NewProductReview().approval_newProduct(newProduct_id=audit_id)
        assert resSubmit["result"] is True

        # Category Leader 审核（因开发代码是后端调用后端接口获取当前审核流，F12/抓取接口 无法获取到当前审核状态，因此只能调用固定审核次数）
        resL = NewProductReview().approval_newProduct(newProduct_id=audit_id)
        assert resL["result"] is True

        # 初审
        resA = NewProductReview().approval_newProduct(newProduct_id=audit_id)
        assert resA["result"] is True

        # 终审(农产品类型初审/终审自动合并，因此暂时注释)
        # resB = NewProductReview().approval_newProduct(newProduct_id=audit_id)
        # assert resB["result"] is True

        # create SKU
        resSKU = NewProductReview().createSKU(newProduct_id=audit_id)
        assert resSKU["result"] is True
        sql_newProduct = "SELECT * FROM weee_comm.gb_product_audit WHERE id ="
        db_res = DBConnect().select_data_from_mysql(sql_newProduct + audit_id)
        print("新品审核id:", db_res[0])

        sql_product = "SELECT * FROM weee_comm.gb_product WHERE id ="
        db_res1 = DBConnect().select_data_from_mysql(sql_product + str(db_res[0][1]))
        print("产品审核id:", db_res1)


    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_create_new_product_delete(self):
        """创建新品后删除"""
        res1 = NewProductReview().create_new_product_zjw(product_area='5', ethnicity='chinese',
                                                              title="auto_new_productForDelete"+str(datetime.date.today()),
                                                              storage_temperate_zone='N1',
                                                              division_num='04',
                                                              catalogue_num='1201', sub_catalogue_num='120101',
                                                              price=39.9,
                                                              unit='L', unit_min=10,
                                                              volume=99, brand_key='BwhpDddW',
                                                              vender_id='8', purchase_price=10,
                                                              purchase_unit='unit',
                                                              vendor_product_title='test-Vendor Product Title',
                                                              vendor_SKU_code='test-Vendor SKU',
                                                              short_title='test-product',
                                                              short_title_en='test-product',
                                                              check_shelf_life='Y',
                                                              receiving_date_type='expiration_date', shelf_life=123,
                                                              receiving_shelf_life_limit=62,
                                                              reminder_shelf_life_limit=31,
                                                              outbound_shelf_life_limit=12, sample='N',
                                                              is_delicate='N',
                                                              is_fragile_product='N', imported_sku='N',
                                                              crossdock_sku='N',
                                                              special_reason='Delicious and delicious bread！',
                                                              is_purchase_product='N', need_detail_description='N')
        # 从创建新品response中获取新品audit_id
        audit_id = str(res1["object"]["id"])
        # 出参断言
        assert res1["result"] is True
        assert res1["object"]["id"] is not None

        # 删除已创建的新品
        res2 = NewProductReview().delete_new_product_review(audit_id)
        # 出参断言
        assert res2["result"] is True

        # 数据库断言
        sql_newProduct_status = "SELECT * FROM weee_comm.gb_product_audit WHERE id ="
        db_res_status = DBConnect().select_data_from_mysql(sql_newProduct_status + audit_id)
        # 校验创建数据新品审批表中status=X（已删除）
        assert db_res_status[0][4] == "X"

    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_create_new_product_createSKU(self):
        """创建新品并create SKU 不走审批流程简化版"""

        # 查询新品审核表中的主键id，用于拼接生成不重复的UPC
        sql_query_id = "SELECT id FROM weee_comm.gb_product_audit order by id desc"
        db_res_id = DBConnect().select_data_from_mysql(sql_query_id)
        # 调用创建新品接口
        res1 = NewProductReview().create_new_product_zjw(product_area='5', ethnicity='chinese',
                                                         title="auto_new_product_createSKU" + str(datetime.date.today()),
                                                         storage_temperate_zone='N1',
                                                         division_num='04',
                                                         catalogue_num='1201', sub_catalogue_num='120101',
                                                         price=39.9,
                                                         unit='L', unit_min=10,
                                                         volume=99, brand_key='BwhpDddW',UPC_code="200000090"+ str(db_res_id[0][0]),
                                                         vender_id='8', purchase_price=10,
                                                         purchase_unit='unit',
                                                         vendor_product_title='test-Vendor Product Title',
                                                         vendor_SKU_code='test-Vendor SKU',
                                                         short_title='test-product',
                                                         short_title_en='test-product',
                                                         check_shelf_life='Y',
                                                         receiving_date_type='expiration_date', shelf_life=123,
                                                         receiving_shelf_life_limit=62,
                                                         reminder_shelf_life_limit=31,
                                                         outbound_shelf_life_limit=12, sample='N',
                                                         is_delicate='N',
                                                         is_fragile_product='N', imported_sku='N',
                                                         crossdock_sku='N',
                                                         special_reason='Delicious and delicious bread！',
                                                         is_purchase_product='N', need_detail_description='N')
        # 从创建新品response中获取新品audit_id
        audit_id = str(res1["object"]["id"])
        # 出参断言
        assert res1["result"] is True
        assert res1["object"]["id"] is not None

        # 手动修改新品Status=B，跳过新品审批流程
        sql_update_status = "update weee_comm.gb_product_audit set status='B',weee_buyer_id ='13347083' where id ="
        DBConnect().update_data_from_mysql(sql_update_status + audit_id)

        # create SKU
        resSKU = NewProductReview().createSKU(newProduct_id=audit_id)
        assert resSKU["result"] is True

        # 创建产品成功后，新品审核表中的product_id写入数据
        sql_query_audit_product_id = "SELECT * FROM weee_comm.gb_product_audit WHERE id ="
        db_res = DBConnect().select_data_from_mysql(sql_query_audit_product_id + audit_id)
        # db校验新品审核表product_id 不为空
        assert db_res[0][1] is not None




if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')

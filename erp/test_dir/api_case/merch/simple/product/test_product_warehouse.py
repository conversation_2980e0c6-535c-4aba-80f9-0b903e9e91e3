import weeeTest

from erp.test_dir.api.merch.product.product_warehouse_api import ProductWarehouseInterface
from erp.test_dir.db_utils import DBConnect


class TestProductWarehouse(weeeTest.TestCase):
    # 定义全局pw id
    global_pw_id = ''

    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_pw_list(self):
        """查询PW列表"""
        resp = ProductWarehouseInterface().product_warehouse_list()
        # 获取出参中的pw id
        pw_id = resp['object']['list'][0]['id']
        # 全局pw id赋值
        TestProductWarehouse.global_pw_id = pw_id
        assert resp["result"] is True
        assert resp["object"]["list"] is not None
        sql_pw = f"SELECT * FROM weee_merch.im_warehouse_product WHERE id= {pw_id};"
        db_res = DBConnect().select_data_from_mysql(sql_pw)
        assert int(pw_id) == db_res[0][0]

    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_pw_list_filter(self):
        """PW多条件组合查询"""
        # 查询结果为空
        resp_result_null = ProductWarehouseInterface().product_warehouse_filter_group(page='1', pageSize='20',
                                                                                      productId='106875 ',
                                                                                      purchaseStatus='A',
                                                                                      purchaseMethod='L',
                                                                                      assortmentRole='Core',
                                                                                      isPurchase='Y',
                                                                                      physicalInventoryId='25',
                                                                                      scaId='13347083')
        assert resp_result_null["result"] is True
        assert int(resp_result_null["object"]["totalCount"]) == 0

        # 再次查询，预期结果中存在一条满足条件的数据
        resp = ProductWarehouseInterface().product_warehouse_filter_group(page='1', pageSize='20', productId='106875 ',
                                                                          purchaseStatus='A', purchaseMethod='L',
                                                                          assortmentRole='Testing', isPurchase='Y',
                                                                          physicalInventoryId='25', scaId='13347083')
        # 获取出参中的pw id
        pw_id = resp['object']['list'][0]['id']
        assert resp["result"] is True
        assert resp["object"]["list"] is not None
        # 数据库校验
        sql_pw = f"SELECT * FROM weee_merch.im_warehouse_product WHERE id= {pw_id};"
        db_res = DBConnect().select_data_from_mysql(sql_pw)
        assert int(pw_id) == db_res[0][0]
        assert int(resp['object']['list'][0]['inventoryId']) == db_res[0][9]
        assert int(resp['object']['list'][0]['scaId']) == db_res[0][12]
        assert resp['object']['list'][0]['assortmentRole'] == db_res[0][19]
        # # 满足条件数据仅1条
        assert len(db_res) == 1

    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_pw_detail(self):
        """查询PW详情页"""
        resp = ProductWarehouseInterface().product_warehouse_detail(TestProductWarehouse.global_pw_id)
        assert resp["result"] is True
        assert resp["object"]["productWarehouse"]["id"] == TestProductWarehouse.global_pw_id

        # 增加出参和db关键字段校验
        sql_pw = f"SELECT * FROM weee_merch.im_warehouse_product WHERE id= {TestProductWarehouse.global_pw_id};"
        db_res = DBConnect().select_data_from_mysql(sql_pw)
        assert int(resp["object"]["productWarehouse"]["productId"]) == db_res[0][7]
        assert int(resp["object"]["productWarehouse"]["inventoryId"]) == db_res[0][9]

    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_pw_edit(self):
        """编辑PW信息"""
        # 根据Purchasing Specialist查询
        resp = ProductWarehouseInterface().product_warehouse_filter(filter='&page=1&pageSize=1&filter[scaId]=13347083')
        assert resp["result"] is True
        # 出参中的信息，用于后续编辑
        pw_id = resp["object"]["list"][0]["id"]
        purchaseStatus = resp["object"]["list"][0]["purchaseStatus"]
        purchaseMethod = resp["object"]["list"][0]["purchaseMethod"]
        secondaryVendor = resp["object"]["list"][0]["secondaryVendor"]
        scaId = resp["object"]["list"][0]["scaId"]
        primaryVendor = resp["object"]["list"][0]["primaryVendor"]
        outl = resp["object"]["list"][0]["outl"]
        assortmentRole = resp["object"]["list"][0]["assortmentRole"]

        # 首次编辑，传入错误的采购员id
        resp_update = ProductWarehouseInterface().product_warehouse_edit(id=pw_id, purchaseStatus=purchaseStatus,
                                                                         secondaryVendor=secondaryVendor,
                                                                         purchaseMethod=purchaseMethod,
                                                                         scaId=scaId + "11111",
                                                                         primaryVendor=primaryVendor, outl=outl,
                                                                         assortmentRole=assortmentRole)
        assert resp_update["result"] is False
        assert resp_update[
                   "message"] == 'error Purchasing Specialist, please add in Purchasing Specialist Management first. '

        # AssortmentRole为空，PurchaseStatus为live拦截
        resp_update1 = ProductWarehouseInterface().product_warehouse_edit(id=pw_id, purchaseStatus="A", purchaseCode='R',
                                                                          purchaseMethod=purchaseMethod, scaId=scaId,
                                                                          primaryVendor=primaryVendor, outl=outl)
        assert resp_update1["result"] is False
        assert resp_update1[
                   "message"] == 'To live this SKU, please assign an assortment role first. '

        # 再次编辑传入正确入参，修改outl
        resp_update2 = ProductWarehouseInterface().product_warehouse_edit(id=pw_id, purchaseStatus=purchaseStatus,
                                                                          secondaryVendor=secondaryVendor,
                                                                          purchaseMethod=purchaseMethod, scaId=scaId,
                                                                          primaryVendor=primaryVendor,
                                                                          outl=int(outl) + 1,
                                                                          assortmentRole=assortmentRole)
        assert resp_update2["result"] is True
        assert pw_id == resp_update2["object"]["id"]

    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_pw_batch_update_outl(self):
        """批量更新PW Outl"""
        # 固定测试sku：107051&25仓
        json_data = {
            "type": "outl",
            "data": {
                "updatePW": [
                    {
                        "id": "1481928",
                        "outl": "10"
                    }
                ]
            }
        }
        resp = ProductWarehouseInterface().product_warehouse_batch_update(json_data)
        assert resp["result"] is True
        assert resp["message"] == "success"

        # 校验修改成功后的db数据
        sql_pw = "SELECT * FROM weee_merch.im_warehouse_product WHERE id= 1481928;"
        db_res = DBConnect().select_data_from_mysql(sql_pw)
        assert 10 == db_res[0][15]

    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_pw_batch_update_primaryVendor(self):
        """批量更新PW PrimaryVendor"""
        # 固定测试sku：107051&25仓
        json_data = {
            "type": "primaryVendor",
            "data": {
                "updatePW": [
                    {
                        "id": "1481928",
                        "primary_vendor": "2"
                    }
                ]
            }
        }
        resp = ProductWarehouseInterface().product_warehouse_batch_update(json_data)
        assert resp["result"] is True
        assert resp["message"] == "success"

        # 校验修改成功后的db数据
        sql_pw = "SELECT * FROM weee_merch.im_warehouse_product WHERE id= 1481928;"
        db_res = DBConnect().select_data_from_mysql(sql_pw)
        assert 2 == db_res[0][10]

    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_pw_batch_update_assortmentRole(self):
        """批量更新PW Assortment Role"""
        # 固定测试sku：107051&25仓
        json_data = {
            "type": "assortmentRole",
            "data": {
                "updatePW": [
                    {
                        "id": "1481928",
                        "assortment_role": "Testing-F-01",
                        "testing_period": 28
                    }
                ],
                "oldPW": [
                    {
                        "id": "1481928",
                        "assortment_role": "null",
                        "testing_period": "null"
                    }
                ]
            }
        }
        resp = ProductWarehouseInterface().product_warehouse_batch_update(json_data)
        assert resp["result"] is True
        assert resp["message"] == "success"

        # 校验修改成功后的db数据
        sql_pw = "SELECT * FROM weee_merch.im_warehouse_product WHERE id= 1481928;"
        db_res = DBConnect().select_data_from_mysql(sql_pw)
        assert "Testing-F-01" == db_res[0][19]

    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_pw_batch_update_status(self):
        """批量更新PW Purchase Status"""
        # 固定测试sku：107051&25仓
        json_data = {
            "pwId": "1481928",
            "psId": [
                [
                    {
                        "sales_org_id": "2",
                        "product_id": "107051"
                    },
                    {
                        "sales_org_id": "102",
                        "product_id": "107051"
                    }
                ]
            ],
            "type": "purchaseStatus",
            "data": {
                "purchase_status": "A",
                "live_code": "R",
                "next_delivery_date": "0000-00-00 00:00:00",
                "comment": ""
            }
        }
        resp = ProductWarehouseInterface().product_warehouse_batch_update_status(json_data)
        assert resp["result"] is True
        assert resp["message"] == "success"

        # 校验修改成功后的db数据
        sql_pw = "SELECT * FROM weee_merch.im_warehouse_product WHERE id= 1481928;"
        db_res = DBConnect().select_data_from_mysql(sql_pw)
        assert "A" == db_res[0][2]
        assert "R" == db_res[0][3]

        # 数据还原
        sql_rollback = "update weee_merch.im_warehouse_product set outl=6,primary_vendor=169,assortment_role=null,testing_period=null,purchase_status='I',live_code=null where id=1481928;"
        DBConnect().update_data_from_mysql(sql_rollback)


if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net")

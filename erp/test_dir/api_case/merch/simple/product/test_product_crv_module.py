import weeeTest
from erp.test_dir.api.merch.product.product_crv_api import ProductCrvInterface
from erp.test_dir.db_utils import DBConnect


class TestProductCrvModule(weeeTest.TestCase):

    @weeeTest.params.file(file_name="product_crv_config.yaml", key="product_crv_list")
    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_product_crv_list(self, name, request):
        """商品crv关系配置列表查询"""
        print("执行接口名称：" + name)
        resp = ProductCrvInterface().inquire_product_crv_list(request["params_data"])
        start_sql = "SELECT id FROM weee_comm.product_crv_relationship WHERE status = 'A' ORDER BY id DESC;"
        res_db = DBConnect().select_data_from_mysql(start_sql)
        assert resp["object"]["list"][0]["id"] == str(res_db[0][0])

    @weeeTest.mark.list('Smoke', 'SRM')
    def test_product_crv_option(self):
        """验证支持CRV商品配置查询"""
        resp = ProductCrvInterface().inquire_product_crv_option()
        res_id = resp["object"]["products"][0]["id"]
        if res_id:
            start_sql = f"SELECT gc.parent_num FROM weee_comm.gb_product gb join weee_comm.gb_catalogue gc on gb.catalogue_num = gc.num WHERE gb.id = {res_id};"
            # 调用数据库查询除结果，结果展示为列表加元组的格式
            res_db = DBConnect().select_data_from_mysql(start_sql)
            # 支持查询的分类都是11分类的商品
            assert int(res_db[0][0]) == 11

    @weeeTest.mark.list('Regression', 'SRM')
    def test_delete_product_crv_config(self):
        """验证删除crv关系配置"""
        start_sql = f"SELECT id, source_product_id FROM weee_comm.product_crv_relationship WHERE status = 'A';"
        res_start_db = DBConnect().select_data_from_mysql(start_sql)
        crv_id = res_start_db[0][0]
        if crv_id:
            data = {"id": crv_id}
            resp = ProductCrvInterface().delete_product_crv_conf(data)
            end_sql = f"SELECT weee_buyer_id FROM weee_comm.gb_product WHERE id = {res_start_db[0][1]};"
            res_end_db = DBConnect().select_data_from_mysql(end_sql)
            if res_end_db[0][0] == "7169981":
                assert resp["message"] == "success"
            else:
                assert resp["message"] == "You do not have sufficient permissions to modify field [crv] of this product"

    @weeeTest.mark.list('Regression', 'SRM')
    def test_add_product_crv_config(self):
        """验证新增crv关系配置"""
        start_sql = "SELECT id FROM weee_comm.product_crv_relationship WHERE source_product_id = 105388 AND status = 'A'"
        res_db = DBConnect().select_data_from_mysql(start_sql)
        if len(res_db) != 0:
            print(res_db[0][0])
            sql = f"UPDATE weee_comm.product_crv_relationship SET status = 'X' WHERE id = {res_db[0][0]}"
            DBConnect().update_data_from_mysql(sql)
            print(res_db[0][0])
            ProductCrvInterface().add_product_crv_conf(crv_data={})
            res_end_db = DBConnect().select_data_from_mysql(start_sql)
            assert len(res_end_db) == len(res_db)
        else:
            ProductCrvInterface().add_product_crv_conf(crv_data={})
            res_end_db = DBConnect().select_data_from_mysql(start_sql)
            assert len(res_end_db) != len(res_db)

    @weeeTest.mark.list('Regression', 'SRM')
    @weeeTest.params.file(file_name="product_crv_config.yaml", key="update_product_crv_conf")
    def test_update_product_crv_config(self, name, request):
        """验证更新crv关系配置"""
        print("执行接口名称：" + name)
        data = request["json_data"]
        resp = ProductCrvInterface().update_product_crv_conf(data)
        start_sql = f"SELECT target_product_id FROM weee_comm.product_crv_relationship WHERE source_product_id = {data['source_product_id']} AND status = 'A';"
        res_db = DBConnect().select_data_from_mysql(start_sql)
        if resp["result"] == "false":
            assert resp["message"] == "You do not have sufficient permissions to modify field [crv] of this product"
        else:
            assert str(res_db[0][0]) == data["target_product_id"]


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')

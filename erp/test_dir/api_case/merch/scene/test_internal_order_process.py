import datetime
import weeeTest
from erp.test_dir.api.merch.transfer.transfer_order import TransferOrder
from erp.test_dir.db_utils import DBConnect


class TestCreatePRProcess(weeeTest.TestCase):
    """internal order的流程"""

    @weeeTest.mark.list('Regression', 'Smoke', 'SRM')
    def test_scene_add_internal_order_and_send_to_wms(self):
        """场景: 创建新的调拨单，添加商品并且下发"""
        create_resp = TransferOrder().create_transfer_order(outbound_inventory=25, inbound_inventory=20,
                                                            email='<EMAIL>',
                                                            pickup_date=datetime.datetime.today().strftime("%Y-%m-%d"),
                                                            eta_date=(datetime.datetime.today() + datetime.timedelta(
                                                                days=1)).strftime("%Y-%m-%d"))
        assert create_resp["result"] is True
        internal_order_id = create_resp["object"]["id"]
        order_line_resp = TransferOrder().transfer_order_add_product_line(order_id=internal_order_id, product_id=44218,
                                                                          quantity=1)
        assert order_line_resp["object"]["success"] == 1
        query_resp = TransferOrder().transfer_order_list(outbound_inventory=25, inbound_inventory=20,
                                                         sku=44218)
        assert query_resp["object"]["orders"][0]["status_title"] == 'Create'
        send_wms_resp = TransferOrder().transfer_order_send_wms(order_id=internal_order_id)
        assert send_wms_resp["object"]["success"] == 1
        query_again_resp = TransferOrder().transfer_order_list(outbound_inventory=25, inbound_inventory=20,
                                                               sku=44218)
        assert query_again_resp["object"]["orders"][0]["status_title"] == 'Printed'
        order_id = query_again_resp["object"]["orders"][0]["order_id"]
        sql = f"SELECT * FROM weee_comm.gb_invoice_internal where order_id = {order_id}"
        db_res = DBConnect().select_data_from_mysql(sql)
        assert str(db_res[0][2]) == datetime.datetime.today().strftime("%Y-%m-%d")
        assert db_res[0][6] == 25
        assert db_res[0][7] == 20



if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')

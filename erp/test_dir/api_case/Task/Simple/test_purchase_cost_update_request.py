import time
import weeeTest
from erp.test_dir.api.task.task_common_api import TaskCommonApi
from erp.test_dir.api.task.purchase_cost_update_request import PurchaseCostUpdateManagement
from erp.test_dir.api.merch.product.product_srm import ProductInfo


class TestCostUpdate(weeeTest.TestCase):
    product = ''
    cost = ''
    instance_url = ''
    ticket_instance_id = ''
    ticket_id = ''
    flow_name = ''
    task_id = ''

    def test_create_request(self, request):
        """新增工单"""
        resp = PurchaseCostUpdateManagement().update_vendor_product_cost(request["json_data"])
        assert resp["result"] is False
        assert "A purchase cost review task will be created for review" in resp["message"]
        TestCostUpdate.ticket_id = resp["message"].split('>')[-2].strip().split(' ')[0]
        TestCostUpdate.product = request["json_data"]["product_id"]
        TestCostUpdate.cost = request["json_data"]["purchase_price"]
        time.sleep(5)

    def test_verify_product_info(self):
        """获取中间产品模块信息并校验内容"""
        time.sleep(5)
        task_detail_url = f'businessKey={TestCostUpdate.ticket_id}'
        resp = TaskCommonApi().get_request_detail_content(task_detail_url)
        assert str(resp["object"]["productId"]) == TestCostUpdate.product
        assert resp["object"]["costPerUnitAfter"] == TestCostUpdate.cost
        assert resp["object"]["effectiveDate"] == "Immediate when approve"

    def test_verify_node_info(self):
        """获取顶部节点模块信息并校验内容"""
        time.sleep(5)
        TestCostUpdate.instance_url = f'businessKey={TestCostUpdate.ticket_id}&zoneId=Asia/Shanghai'
        resp = TaskCommonApi().get_request_detail(TestCostUpdate.instance_url)
        assert resp["object"]["ticketNumber"] == str(TestCostUpdate.ticket_id)
        assert resp["object"]["creator"] == "<EMAIL>"
        assert resp["object"]["assigneeName"] == "<EMAIL>"
        deadline = TaskCommonApi().get_next_business_day(2)
        assert deadline in resp["object"]["deadline"]
        assert resp["object"]["flowName"] == "Owner/Local Owner"
        TestCostUpdate.ticket_instance_id = resp["object"]["processInstanceId"]
        TestCostUpdate.task_id = resp["object"]["taskId"]

    def test_get_product_vendor_cost(self):
        """获取product vendor的cost"""
        resp = ProductInfo().product_detail(TestCostUpdate.product)
        final_cost = resp["object"]["purchase_info"]["vendor_info"][0]["purchase_price"]
        return final_cost

    @weeeTest.params.file(file_name="task_data.yaml", key="Purchase_cost_update_increase")
    @weeeTest.mark.list('Regression', 'SRM', 'Task', 'Smoke')
    def test_cancel_ticket(self, request):
        """创建purchase cost update工单并且取消 [103651]"""
        TestCostUpdate.test_create_request(self, request)
        TestCostUpdate.test_verify_product_info(self)
        TestCostUpdate.test_verify_node_info(self)
        cancel_url = f'process_instance_id={TestCostUpdate.ticket_instance_id}&task_id={TestCostUpdate.ticket_id}&type=bpm_purchase_cost&comment=1'
        cancel_resp = TaskCommonApi().cancel_request(cancel_url)
        assert cancel_resp["message"] == "Task canceled."
        time.sleep(3)
        resp = TaskCommonApi().get_request_detail(TestCostUpdate.instance_url)
        assert resp["object"]["flowName"] == "End"
        assert resp["object"]["flowStatus"] == 3
        final_cost = TestCostUpdate.test_get_product_vendor_cost(self)
        assert final_cost != TestCostUpdate.cost

    @weeeTest.params.file(file_name="task_data.yaml", key="Purchase_cost_update_increase")
    @weeeTest.mark.list('Regression', 'SRM', 'Task', 'Smoke')
    def test_reject_ticket(self, request):
        """创建purchase cost update工单并且reject [103653]"""
        TestCostUpdate.test_create_request(self, request)
        TestCostUpdate.test_verify_product_info(self)
        TestCostUpdate.test_verify_node_info(self)
        resp_reject = TaskCommonApi().submit_purchase_cost_update_request(
            processInstanceId=TestCostUpdate.ticket_instance_id,
            taskId=TestCostUpdate.task_id, comment='Solution : Reject     Comment : 1',
            solution='owner_solution_2')
        assert resp_reject["result"] is True
        time.sleep(3)
        resp = TaskCommonApi().get_request_detail(TestCostUpdate.instance_url)
        assert resp["object"]["flowName"] == "End"
        assert resp["object"]["flowStatus"] == 2
        final_cost = TestCostUpdate.test_get_product_vendor_cost(self)
        assert final_cost != TestCostUpdate.cost

    @weeeTest.params.file(file_name="task_data.yaml", key="Purchase_cost_update_increase")
    @weeeTest.mark.list('Regression', 'SRM', 'Task', 'Smoke')
    def test_approve_ticket(self, request):
        """创建purchase cost update工单并且approve [103652]"""
        TestCostUpdate.test_create_request(self, request)
        TestCostUpdate.test_verify_product_info(self)
        TestCostUpdate.test_verify_node_info(self)
        resp_approve = TaskCommonApi().submit_purchase_cost_update_request(
            processInstanceId=TestCostUpdate.ticket_instance_id,
            taskId=TestCostUpdate.task_id, comment='Solution : Approve     Comment : ',
            solution='owner_solution_1')
        assert resp_approve["result"] is True
        time.sleep(3)
        resp = TaskCommonApi().get_request_detail(TestCostUpdate.instance_url)
        assert resp["object"]["flowName"] == "End"
        assert resp["object"]["flowStatus"] == 2
        final_cost = TestCostUpdate.test_get_product_vendor_cost(self)
        assert final_cost == TestCostUpdate.cost

    @weeeTest.params.file(file_name="task_data.yaml", key="Purchase_cost_update_cost_fail_validation")
    @weeeTest.mark.list('Regression', 'SRM', 'Task', 'Smoke')
    def test_cost_update_fail_validation(self, request):
        """cost超过selling price, 更新失败"""
        resp = PurchaseCostUpdateManagement().update_vendor_product_cost(request["json_data"])
        assert resp["result"] is False
        assert "Purchase cost cannot be greater than the" in resp["message"]
        final_cost = TestCostUpdate.test_get_product_vendor_cost(self)
        assert final_cost == "10.20"

    @weeeTest.params.file(file_name="task_data.yaml", key="Purchase_cost_update_cost_fail_duplicate")
    @weeeTest.mark.list('Regression', 'SRM', 'Task', 'Smoke')
    def test_cost_update_fail_validation(self, request):
        """已有工单, 重复创建, 更新失败"""
        resp = PurchaseCostUpdateManagement().update_vendor_product_cost(request["json_data"])
        assert resp["result"] is False
        assert "There is a pending purchase cost update request" in resp["message"]

    @weeeTest.params.file(file_name="task_data.yaml", key="Purchase_cost_update_cost_back")
    @weeeTest.mark.list('Regression', 'SRM', 'Task', 'Smoke')
    def test_cost_data_reset(self, request):
        """数据还原, 直接更新成功"""
        resp = PurchaseCostUpdateManagement().update_vendor_product_cost(request["json_data"])
        assert resp["result"] is True
        final_cost = TestCostUpdate.test_get_product_vendor_cost(self)
        assert final_cost == "10.00"


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')

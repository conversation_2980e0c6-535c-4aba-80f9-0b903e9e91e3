

import weeeTest
from erp.test_dir.api.desk.Dashboard.cs_dashboard import DeskCsDashboard

class TestDeskCsDashboard(weeeTest.TestCase):
    id = ""

    @weeeTest.params.file(file_name="cs_dashboard.yaml", key="get_my_cs_agent_dashboard")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_get_my_cs_agent_dashboard(self, name, description, request, expected_result):
        """查看my_cs_Agent_Dashboard详情页"""
        print(description)
        resp = DeskCsDashboard().get_my_cs_agent_dashboard(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]


    @weeeTest.params.file(file_name="cs_dashboard.yaml", key="get_daily_dashboard")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_get_daily_dashboard(self,name,description,request,expected_result):
        """查询test_daily_Dashboard接口"""
        print(description)
        resp = DeskCsDashboard().get_daily_dashboard(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

    @weeeTest.params.file(file_name="cs_dashboard.yaml", key="agent_status_monitoring_dashboard")
    @weeeTest.mark.list('Regression', 'Smoke', 'CS')
    def test_agent_status_monitoring_dashboard(self,name,description,request,expected_result):
        """查询agent_status_monitoring_dashboard"""
        print(description)
        request["json_data"]["caseId"] = TestDeskCsDashboard.id
        resp = DeskCsDashboard().agent_status_monitoring_dashboard(request["json_data"])
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] == expected_result["result"]

if __name__ == '__main__':
   weeeTest.main(base_url='https://api.tb1.sayweee.net')
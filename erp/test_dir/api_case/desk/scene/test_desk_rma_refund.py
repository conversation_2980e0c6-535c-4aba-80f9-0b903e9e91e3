import datetime
import time

import weeeTest
from erp.test_dir.api.desk.rma.desk_rma_refund import DeskRmaRefund
from erp.test_dir.db_utils import DBConnect
from erp.test_dir.api_case import desk_header_huadan

class TestDeskRmaRefund(weeeTest.TestCase):
    productId = ''
    orderProductId = ''
    id = ''
    product = None
    order_id = 42651654
    """DESK发起售后申请approve场景"""

    @weeeTest.params.file(file_name="desk_rma_refund.yaml", key="desk_get_valid_product_info")
    @weeeTest.mark.list('Regression', 'CS')
    def test_desk_get_valid_product_info(self, name, description, request, expected_result):
        """获取有效产品信息"""
        print(description)
        resp = DeskRmaRefund().desk_get_valid_product_info(request["json_data"])
        assert resp["message_id"] == "10000"
        assert resp["result"] is True
        assert resp["object"]["orderId"] == 42651654
        assert resp["object"]["orderStatus"] == "Shipped"
        assert resp["object"]["orderType"] == "S-normal-0"
        assert resp["object"]["status"] == "New"
        assert resp["object"]["sellerId"] == 8113
        assert resp["object"]["sellerName"] == "乡味坊"
        assert resp["object"]["source"] == "desk"
        assert 'userId' in resp['object'].keys()
        assert 'username' in resp['object'].keys()
        valid_product_list = [i for i in resp["object"]["productList"] if i["statusType"] == 1]
        TestDeskRmaRefund.product = valid_product_list[0]
        TestDeskRmaRefund.productId = valid_product_list[0]["productId"]
        TestDeskRmaRefund.orderProductId = valid_product_list[0]["orderProductId"]

    @weeeTest.params.file(file_name="desk_rma_refund.yaml", key="desk_rma_create_request")
    @weeeTest.mark.list('Regression', 'CS')
    def test_desk_rma_create_request(self,name, description, request, expected_result):
        """【101922】 desk创建request"""
        request['json_data']['orderId'] = str(TestDeskRmaRefund.order_id)
        request['json_data']['productList'][0] = TestDeskRmaRefund.product
        data = request['json_data']
        resp = DeskRmaRefund().desk_rma_create_request(data)
        assert resp["message_id"] == "10000"
        assert resp["result"] is True



    @weeeTest.params.file(file_name="desk_rma_refund.yaml", key="desk_rma_refund_do_action")
    @weeeTest.mark.list('Regression','CS')
    def test_desk_rma_refund_do_action(self, name, description, request, expected_result):
        """【101917】审批 refund"""
        sql = "SELECT * FROM `weee_cs`.`cs_rma_refund` WHERE `user_id` = '7616787' ORDER BY `id` DESC LIMIT 1;"
        db_res = DBConnect().select_data_from_mysql(sql)[0][0]
        request['json_data']['id'] = db_res
        data = request['json_data']
        resp = DeskRmaRefund().desk_rma_refund_do_action(data)
        TestDeskRmaRefund.id = db_res
        assert resp["message_id"] == "10000"
        assert resp["result"] is True

    @weeeTest.params.file(file_name="desk_rma_refund.yaml", key="desk_rma_get_refund_detail")
    @weeeTest.mark.list('Regression', 'CS')
    def test_desk_rma_get_refund_detail(self, name, description, request, expected_result):
        """【101929】获取refund detail"""
        sql = "SELECT * FROM `weee_cs`.`cs_rma_refund` WHERE `user_id` = '7616787' ORDER BY `id` DESC LIMIT 1;"
        db_res = DBConnect().select_data_from_mysql(sql)[0][0]
        request['json_data']['id'] = db_res
        time.sleep(15)
        resp = DeskRmaRefund().desk_rma_get_refund_detail(request['json_data'])
        time.sleep(15)
        assert resp["message_id"] == "10000"
        assert resp["result"] is True
        assert resp["object"]["orderId"] == 42651654
        assert resp["object"]["orderType"] == "S-normal-0"
        assert resp["object"]["comments"][0]["comment"] == "qa测试-Product quality"
        assert resp["object"]["userId"] == 7616787
        assert resp["object"]["username"] == "huadan"
        assert resp["object"]["approveId"] == 10060559
        assert resp["object"]["approveName"] == "qifang.ye"


    @weeeTest.params.file(file_name="desk_rma_refund.yaml", key="desk_rma_refund_find_list")
    @weeeTest.mark.list('Regression', 'CS')
    def test_desk_rma_refund_find_list(self, name, description, request, expected_result):
        """【101937】查询refund list"""
        print(description)
        time.sleep(30)
        data = request['json_data']
        data['applyTimeRange'] = [(datetime.datetime.now() + datetime.timedelta(days=-90)).strftime("%Y-%m-%d"), str(datetime.date.today())]
        resp = DeskRmaRefund().desk_rma_refund_find_list(data)
        refund = [item for item in resp['object']['data'] if item['id'] == TestDeskRmaRefund.id]
        assert resp["message_id"] == expected_result["message_id"]
        assert resp["result"] is True
        assert refund[0]['status'] == 'B'
        sql = fr"SELECT status FROM `weee_cs`.`cs_rma_refund` WHERE id={TestDeskRmaRefund.id}"
        db_res = DBConnect().select_data_from_mysql(sql)
        assert db_res[0][0] == refund[0]['status'] == 'B'


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net')
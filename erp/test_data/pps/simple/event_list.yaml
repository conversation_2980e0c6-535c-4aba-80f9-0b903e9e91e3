update_event_success:
  - request:
      # 更新时间
      url: "/central/promotion_planning/v2/events/202503_Futur_1"
      json_data: {
        "title": 'future_event_update',
        "start_date": '2025-05-17',
        "end_date": '2025-06-03',
        "storefronts": [ "store_ph", "lang_vi" ],
        "owner_email": "<EMAIL>",
        "description": 'for update use1'
      }
  - request:
      # 更新title
      url: '/central/promotion_planning/v2/events/202503_Futur_1'
      json_data: {
        "title": 'future_event_update_test',
        "start_date": '2025-05-17',
        "end_date": '2025-06-02',
        "storefronts": [ "store_ph", "lang_vi" ],
        "owner_email": "<EMAIL>",
        "description": 'for update use2'
      }
  - request:
      # 数据还原
      url: '/central/promotion_planning/v2/events/202503_Futur_1'
      json_data: {
        "title": 'future_event_update',
        "start_date": '2025-05-17',
        "end_date": '2025-06-01',
        "storefronts": [ "store_ph", "lang_vi" ],
        "owner_email": "<EMAIL>",
        "description": 'for update use3'
      }

update_event_fail:
  - request:
      # 更新时间
      url: "/central/promotion_planning/v2/events/202404_0417_1"
      json_data: {
        "title": '0617',
        "start_date": '2025-05-17',
        "end_date": '2025-06-03',
        "storefronts": [ "store_ph", "lang_vi" ],
        "owner_email": "<EMAIL>",
        "description": 'for update use1'
      }
      message: "Event title cannot be blank and should contain at least one alphabet"
  - request:
      # 更新时间
      url: "/central/promotion_planning/v2/events/202401_Sudee_1"
      json_data: {
        "title": 'for update use2',
        "start_date": '2025-05-17',
        "end_date": '2025-06-03',
        "storefronts": [ "store_ph", "lang_vi" ],
        "owner_email": "<EMAIL>",
        "description": 'for update use2'
      }
      message: "Event has already ended, hence cannot be modified"
  - request:
      # 更新时间
      url: "/central/promotion_planning/v2/events/202401_Sudee_1"
      json_data: {
        "title": 'for update use3',
        "start_date": '2024-01-01',
        "end_date": '2025-06-03',
        "storefronts": [ "store_ph", "lang_vi" ],
        "owner_email": "<EMAIL>",
        "description": 'for update use3'
      }
      message: "Event Start Date cannot be set to today or past"

link_event_success:
  - request:
      # 更新时间
      json_data: {
        "eventCode": ["202406_Linkt_1"],
        "eventGroupId": 1434,
        "promotionDtos": [{"pepIds":[8739739],"priceSpecialIds":[7762797]},{"pepIds":[],"priceSpecialIds":[7748560]},{"pepIds":[],"priceSpecialIds":[7740318]}]
      }
      message: "SKU : 105645, SO : 1, Start : 2024-09-28, End : 2024-12-27 - Successfully Linked to 202406_Linkt_1##SKU : 77683, SO : 2, Start : 2024-05-21, End : 2024-05-21 - Successfully Linked to 202406_Linkt_1##SKU : 36497, SO : 2, Start : 2024-04-29, End : 2038-01-18 - Successfully Linked to 202406_Linkt_1"

link_event_fail:
  - request:
      json_data: {
        "eventCode": ["202406_Notif_1"],
        "eventGroupId": 1409,
        "promotionDtos": [{"pepIds":[8738764,8738905,8738763],"priceSpecialIds":[7738666]}]
      }
      message: "SKU : 11700, SO : 1, Start : 2024-04-20, End : 2024-07-19 - Max Order Quantity should be less than or equal to 2 to link to Crazy 8 Event 202406_Notif_1"
  - request:
      json_data: {
        "eventCode": ["202403_Event_1"],
        "eventGroupId": 1338,
        "promotionDtos": [{"pepIds":[8738993,8738995],"priceSpecialIds":[7748154]}]
      }
      message: "SKU : 875, SO : 3, Start : 2024-06-05, End : 2024-07-20 - Link already exists in 202403_Event_1"
  - request:
      json_data: {
        "eventCode": [ "202410_Testc_1" ],
        "eventGroupId": 1699,
        "promotionDtos": [ { "pepIds": [],"priceSpecialIds": [ 7821006 ] } ]
      }
      message: "SKU : 2121651, SO : 2, Start : 2025-02-08, End : 2025-02-28 - The maximum number of promotion price records allowed for this Crazy 8 Event has been reached."

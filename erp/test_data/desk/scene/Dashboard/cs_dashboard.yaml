get_my_cs_agent_dashboard:
  - name: 查询MY Dashboard接口
    description: 默认查询
    request:
      method: get
      url: "/cs/desk/dashboard/my/csAgentDashboard"
      json_data: {"id":"","language":"undefined"}
    expected_result:
      message_id: "10000"
      result: true



get_daily_dashboard:
  - name: 查询daily dashboard接口
    description: 默认查询
    request:
      method: get
      url: "/cs/desk/dashboard/daily/digital"
      json_data: {"id":"","language":"undefined"}
    expected_result:
      message_id: "10000"
      result: true


agent_status_monitoring_dashboard:
  - name: 查询Agent_status_Monitoring_Dashboard接口
    description: Group为All
    request:
      method: post
      url: "/cs/desk/dashboard/statusMonitoring"
      json_data: {
        "orderColumn": "status",
        "orderRule": "asc"

      }
    expected_result:
      message_id: "10000"
      result: true
